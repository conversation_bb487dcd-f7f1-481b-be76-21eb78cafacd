#!/usr/bin/env python3
"""
LaTeX2PNG 快速演示
展示基本的LaTeX到PNG转换功能
"""

import matplotlib
import matplotlib.pyplot as plt
import os
import time
from pathlib import Path

# 配置matplotlib使用内置mathtext（不需要外部LaTeX）
matplotlib.rcParams['text.usetex'] = False
matplotlib.rcParams['mathtext.fontset'] = 'cm'  # Computer Modern字体
matplotlib.rcParams['font.size'] = 14

def render_latex_to_png(latex_code, output_path, dpi=300):
    """
    将LaTeX代码渲染为PNG图像
    
    Args:
        latex_code: LaTeX数学公式代码
        output_path: 输出PNG文件路径
        dpi: 图像分辨率
    
    Returns:
        bool: 是否成功
    """
    try:
        # 创建图形
        fig, ax = plt.subplots(figsize=(8, 2))
        
        # 渲染LaTeX文本
        ax.text(0.5, 0.5, latex_code,
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes,
               fontsize=16)
        
        # 移除坐标轴
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        
        # 保存图像
        plt.savefig(
            output_path,
            dpi=dpi,
            bbox_inches='tight',
            pad_inches=0.1,
            transparent=True,
            facecolor='none',
            format='png'
        )
        plt.close(fig)
        
        return True
        
    except Exception as e:
        print(f"渲染失败: {e}")
        plt.close('all')  # 确保清理
        return False

def batch_process_latex(latex_list, output_dir="./demo_output"):
    """
    批量处理LaTeX公式列表
    
    Args:
        latex_list: LaTeX公式列表
        output_dir: 输出目录
    
    Returns:
        dict: 处理结果统计
    """
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    results = {
        'total': len(latex_list),
        'success': 0,
        'failed': 0,
        'files': [],
        'errors': []
    }
    
    print(f"开始批量处理 {len(latex_list)} 个LaTeX公式...")
    print(f"输出目录: {output_path.absolute()}")
    
    start_time = time.time()
    
    for i, latex_code in enumerate(latex_list):
        # 生成文件名
        filename = f"formula_{i+1:04d}.png"
        file_path = output_path / filename
        
        print(f"[{i+1}/{len(latex_list)}] 处理: {latex_code[:50]}...")
        
        # 渲染
        success = render_latex_to_png(latex_code, str(file_path))
        
        if success:
            results['success'] += 1
            results['files'].append(str(file_path))
            print(f"  ✓ 成功: {filename}")
        else:
            results['failed'] += 1
            results['errors'].append(f"公式 {i+1}: {latex_code}")
            print(f"  ✗ 失败: {latex_code}")
    
    elapsed_time = time.time() - start_time
    
    # 生成映射文件
    mapping = {}
    for i, latex_code in enumerate(latex_list):
        if i < results['success']:
            filename = f"formula_{i+1:04d}"
            mapping[filename] = latex_code
    
    # 保存映射文件
    import json
    mapping_file = output_path / "mapping.json"
    with open(mapping_file, 'w', encoding='utf-8') as f:
        json.dump(mapping, f, ensure_ascii=False, indent=2)
    
    # 生成统计报告
    report = f"""
LaTeX2PNG 处理报告
==================

处理统计:
- 总数: {results['total']}
- 成功: {results['success']}
- 失败: {results['failed']}
- 成功率: {results['success']/results['total']*100:.1f}%

性能指标:
- 总耗时: {elapsed_time:.2f} 秒
- 平均速度: {results['total']/elapsed_time:.1f} 公式/秒

输出文件:
- 图像目录: {output_path}/
- 映射文件: {mapping_file}
- 成功生成: {results['success']} 个PNG文件
"""
    
    # 保存报告
    report_file = output_path / "processing_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(report)
    
    if results['failed'] > 0:
        print("失败的公式:")
        for error in results['errors']:
            print(f"  - {error}")
    
    return results

def main():
    """主演示函数"""
    print("LaTeX2PNG 转换器演示")
    print("=" * 50)
    
    # 示例LaTeX公式
    sample_formulas = [
        r"$x^2 + y^2 = z^2$",
        r"$E = mc^2$",
        r"$\frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$",
        r"$\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}$",
        r"$\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}$",
        r"$\lim_{x \to 0} \frac{\sin x}{x} = 1$",
        r"$\nabla \cdot \vec{E} = \frac{\rho}{\epsilon_0}$",
        r"$\begin{pmatrix} a & b \\ c & d \end{pmatrix}$",
        r"$\alpha + \beta = \gamma$",
        r"$\mathbb{E}[X] = \sum_{i} x_i P(X = x_i)$"
    ]
    
    print(f"将处理 {len(sample_formulas)} 个示例公式")
    
    # 执行批量处理
    results = batch_process_latex(sample_formulas, "./demo_output")
    
    print("\n" + "=" * 50)
    if results['success'] == results['total']:
        print("🎉 演示完成！所有公式都成功转换")
        print("\n查看结果:")
        print("1. 打开 ./demo_output/ 目录查看PNG图像")
        print("2. 查看 mapping.json 了解文件名映射")
        print("3. 查看 processing_report.txt 了解详细统计")
    else:
        print(f"⚠ 演示完成，{results['success']}/{results['total']} 个公式成功转换")
    
    print(f"\n输出目录: {Path('./demo_output').absolute()}")

if __name__ == "__main__":
    main()
