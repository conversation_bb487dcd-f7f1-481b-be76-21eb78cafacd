#!/usr/bin/env python3
"""
LaTeX预处理器
处理matplotlib不兼容的LaTeX环境和命令
"""

import re
from typing import <PERSON><PERSON>

def preprocess_latex(latex_code: str) -> Tuple[str, bool]:
    """
    预处理LaTeX代码，修复matplotlib兼容性问题
    按照LLD设计的Level1-Level3分层处理

    Args:
        latex_code: 原始LaTeX代码

    Returns:
        (processed_code, was_modified): 处理后的代码和是否被修改的标志
    """
    original_code = latex_code
    modified = False

    # Level1: 基础语法修复
    latex_code, level1_modified = _level1_basic_fixes(latex_code)
    modified = modified or level1_modified

    # Level2: 结构标准化
    latex_code, level2_modified = _level2_structure_fixes(latex_code)
    modified = modified or level2_modified

    # Level3: 语义智能修复
    latex_code, level3_modified = _level3_semantic_fixes(latex_code)
    modified = modified or level3_modified

    return latex_code, modified

def _level1_basic_fixes(latex_code: str) -> <PERSON><PERSON>[str, bool]:
    """Level1: 基础语法修复"""
    original_code = latex_code
    modified = False

    # 1.1 移除外层数学模式标记（避免双重包围）
    if latex_code.startswith('$') and latex_code.endswith('$'):
        latex_code = latex_code[1:-1]
        modified = True

    # 1.2 处理SI单位包命令 -> 简化为文本
    si_patterns = [
        (r'\\SI\{([^}]+)\}\{([^}]+)\}', r'\1\,\text{\2}'),  # \SI{value}{unit}
        (r'\\si\{([^}]+)\}', r'\text{\1}'),                 # \si{unit}
        (r'(\d+)\\,\\si\{([^}]+)\}', r'\1\,\text{\2}'),     # number\,\si{unit}
    ]

    for pattern, replacement in si_patterns:
        old_latex = latex_code
        latex_code = re.sub(pattern, replacement, latex_code)
        if old_latex != latex_code:
            modified = True

    # 1.3 处理颜色命令 -> 移除颜色保留内容
    color_patterns = [
        (r'\\color\{[^}]+\}\{([^}]+)\}', r'\1'),  # \color{red}{text}
        (r'\{\\color\{[^}]+\}([^}]+)\}', r'\1'),  # {\color{red}text}
    ]

    for pattern, replacement in color_patterns:
        old_latex = latex_code
        latex_code = re.sub(pattern, replacement, latex_code)
        if old_latex != latex_code:
            modified = True

    return latex_code, modified

def _level2_structure_fixes(latex_code: str) -> Tuple[str, bool]:
    """Level2: 结构标准化"""
    original_code = latex_code
    modified = False

    # 2.1 处理复杂数学环境 -> 简化为单行公式
    complex_environments = [
        'eqnarray', 'align', 'split', 'gather', 'multline'
    ]

    for env in complex_environments:
        pattern = rf'\\begin\{{{env}\*?\}}(.*?)\\end\{{{env}\*?\}}'
        match = re.search(pattern, latex_code, re.DOTALL)
        if match:
            content = match.group(1).strip()
            # 简化处理：移除换行和对齐符号
            content = re.sub(r'\\\\', ' ', content)  # 移除换行
            content = re.sub(r'&=&', '=', content)   # 简化对齐
            content = re.sub(r'&\+&', '+', content)  # 简化对齐
            content = re.sub(r'&([^&]*)&', r'\1', content)  # 移除其他对齐符号
            content = re.sub(r'&', '', content)     # 移除剩余对齐符号
            content = re.sub(r'\s+', ' ', content).strip()  # 清理空格

            # 替换整个环境
            latex_code = latex_code.replace(match.group(0), content)
            modified = True

    # 2.2 处理cases环境 -> 简化为文本描述
    cases_pattern = r'\\begin\{cases\}(.*?)\\end\{cases\}'
    match = re.search(cases_pattern, latex_code, re.DOTALL)
    if match:
        # 对于复杂的cases，简化为文本描述
        if len(match.group(0)) > 200:
            latex_code = latex_code.replace(match.group(0), r'\text{piecewise function}')
            modified = True
        else:
            # 简单的cases保留但简化
            content = match.group(1).strip()
            content = re.sub(r'\\\\', r' \text{ or } ', content)  # 换行改为"or"
            content = re.sub(r'&', '', content)  # 移除对齐符号
            latex_code = latex_code.replace(match.group(0), f'\\text{{{content}}}')
            modified = True

    # 2.3 处理复杂矩阵 -> 简化处理
    matrix_environments = ['bmatrix', 'pmatrix', 'vmatrix', 'Bmatrix', 'Vmatrix']
    for env in matrix_environments:
        pattern = rf'\\begin\{{{env}\}}(.*?)\\end\{{{env}\}}'
        match = re.search(pattern, latex_code, re.DOTALL)
        if match and len(match.group(0)) > 300:  # 只处理复杂矩阵
            latex_code = latex_code.replace(match.group(0), r'\text{matrix}')
            modified = True

    return latex_code, modified

def _level3_semantic_fixes(latex_code: str) -> Tuple[str, bool]:
    """Level3: 语义智能修复"""
    original_code = latex_code
    modified = False

    # 3.1 处理自定义命令 -> 移除或替换为标准命令
    custom_commands = [
        # 常见的自定义命令模式
        (r'\\[A-Z][a-zA-Z]*\{([^}]+)\}', r'\1'),  # 大写开头的命令
        (r'\\my[a-zA-Z]+\{([^}]+)\}', r'\1'),     # my开头的命令
        (r'\\custom[a-zA-Z]*\{([^}]+)\}', r'\1'), # custom开头的命令
    ]

    for pattern, replacement in custom_commands:
        old_latex = latex_code
        latex_code = re.sub(pattern, replacement, latex_code)
        if old_latex != latex_code:
            modified = True

    # 3.2 处理度数符号
    degree_patterns = [
        (r'(\\theta\s*=\s*\d+)\s*\\degree', r'\1^\\circ'),  # \theta=90\degree
        (r'(\d+)\s*\\degree', r'\1^\\circ'),                # 90\degree
    ]

    for pattern, replacement in degree_patterns:
        old_latex = latex_code
        latex_code = re.sub(pattern, replacement, latex_code)
        if old_latex != latex_code:
            modified = True

    # 3.3 处理过长的公式 -> 截断或简化
    if len(latex_code) > 500:
        # 对于极长的公式，提供简化版本
        latex_code = r'\text{Complex formula (simplified for rendering)}'
        modified = True

    # 3.4 最终清理
    latex_code = re.sub(r'\s+', ' ', latex_code).strip()  # 标准化空格

    return latex_code, modified
    
    # 3. 处理过长的公式（分行处理）
    if len(latex_code) > 200 and '\\\\' in latex_code:
        # 对于很长的多行公式，确保使用align环境
        if not ('\\begin{align' in latex_code or '\\begin{gather' in latex_code):
            # 包装在align环境中
            latex_code = f'\\begin{{align}}\n{latex_code}\n\\end{{align}}'
            modified = True
    
    # 4. 处理特殊字符问题
    # 移除可能导致问题的额外空格和换行
    latex_code = re.sub(r'\s+', ' ', latex_code).strip()
    
    # 5. 处理嵌套的$符号问题
    # 如果代码中已经有$，不要再添加外层$
    if latex_code.startswith('$') and latex_code.endswith('$'):
        # 已经有$包围，移除以避免双重包围
        latex_code = latex_code[1:-1]
        modified = True
    
    return latex_code, modified

def validate_latex_syntax(latex_code: str) -> Tuple[bool, str]:
    """
    验证LaTeX语法的基本正确性
    
    Args:
        latex_code: LaTeX代码
        
    Returns:
        (is_valid, error_message): 是否有效和错误信息
    """
    # 检查括号匹配
    brackets = {'\\{': '\\}', '\\(': '\\)', '\\[': '\\]'}
    stack = []
    
    # 简单的括号匹配检查
    for i, char in enumerate(latex_code):
        if i < len(latex_code) - 1:
            two_char = latex_code[i:i+2]
            if two_char in brackets:
                stack.append(brackets[two_char])
            elif two_char in brackets.values():
                if not stack or stack[-1] != two_char:
                    return False, f"括号不匹配: {two_char}"
                stack.pop()
    
    if stack:
        return False, f"未闭合的括号: {stack}"
    
    # 检查环境匹配
    begin_pattern = r'\\begin\{([^}]+)\}'
    end_pattern = r'\\end\{([^}]+)\}'
    
    begins = re.findall(begin_pattern, latex_code)
    ends = re.findall(end_pattern, latex_code)
    
    if len(begins) != len(ends):
        return False, f"环境不匹配: {len(begins)} begin vs {len(ends)} end"
    
    for begin_env, end_env in zip(begins, ends):
        if begin_env != end_env:
            return False, f"环境不匹配: \\begin{{{begin_env}}} vs \\end{{{end_env}}}"
    
    return True, ""

def get_latex_complexity_score(latex_code: str) -> int:
    """
    评估LaTeX代码的复杂度
    
    Args:
        latex_code: LaTeX代码
        
    Returns:
        复杂度分数（0-100）
    """
    score = 0
    
    # 基础分数
    score += len(latex_code) // 10
    
    # 环境复杂度
    environments = ['eqnarray', 'align', 'gather', 'split', 'multline', 'cases']
    for env in environments:
        if f'\\begin{{{env}}}' in latex_code:
            score += 20
    
    # 命令复杂度
    complex_commands = ['\\frac', '\\sum', '\\int', '\\prod', '\\bm', '\\mathcal', '\\mathbb']
    for cmd in complex_commands:
        score += latex_code.count(cmd) * 5
    
    # 嵌套复杂度
    score += latex_code.count('{') * 2
    score += latex_code.count('\\') * 1
    
    return min(score, 100)

def suggest_alternatives(latex_code: str) -> list:
    """
    为有问题的LaTeX代码建议替代方案
    
    Args:
        latex_code: LaTeX代码
        
    Returns:
        替代方案列表
    """
    suggestions = []
    
    if '\\begin{eqnarray}' in latex_code:
        # 提供align环境的替代
        alt_code = latex_code.replace('\\begin{eqnarray}', '\\begin{align}')
        alt_code = alt_code.replace('\\end{eqnarray}', '\\end{align}')
        alt_code = re.sub(r'&=&', '&=', alt_code)
        suggestions.append(("使用align环境替代eqnarray", alt_code))
    
    if len(latex_code) > 150:
        # 建议简化长公式
        suggestions.append(("建议简化公式", "考虑将长公式分解为多个较短的公式"))
    
    complexity = get_latex_complexity_score(latex_code)
    if complexity > 50:
        suggestions.append(("降低复杂度", f"当前复杂度: {complexity}/100，建议简化"))
    
    return suggestions

def main():
    """测试预处理器"""
    test_cases = [
        r"\begin{eqnarray}J^{(i)} &=& \left\| \hat{\bm{u}}(0) - \tilde{\bm{u}}^{(i)}(0) \right\|_{\mathcal{B}}^2  \\&+& \sum_{t=0}^{T_a} \left\| \bm{z}(t) - \bm{H}_{\mathbb{C}} \tilde{\bm{u}}^{(i)}(t) - \bm{v}(t) \right\|_{\bm{R}_\mathbb{C}}^2\,,\end{eqnarray}",
        r"x^2 + y^2 = z^2",
        r"$\frac{1}{2}\pi r^2$",
    ]
    
    print("LaTeX预处理器测试")
    print("=" * 50)
    
    for i, test_code in enumerate(test_cases, 1):
        print(f"\n测试 {i}:")
        print(f"原始: {test_code[:50]}...")
        
        # 预处理
        processed, modified = preprocess_latex(test_code)
        print(f"修改: {'是' if modified else '否'}")
        if modified:
            print(f"处理后: {processed[:50]}...")
        
        # 验证
        valid, error = validate_latex_syntax(processed)
        print(f"语法: {'有效' if valid else '无效'}")
        if not valid:
            print(f"错误: {error}")
        
        # 复杂度
        complexity = get_latex_complexity_score(processed)
        print(f"复杂度: {complexity}/100")
        
        # 建议
        suggestions = suggest_alternatives(test_code)
        if suggestions:
            print("建议:")
            for desc, alt in suggestions:
                print(f"  - {desc}")

if __name__ == "__main__":
    main()
