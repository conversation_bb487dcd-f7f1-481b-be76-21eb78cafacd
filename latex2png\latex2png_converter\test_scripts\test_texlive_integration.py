#!/usr/bin/env python3
"""
测试TeX Live集成
"""

import sys
import os
from pathlib import Path
import tempfile

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_texlive_engine():
    """测试TeX Live引擎"""
    print("测试TeX Live引擎集成...")
    
    try:
        from config.render_config import RenderConfig
        from engines.texlive_engine import TeXLiveEngine
        from core import LaTeXItem
        
        # 创建配置
        config = RenderConfig()
        print(f"  ✓ 配置创建: DPI={config.dpi}")
        
        # 创建引擎
        engine = TeXLiveEngine(config)
        print("  ✓ TeX Live引擎创建成功")
        
        # 检查可用性
        is_available = engine.is_available()
        print(f"  ✓ 引擎可用性: {is_available}")
        
        if not is_available:
            print("  ✗ TeX Live引擎不可用")
            return False
        
        # 初始化引擎
        success = engine.initialize()
        print(f"  ✓ 引擎初始化: {success}")
        
        if not success:
            print("  ✗ TeX Live引擎初始化失败")
            return False
        
        # 创建测试LaTeX项目
        test_item = LaTeXItem(
            index=1,
            original_latex=r"\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}",
            normalized_latex=r"\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}",
            category="test"
        )
        
        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 设置输出目录
            config.output_dir = temp_dir
            
            # 渲染测试
            print("  正在测试渲染...")
            image_path = engine.render(test_item)
            
            # 验证结果
            if Path(image_path).exists() and Path(image_path).stat().st_size > 0:
                print(f"  ✓ 渲染成功: {image_path}")
                print(f"  ✓ 文件大小: {Path(image_path).stat().st_size} bytes")
                return True
            else:
                print("  ✗ 渲染失败：文件未生成或为空")
                return False
        
    except Exception as e:
        print(f"  ✗ TeX Live引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complex_latex():
    """测试复杂LaTeX公式"""
    print("\n测试复杂LaTeX公式...")
    
    try:
        from config.render_config import RenderConfig
        from engines.texlive_engine import TeXLiveEngine
        from core import LaTeXItem
        
        config = RenderConfig()
        engine = TeXLiveEngine(config)
        
        if not engine.is_available() or not engine.initialize():
            print("  ✗ 引擎不可用")
            return False
        
        # 复杂公式测试
        complex_formulas = [
            r"\begin{bmatrix} a & b \\ c & d \end{bmatrix}",
            r"\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}",
            r"\oint_C \vec{F} \cdot d\vec{r} = \iint_S (\nabla \times \vec{F}) \cdot d\vec{S}",
            r"\mathcal{L}\{f(t)\} = \int_0^{\infty} f(t) e^{-st} dt"
        ]
        
        success_count = 0
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config.output_dir = temp_dir
            
            for i, formula in enumerate(complex_formulas):
                test_item = LaTeXItem(
                    index=i+1,
                    original_latex=formula,
                    normalized_latex=formula,
                    category="complex"
                )
                
                try:
                    image_path = engine.render(test_item)
                    if Path(image_path).exists() and Path(image_path).stat().st_size > 0:
                        print(f"  ✓ 复杂公式 {i+1}: 成功")
                        success_count += 1
                    else:
                        print(f"  ✗ 复杂公式 {i+1}: 失败")
                except Exception as e:
                    print(f"  ✗ 复杂公式 {i+1}: 异常 - {e}")
        
        print(f"  复杂公式测试结果: {success_count}/{len(complex_formulas)} 成功")
        return success_count == len(complex_formulas)
        
    except Exception as e:
        print(f"  ✗ 复杂LaTeX测试失败: {e}")
        return False

def test_performance():
    """测试性能"""
    print("\n测试渲染性能...")
    
    try:
        from config.render_config import RenderConfig
        from engines.texlive_engine import TeXLiveEngine
        from core import LaTeXItem
        import time
        
        config = RenderConfig()
        engine = TeXLiveEngine(config)
        
        if not engine.is_available() or not engine.initialize():
            print("  ✗ 引擎不可用")
            return False
        
        # 性能测试公式
        test_formulas = [
            r"x^2 + y^2 = z^2",
            r"\frac{a}{b} + \frac{c}{d}",
            r"\int_0^1 x dx",
            r"\sum_{i=1}^n i",
            r"e^{i\pi} + 1 = 0"
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            config.output_dir = temp_dir
            
            start_time = time.time()
            success_count = 0
            
            for i, formula in enumerate(test_formulas):
                test_item = LaTeXItem(
                    index=i+1,
                    original_latex=formula,
                    normalized_latex=formula,
                    category="perf"
                )
                
                try:
                    item_start = time.time()
                    image_path = engine.render(test_item)
                    item_time = time.time() - item_start
                    
                    if Path(image_path).exists() and Path(image_path).stat().st_size > 0:
                        print(f"  ✓ 公式 {i+1}: {item_time:.3f}秒")
                        success_count += 1
                    else:
                        print(f"  ✗ 公式 {i+1}: 渲染失败")
                except Exception as e:
                    print(f"  ✗ 公式 {i+1}: 异常 - {e}")
            
            total_time = time.time() - start_time
            
            if success_count > 0:
                avg_time = total_time / success_count
                rate = success_count / total_time
                print(f"  性能统计:")
                print(f"    - 成功数: {success_count}/{len(test_formulas)}")
                print(f"    - 总耗时: {total_time:.3f}秒")
                print(f"    - 平均时间: {avg_time:.3f}秒/公式")
                print(f"    - 处理速率: {rate:.1f}公式/秒")
                print(f"    - 预估速率: {rate*60:.0f}公式/分钟")
                
                return success_count == len(test_formulas)
            else:
                print("  ✗ 所有公式都失败")
                return False
        
    except Exception as e:
        print(f"  ✗ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("TeX Live集成测试")
    print("=" * 40)
    
    tests = [
        ("基本引擎功能", test_texlive_engine),
        ("复杂LaTeX公式", test_complex_latex),
        ("渲染性能", test_performance),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 40)
    print(f"TeX Live集成测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 TeX Live完全集成成功！")
        print("\n现在您可以:")
        print("1. 处理复杂的LaTeX公式（包括矩阵、积分等）")
        print("2. 获得高质量的渲染结果")
        print("3. 使用完整的LaTeX功能")
        return 0
    else:
        print("❌ TeX Live集成存在问题")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
