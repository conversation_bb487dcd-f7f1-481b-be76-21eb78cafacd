# TeX Live查找脚本

Write-Host "TeX Live安装查找" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green

# 1. 检查PATH中的LaTeX命令
Write-Host "`n1. 检查PATH中的LaTeX命令:" -ForegroundColor Yellow
$latexCommands = @("latex", "pdflatex", "xelatex", "lualatex", "tlmgr")

foreach ($cmd in $latexCommands) {
    $cmdPath = Get-Command $cmd -ErrorAction SilentlyContinue
    if ($cmdPath) {
        Write-Host "   ✓ $cmd`: $($cmdPath.Source)" -ForegroundColor Green
    } else {
        Write-Host "   ✗ $cmd`: 未找到" -ForegroundColor Red
    }
}

# 2. 搜索常见安装位置
Write-Host "`n2. 搜索常见TeX Live安装位置:" -ForegroundColor Yellow
$commonPaths = @(
    "C:\texlive",
    "D:\texlive", 
    "C:\Program Files\texlive",
    "D:\Program Files\texlive",
    "C:\TeX Live",
    "D:\TeX Live",
    "$env:USERPROFILE\texlive",
    "C:\texlive\2024",
    "C:\texlive\2023",
    "D:\texlive\2024",
    "D:\texlive\2023"
)

$foundPaths = @()
foreach ($path in $commonPaths) {
    if (Test-Path $path) {
        Write-Host "   ✓ 找到: $path" -ForegroundColor Green
        $foundPaths += $path
        
        # 检查bin目录
        $binDirs = @(
            Join-Path $path "bin\windows",
            Join-Path $path "bin\win32", 
            Join-Path $path "bin\x86_64-w64-mingw32"
        )
        
        foreach ($binDir in $binDirs) {
            if (Test-Path $binDir) {
                Write-Host "     - bin目录: $binDir" -ForegroundColor Cyan
                
                # 检查关键可执行文件
                $exeFiles = @("latex.exe", "pdflatex.exe", "tlmgr.exe")
                foreach ($exe in $exeFiles) {
                    $exePath = Join-Path $binDir $exe
                    if (Test-Path $exePath) {
                        Write-Host "       ✓ $exe" -ForegroundColor Green
                    }
                }
            }
        }
    } else {
        Write-Host "   ✗ 未找到: $path" -ForegroundColor Gray
    }
}

# 3. 全盘搜索（如果常见位置没找到）
if ($foundPaths.Count -eq 0) {
    Write-Host "`n3. 执行全盘搜索（这可能需要几分钟）:" -ForegroundColor Yellow
    Write-Host "   正在搜索包含'texlive'的目录..." -ForegroundColor Cyan
    
    $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 } | Select-Object -ExpandProperty DeviceID
    
    foreach ($drive in $drives) {
        Write-Host "   搜索驱动器 $drive..." -ForegroundColor Cyan
        $texliveDirs = Get-ChildItem -Path "$drive\" -Directory -Recurse -Name "*texlive*" -ErrorAction SilentlyContinue | Select-Object -First 5

        foreach ($dir in $texliveDirs) {
            $fullPath = Join-Path "$drive\" $dir
            Write-Host "     找到: $fullPath" -ForegroundColor Green
            $foundPaths += $fullPath
        }
    }
}

# 4. 检查注册表
Write-Host "`n4. 检查Windows注册表:" -ForegroundColor Yellow
$regPaths = @(
    "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
    "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*"
)

foreach ($regPath in $regPaths) {
    $texPrograms = Get-ItemProperty $regPath -ErrorAction SilentlyContinue |
                  Where-Object { $_.DisplayName -like "*TeX Live*" -or $_.DisplayName -like "*TeXLive*" }

    foreach ($program in $texPrograms) {
        Write-Host "   ✓ 注册表中找到: $($program.DisplayName)" -ForegroundColor Green
        if ($program.InstallLocation) {
            Write-Host "     安装位置: $($program.InstallLocation)" -ForegroundColor Cyan
            $foundPaths += $program.InstallLocation
        }
    }
}

# 5. 总结和建议
Write-Host "`n5. 总结:" -ForegroundColor Yellow
if ($foundPaths.Count -gt 0) {
    Write-Host "   找到 $($foundPaths.Count) 个可能的TeX Live安装:" -ForegroundColor Green
    $foundPaths | Sort-Object -Unique | ForEach-Object {
        Write-Host "   - $_" -ForegroundColor Green
    }
    
    Write-Host "`n建议操作:" -ForegroundColor Yellow
    Write-Host "1. 选择一个有效的TeX Live安装路径" -ForegroundColor White
    Write-Host "2. 将其bin目录添加到PATH环境变量" -ForegroundColor White
    Write-Host "3. 或者运行: .\setup_texlive_env.ps1 -TexLivePath '选择的路径'" -ForegroundColor White
    
} else {
    Write-Host "   ✗ 未找到TeX Live安装" -ForegroundColor Red
    Write-Host "`n可能的原因:" -ForegroundColor Yellow
    Write-Host "1. TeX Live未正确安装" -ForegroundColor White
    Write-Host "2. 安装在非标准位置" -ForegroundColor White
    Write-Host "3. 安装过程中出现错误" -ForegroundColor White
    
    Write-Host "`n建议操作:" -ForegroundColor Yellow
    Write-Host "1. 重新下载并安装TeX Live: https://www.tug.org/texlive/" -ForegroundColor White
    Write-Host "2. 确保选择完整安装" -ForegroundColor White
    Write-Host "3. 安装时勾选'添加到PATH'" -ForegroundColor White
    Write-Host "4. 或者使用便携版安装: .\install_texlive_portable.ps1" -ForegroundColor White
}

Write-Host "`n查找完成!" -ForegroundColor Green
