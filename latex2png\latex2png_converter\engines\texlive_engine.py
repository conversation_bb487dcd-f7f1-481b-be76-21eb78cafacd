"""
TeX Live渲染引擎
使用TeX Live + matplotlib进行高质量渲染
"""

import matplotlib
import matplotlib.pyplot as plt
from pathlib import Path
import tempfile
import os
import subprocess
from typing import Dict, Any
from .base_engine import RenderEngine
from ..core import LaTeXItem
from ..monitoring.logger import get_logger

class TeXLiveEngine(RenderEngine):
    """TeX Live渲染引擎"""

    def __init__(self, config):
        super().__init__(config)
        self.logger = get_logger(__name__)
        self._matplotlib_configured = False
        self._texlive_available = None

    def _perform_initialization(self):
        """执行TeX Live引擎初始化"""
        self.logger.info("初始化TeX Live引擎...")
        
        # 检查TeX Live可用性
        if not self._check_texlive_installation():
            raise RuntimeError("TeX Live未安装或不可用")
        
        # 配置matplotlib使用LaTeX
        self._configure_matplotlib()
        
        # 测试渲染功能
        if not self.test_render():
            raise RuntimeError("TeX Live引擎测试渲染失败")
        
        self.logger.info("TeX Live引擎初始化成功")

    def _check_texlive_installation(self) -> bool:
        """检查LaTeX安装（支持TeX Live或matplotlib内置）"""
        if self._texlive_available is not None:
            return self._texlive_available

        # 方法1：检查外部LaTeX命令
        try:
            result = subprocess.run(['latex', '--version'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                output = result.stdout.lower()
                if 'tex live' in output or 'texlive' in output:
                    self.logger.info("检测到TeX Live安装")
                    self._texlive_available = True
                    return True
                elif 'miktex' in output:
                    self.logger.warning("检测到MiKTeX，建议使用TeX Live")
                    # 仍然可以使用，但记录警告
                    self._texlive_available = True
                    return True
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
            pass

        # 方法2：使用matplotlib内置LaTeX支持（fallback）
        try:
            self.logger.info("外部LaTeX不可用，尝试使用matplotlib内置LaTeX支持")
            # 测试matplotlib的LaTeX渲染
            import matplotlib
            matplotlib.rcParams['text.usetex'] = False  # 使用内置mathtext
            self.logger.info("将使用matplotlib内置mathtext渲染器")
            self._texlive_available = True  # 标记为可用
            return True
        except Exception as e:
            self.logger.error(f"matplotlib LaTeX支持也不可用: {e}")

        self.logger.error("无可用的LaTeX渲染方式")
        self._texlive_available = False
        return False

    def _configure_matplotlib(self):
        """配置matplotlib LaTeX支持"""
        if self._matplotlib_configured:
            return

        try:
            # 检查是否有外部LaTeX
            has_external_latex = False
            try:
                result = subprocess.run(['latex', '--version'],
                                      capture_output=True, text=True, timeout=5)
                has_external_latex = (result.returncode == 0)
            except:
                pass

            if has_external_latex:
                # 使用外部LaTeX
                self.logger.info("配置matplotlib使用外部LaTeX")
                matplotlib.rcParams['text.usetex'] = True
                matplotlib.rcParams['text.latex.preamble'] = r'''
                \usepackage{amsmath}
                \usepackage{amsfonts}
                \usepackage{amssymb}
                \usepackage{mathtools}
                '''
                matplotlib.rcParams['font.family'] = 'serif'
                matplotlib.rcParams['font.serif'] = ['Computer Modern Roman']
            else:
                # 使用matplotlib内置mathtext
                self.logger.info("配置matplotlib使用内置mathtext")
                matplotlib.rcParams['text.usetex'] = False
                matplotlib.rcParams['font.family'] = 'serif'
                matplotlib.rcParams['mathtext.fontset'] = 'cm'  # Computer Modern

            # 设置DPI
            matplotlib.rcParams['figure.dpi'] = self.config.dpi
            matplotlib.rcParams['savefig.dpi'] = self.config.dpi

            self._matplotlib_configured = True
            self.logger.debug("matplotlib配置完成")

        except Exception as e:
            self.logger.error(f"matplotlib配置失败: {e}")
            raise

    def render(self, latex_item: LaTeXItem) -> str:
        """使用TeX Live渲染LaTeX"""
        try:
            # 确保引擎已初始化
            if not self._is_initialized:
                if not self.initialize():
                    raise RuntimeError("TeX Live引擎初始化失败")

            # 准备LaTeX代码
            latex_code = self._prepare_latex_code(latex_item.normalized_latex)
            
            # 创建图形
            fig, ax = plt.subplots(figsize=(10, 2))
            
            # 渲染LaTeX文本
            ax.text(0.5, 0.5, latex_code,
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes,
                   fontsize=self.config.font_size)

            # 移除坐标轴
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 生成输出文件路径
            image_filename = self._generate_image_filename(latex_item)
            output_path = Path(self.config.output_dir) / "images" / image_filename
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存图像
            plt.savefig(
                output_path,
                dpi=self.config.dpi,
                bbox_inches='tight',
                pad_inches=0.1,
                transparent=self.config.transparent_background,
                format='png',
                facecolor='white' if not self.config.transparent_background else 'none'
            )
            plt.close(fig)

            # 验证输出
            if not self._validate_output(str(output_path)):
                raise RuntimeError(f"输出文件验证失败: {output_path}")

            self.logger.debug(f"TeX Live渲染成功: {image_filename}")
            return str(output_path)

        except Exception as e:
            self.logger.error(f"TeX Live渲染失败: {e}")
            # 确保清理matplotlib资源
            plt.close('all')
            raise

    def is_available(self) -> bool:
        """检查TeX Live是否可用"""
        if self._texlive_available is None:
            self._check_texlive_installation()
        return self._texlive_available or False

    def _handle_initialization_error(self, error: Exception):
        """处理初始化错误"""
        self.logger.error(f"TeX Live引擎初始化失败: {error}")
        
        # 提供故障排除建议
        suggestions = [
            "1. 确认TeX Live已正确安装",
            "2. 检查PATH环境变量包含TeX Live bin目录",
            "3. 尝试在命令行运行 'latex --version'",
            "4. 检查matplotlib是否正确安装",
            "5. 确认系统有足够的磁盘空间和内存"
        ]
        
        self.logger.info("故障排除建议:")
        for suggestion in suggestions:
            self.logger.info(suggestion)

    def cleanup(self):
        """清理资源"""
        try:
            # 清理matplotlib图形
            plt.close('all')
            
            # 重置matplotlib配置（如果需要）
            if self._matplotlib_configured:
                matplotlib.rcdefaults()
                self._matplotlib_configured = False
                
            self.logger.debug("TeX Live引擎资源清理完成")
        except Exception as e:
            self.logger.warning(f"TeX Live引擎清理时出现警告: {e}")

    def _get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'dpi': self.config.dpi,
            'font_size': self.config.font_size,
            'font_family': self.config.font_family,
            'transparent_background': self.config.transparent_background,
            'image_format': self.config.image_format,
            'matplotlib_configured': self._matplotlib_configured
        }

    def get_supported_features(self) -> Dict[str, bool]:
        """获取支持的功能特性"""
        return {
            'basic_math': True,
            'complex_expressions': True,
            'environments': True,
            'custom_fonts': True,
            'transparency': True,
            'high_quality': True,
            'vector_output': False,  # 当前配置为PNG
            'color_support': True
        }

    def estimate_complexity(self, latex_code: str) -> str:
        """估算LaTeX代码复杂度"""
        # TeX Live可以处理各种复杂度的LaTeX
        complex_indicators = [
            r'\begin{', r'\end{',  # 环境
            r'\matrix', r'\array',  # 矩阵和数组
            r'\cases', r'\align',   # 复杂结构
            r'\int', r'\sum', r'\prod',  # 积分求和
            r'\frac', r'\sqrt',     # 分数和根号
        ]
        
        complexity_score = sum(1 for indicator in complex_indicators 
                             if indicator in latex_code)
        
        if complexity_score == 0:
            return 'simple'
        elif complexity_score <= 3:
            return 'medium'
        else:
            return 'complex'

    def get_version_info(self) -> Dict[str, str]:
        """获取版本信息"""
        info = {'engine': 'TeX Live + matplotlib'}
        
        try:
            # 获取TeX Live版本
            result = subprocess.run(['latex', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                if lines:
                    info['texlive'] = lines[0].strip()
        except Exception:
            info['texlive'] = 'unknown'
        
        try:
            # 获取matplotlib版本
            info['matplotlib'] = matplotlib.__version__
        except Exception:
            info['matplotlib'] = 'unknown'
        
        return info
