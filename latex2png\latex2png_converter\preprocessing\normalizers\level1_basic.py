"""
Level1基础规范化器
处理基础的LaTeX语法修复
"""

import re
from typing import Dict, Pattern, List, Tuple
from ...monitoring.logger import get_logger

class Level1BasicNormalizer:
    """Level1基础规范化器"""

    def __init__(self):
        self.logger = get_logger(__name__)

        # 基础修复规则 - 使用元组存储 (pattern, replacement, description)
        self.basic_rules: List[Tuple[Pattern, str, str]] = [
            # 字体命令现代化
            (re.compile(r'\\rm\s+([^{}]+)'), r'\\mathrm{\1}', '旧式字体命令转换'),
            (re.compile(r'\\bf\s+([^{}]+)'), r'\\mathbf{\1}', '粗体命令现代化'),
            (re.compile(r'\\it\s+([^{}]+)'), r'\\mathit{\1}', '斜体命令现代化'),
            
            # 过时语法现代化
            (re.compile(r'\\over'), r'\\frac', '分数语法现代化'),
            (re.compile(r'\\choose'), r'\\binom', '组合数语法更新'),
            (re.compile(r'\\atop'), r'\\genfrac{}{}{}{}', 'atop语法替换'),
            
            # 空格标准化
            (re.compile(r'\s+'), ' ', '多余空格合并'),
            (re.compile(r'\s*{\s*'), '{', '括号内空格清理'),
            (re.compile(r'\s*}\s*'), '}', '括号外空格清理'),
            (re.compile(r'\s*\^\s*'), '^', '上标空格清理'),
            (re.compile(r'\s*_\s*'), '_', '下标空格清理'),
            
            # 移除多余的数学模式标记
            (re.compile(r'^\$+'), '', '开头数学模式标记移除'),
            (re.compile(r'\$+$'), '', '结尾数学模式标记移除'),
            (re.compile(r'\$\$'), '', '双美元符号移除'),
            
            # 常见符号修复
            (re.compile(r'\\rm\s*\\AA'), r'\\mathrm{\\AA}', 'AA符号修复'),
            (re.compile(r'\\times\s+'), r'\\times ', '乘号空格标准化'),
            (re.compile(r'\\cdot\s+'), r'\\cdot ', '点乘空格标准化'),
            
            # 环境标准化
            (re.compile(r'\\begin\s*{\s*eqnarray\s*}'), r'\\begin{align}', 'eqnarray环境替换'),
            (re.compile(r'\\end\s*{\s*eqnarray\s*}'), r'\\end{align}', 'eqnarray环境结束替换'),
            
            # 括号匹配修复（简单情况）
            (re.compile(r'\\left\s*\('), r'\\left(', 'left括号空格清理'),
            (re.compile(r'\\right\s*\)'), r'\\right)', 'right括号空格清理'),
            (re.compile(r'\\left\s*\['), r'\\left[', 'left方括号空格清理'),
            (re.compile(r'\\right\s*\]'), r'\\right]', 'right方括号空格清理'),
        ]

        # 特殊处理规则（需要更复杂逻辑的）
        self.special_rules = [
            self._fix_frac_syntax,
            self._fix_nested_braces,
            self._fix_subscript_superscript,
        ]

    def normalize(self, latex_code: str) -> str:
        """应用基础规范化规则"""
        if not latex_code or not latex_code.strip():
            return latex_code
            
        result = latex_code.strip()
        changes_made = []

        # 应用基础正则表达式规则
        for pattern, replacement, description in self.basic_rules:
            old_result = result
            result = pattern.sub(replacement, result)
            if old_result != result:
                changes_made.append(description)
                self.logger.debug(f"应用规则: {description}")

        # 应用特殊处理规则
        for special_rule in self.special_rules:
            try:
                old_result = result
                result = special_rule(result)
                if old_result != result:
                    changes_made.append(f"特殊规则: {special_rule.__name__}")
                    self.logger.debug(f"应用特殊规则: {special_rule.__name__}")
            except Exception as e:
                self.logger.warning(f"特殊规则 {special_rule.__name__} 执行失败: {e}")

        # 最终清理
        result = result.strip()
        
        if changes_made:
            self.logger.debug(f"Level1规范化完成，应用了 {len(changes_made)} 个规则")
        
        return result

    def _fix_frac_syntax(self, latex_code: str) -> str:
        """修复分数语法"""
        # 处理 a \over b 格式
        pattern = re.compile(r'([^{}\\]+)\s*\\over\s*([^{}\\]+)')
        
        def replace_over(match):
            numerator = match.group(1).strip()
            denominator = match.group(2).strip()
            return f'\\frac{{{numerator}}}{{{denominator}}}'
        
        return pattern.sub(replace_over, latex_code)

    def _fix_nested_braces(self, latex_code: str) -> str:
        """修复嵌套括号问题"""
        # 移除多余的嵌套括号 {{content}} -> {content}
        while True:
            old_code = latex_code
            latex_code = re.sub(r'\{\{([^{}]*)\}\}', r'{\1}', latex_code)
            if old_code == latex_code:
                break
        return latex_code

    def _fix_subscript_superscript(self, latex_code: str) -> str:
        """修复上下标语法"""
        # 确保上下标内容被正确括起来
        # 处理多字符上下标
        latex_code = re.sub(r'_([a-zA-Z0-9]{2,})', r'_{\1}', latex_code)
        latex_code = re.sub(r'\^([a-zA-Z0-9]{2,})', r'^{\1}', latex_code)
        
        # 处理连续的上下标
        latex_code = re.sub(r'_\{([^}]+)\}_\{([^}]+)\}', r'_{\1\2}', latex_code)
        latex_code = re.sub(r'\^\{([^}]+)\}\^\{([^}]+)\}', r'^{\1\2}', latex_code)
        
        return latex_code

    def get_applied_rules(self, original: str, normalized: str) -> List[str]:
        """获取应用的规则列表（用于调试）"""
        applied_rules = []
        
        # 重新应用规则以检测哪些被使用
        temp_result = original.strip()
        
        for pattern, replacement, description in self.basic_rules:
            old_temp = temp_result
            temp_result = pattern.sub(replacement, temp_result)
            if old_temp != temp_result:
                applied_rules.append(description)
        
        return applied_rules

    def validate_result(self, result: str) -> bool:
        """验证规范化结果的基本正确性"""
        try:
            # 检查括号匹配
            if not self._check_brace_balance(result):
                return False
            
            # 检查基本LaTeX语法
            if not self._check_basic_syntax(result):
                return False
                
            return True
        except Exception:
            return False

    def _check_brace_balance(self, latex_code: str) -> bool:
        """检查括号是否平衡"""
        brace_count = 0
        for char in latex_code:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count < 0:
                    return False
        return brace_count == 0

    def _check_basic_syntax(self, latex_code: str) -> bool:
        """检查基本LaTeX语法"""
        # 检查是否有未闭合的命令
        if latex_code.count('\\begin{') != latex_code.count('\\end{'):
            return False
        
        # 检查是否有孤立的控制字符
        if re.search(r'\\[^a-zA-Z]', latex_code):
            return False
            
        return True
