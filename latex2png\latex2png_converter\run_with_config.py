#!/usr/bin/env python3
"""
基于配置文件的LaTeX2PNG启动器
读取user_config.yaml配置文件，自动设置环境并运行转换
"""

import sys
import os
import time
import argparse
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config_loader import load_user_config, setup_environment_from_config, validate_config, print_config_summary
from latex_preprocessor import preprocess_latex, validate_latex_syntax, get_latex_complexity_score

def render_single_formula(args):
    """渲染单个公式的工作函数（模块级别函数，支持多进程）"""
    i, latex_code, images_dir, render_config = args

    try:
        # 重新配置matplotlib（每个进程需要独立配置）
        import matplotlib
        import matplotlib.pyplot as plt
        from pathlib import Path

        if render_config.get('use_external_latex', True):
            matplotlib.rcParams['text.usetex'] = True
            matplotlib.rcParams['text.latex.preamble'] = r'''
            \usepackage{amsmath}
            \usepackage{amsfonts}
            \usepackage{amssymb}
            \usepackage{mathtools}
            \usepackage{bm}
            '''
        else:
            matplotlib.rcParams['text.usetex'] = False
            matplotlib.rcParams['mathtext.fontset'] = 'cm'

        matplotlib.rcParams['figure.dpi'] = render_config.get('dpi', 300)
        matplotlib.rcParams['savefig.dpi'] = render_config.get('dpi', 300)

        # 预处理LaTeX代码
        processed_latex, was_modified = preprocess_latex(latex_code)

        # 创建图形
        fig, ax = plt.subplots(figsize=render_config.get('figure_size', [8, 2]))
        ax.text(0.5, 0.5, f'${processed_latex}$',
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes,
               fontsize=render_config.get('font_size', 12))
        ax.axis('off')

        # 生成文件名
        filename = f"formula_{i+1:06d}.png"
        filepath = Path(images_dir) / filename

        # 保存图像
        plt.savefig(filepath,
                   dpi=render_config.get('dpi', 300),
                   bbox_inches='tight',
                   transparent=render_config.get('transparent_background', True),
                   facecolor='none')
        plt.close(fig)

        # 验证输出
        if filepath.exists() and filepath.stat().st_size > 0:
            return True, i, latex_code, None
        else:
            return False, i, latex_code, "文件未生成或为空"

    except Exception as e:
        import matplotlib.pyplot as plt
        plt.close('all')
        return False, i, latex_code, str(e)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="LaTeX2PNG转换器 - 基于配置文件运行",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_with_config.py                          # 使用配置文件中的默认输入
  python run_with_config.py input.txt                # 指定输入文件
  python run_with_config.py input.txt -o ./my_output # 指定输出目录
  python run_with_config.py --test-engines           # 测试引擎
  python run_with_config.py --config-test            # 测试配置
        """
    )
    
    parser.add_argument('input_file', nargs='?', 
                       help='输入文件路径（可选，使用配置文件默认值）')
    
    parser.add_argument('-o', '--output-dir', 
                       help='输出目录（覆盖配置文件设置）')
    
    parser.add_argument('-c', '--config', default='config/user_config.yaml',
                       help='配置文件路径（默认: config/user_config.yaml）')
    
    parser.add_argument('--test-engines', action='store_true',
                       help='测试渲染引擎')
    
    parser.add_argument('--config-test', action='store_true',
                       help='测试配置文件加载')
    
    parser.add_argument('--verbose', action='store_true',
                       help='详细输出')
    
    parser.add_argument('--dry-run', action='store_true',
                       help='试运行（不实际处理）')
    
    return parser.parse_args()

def test_engines_with_config(config):
    """使用配置测试引擎"""
    print("测试渲染引擎...")
    
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        import tempfile
        
        # 从配置获取渲染设置
        render_config = config.get('render', {})
        
        # 配置matplotlib
        if render_config.get('use_external_latex', True):
            matplotlib.rcParams['text.usetex'] = True
            matplotlib.rcParams['text.latex.preamble'] = r'''
            \usepackage{amsmath}
            \usepackage{amsfonts}
            \usepackage{amssymb}
            '''
            print("✓ 配置为使用外部TeX Live")
        else:
            matplotlib.rcParams['text.usetex'] = False
            matplotlib.rcParams['mathtext.fontset'] = 'cm'
            print("✓ 配置为使用matplotlib mathtext")
        
        matplotlib.rcParams['figure.dpi'] = render_config.get('dpi', 300)
        matplotlib.rcParams['savefig.dpi'] = render_config.get('dpi', 300)
        
        # 测试渲染
        test_formulas = [
            r"x^2 + y^2 = z^2",
            r"\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}",
            r"\begin{bmatrix} a & b \\ c & d \end{bmatrix}"
        ]
        
        success_count = 0
        
        for i, formula in enumerate(test_formulas):
            try:
                fig, ax = plt.subplots(figsize=render_config.get('figure_size', [6, 1.5]))
                ax.text(0.5, 0.5, f'${formula}$',
                       horizontalalignment='center',
                       verticalalignment='center',
                       transform=ax.transAxes,
                       fontsize=render_config.get('font_size', 12))
                ax.axis('off')
                
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
                    temp_file = f.name
                
                plt.savefig(temp_file, 
                           dpi=render_config.get('dpi', 300),
                           bbox_inches='tight',
                           transparent=render_config.get('transparent_background', True))
                plt.close(fig)
                
                if Path(temp_file).exists() and Path(temp_file).stat().st_size > 0:
                    print(f"✓ 测试公式 {i+1}: 成功 ({Path(temp_file).stat().st_size} bytes)")
                    success_count += 1
                    os.unlink(temp_file)
                else:
                    print(f"✗ 测试公式 {i+1}: 失败")
                    
            except Exception as e:
                print(f"✗ 测试公式 {i+1}: 异常 - {e}")
                plt.close('all')
        
        print(f"\n引擎测试结果: {success_count}/{len(test_formulas)} 成功")
        return success_count == len(test_formulas)
        
    except Exception as e:
        print(f"引擎测试失败: {e}")
        return False

def run_conversion_with_config(config, input_file, output_dir, verbose=False, dry_run=False):
    """使用配置运行转换"""
    print("开始LaTeX2PNG转换...")
    
    # 获取配置参数
    io_config = config.get('io', {})
    render_config = config.get('render', {})
    perf_config = config.get('performance', {})
    
    # 确定输入文件
    if not input_file:
        input_file = io_config.get('default_input_file')
        if not input_file:
            print("错误: 未指定输入文件，且配置中无默认输入文件")
            return False
    
    # 确定输出目录
    if not output_dir:
        output_dir = io_config.get('default_output_dir', './output')
    
    # 验证输入文件
    if not Path(input_file).exists():
        print(f"错误: 输入文件不存在: {input_file}")
        return False
    
    print(f"输入文件: {input_file}")
    print(f"输出目录: {output_dir}")
    print(f"DPI: {render_config.get('dpi', 300)}")
    print(f"最大并发: {perf_config.get('max_workers', 16)}")
    print(f"批次大小: {perf_config.get('batch_size', 2000)}")
    
    if dry_run:
        print("试运行模式 - 不执行实际转换")
        return True
    
    # 调用实际的转换逻辑
    try:
        # 使用原来的matplotlib转换逻辑
        import matplotlib
        import matplotlib.pyplot as plt
        import tempfile
        import json
        import time

        print("正在处理...")

        # 读取输入文件
        with open(input_file, 'r', encoding=io_config.get('input_encoding', 'utf-8')) as f:
            lines = f.readlines()

        # 过滤有效的LaTeX行
        latex_lines = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('%'):
                latex_lines.append(line)

        print(f"找到 {len(latex_lines)} 个LaTeX公式")

        if len(latex_lines) == 0:
            print("警告: 输入文件中没有有效的LaTeX公式")
            return False

        # 创建输出目录结构
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        images_dir = output_path / "images"
        images_dir.mkdir(exist_ok=True)

        # 配置matplotlib
        if render_config.get('use_external_latex', True):
            matplotlib.rcParams['text.usetex'] = True
            matplotlib.rcParams['text.latex.preamble'] = r'''
            \usepackage{amsmath}
            \usepackage{amsfonts}
            \usepackage{amssymb}
            \usepackage{mathtools}
            \usepackage{bm}
            '''
            print("✓ 使用TeX Live渲染")
        else:
            matplotlib.rcParams['text.usetex'] = False
            matplotlib.rcParams['mathtext.fontset'] = 'cm'
            print("✓ 使用matplotlib mathtext渲染")

        matplotlib.rcParams['figure.dpi'] = render_config.get('dpi', 300)
        matplotlib.rcParams['savefig.dpi'] = render_config.get('dpi', 300)

        # 批量处理
        results = {
            'total': len(latex_lines),
            'success': 0,
            'failed': 0,
            'mapping': {},
            'errors': []
        }

        # 并发处理配置
        max_workers = perf_config.get('max_workers', 16)
        batch_size = perf_config.get('batch_size', 2000)

        print(f"使用并发处理: {max_workers} 个工作进程")

        start_time = time.time()

        # 使用多进程并发处理
        from concurrent.futures import ProcessPoolExecutor, as_completed

        # 实现真正的批次处理（PRD要求的递归分批机制）
        total_formulas = len(latex_lines)
        completed = 0

        # 分批处理
        for batch_start in range(0, total_formulas, batch_size):
            batch_end = min(batch_start + batch_size, total_formulas)
            batch_lines = latex_lines[batch_start:batch_end]
            batch_size_actual = len(batch_lines)

            print(f"处理批次 {batch_start//batch_size + 1}: {batch_start+1}-{batch_end} ({batch_size_actual}个公式)")

            # 准备当前批次任务参数
            batch_tasks = [(batch_start + i, latex_code, str(images_dir), render_config)
                          for i, latex_code in enumerate(batch_lines)]

            # 并发执行当前批次
            with ProcessPoolExecutor(max_workers=max_workers) as executor:
                # 提交当前批次任务
                future_to_task = {executor.submit(render_single_formula, task): task
                                 for task in batch_tasks}

                # 收集当前批次结果
                batch_completed = 0
                for future in as_completed(future_to_task):
                    success, i, latex_code, error = future.result()
                    batch_completed += 1
                    completed += 1

                    if success:
                        results['success'] += 1
                        results['mapping'][f"formula_{i+1:06d}"] = latex_code

                        if verbose or completed % 50 == 0:
                            print(f"  ✓ 完成 {completed}/{total_formulas}: {latex_code[:50]}...")
                    else:
                        results['failed'] += 1
                        error_msg = f"公式 {i+1}: {latex_code}"
                        if error:
                            error_msg += f" - 错误: {error}"
                        results['errors'].append(error_msg)

            # 批次完成后的内存清理
            import gc
            gc.collect()

            print(f"  批次完成: {batch_completed}/{batch_size_actual} 成功")

        total_time = time.time() - start_time

        print(f"\n渲染统计:")
        print(f"  总公式数: {results['total']}")
        print(f"  成功: {results['success']}")
        print(f"  失败: {results['failed']}")
        print(f"  成功率: {results['success']/results['total']*100:.1f}%")
        print(f"  总耗时: {total_time:.2f}秒")
        print(f"  平均: {total_time/results['total']*1000:.1f}ms/公式")

        # 生成输出文件
        mapping_file = output_path / "mapping.json"
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(results['mapping'], f, ensure_ascii=False, indent=2)

        # 创建错误日志目录
        error_logs_dir = output_path / "error_logs"
        error_logs_dir.mkdir(exist_ok=True)

        # 保存详细错误日志
        if results['errors']:
            # 提取失败的LaTeX代码
            failed_latex_codes = []

            for error_msg in results['errors']:
                # 提取LaTeX代码（格式：公式 X: LaTeX代码）
                if ': ' in error_msg:
                    parts = error_msg.split(': ', 1)
                    if len(parts) >= 2:
                        latex_code = parts[1].strip()
                        failed_latex_codes.append(latex_code)

            # 保存失败的LaTeX代码为txt文件（与输入格式相同）
            if failed_latex_codes:
                failed_latex_file = error_logs_dir / "failed_formulas.txt"
                with open(failed_latex_file, 'w', encoding='utf-8') as f:
                    for latex_code in failed_latex_codes:
                        f.write(f"{latex_code}\n")

                print(f"✓ 失败公式已保存到: {failed_latex_file}")
                print(f"  - 可直接用作输入文件重新测试")
                print(f"  - 失败公式数量: {len(failed_latex_codes)}")

            # 保存错误日志
            error_log_file = error_logs_dir / "error_log.txt"
            with open(error_log_file, 'w', encoding='utf-8') as f:
                f.write("LaTeX批量渲染错误日志\n")
                f.write("=" * 50 + "\n\n")
                for error_msg in results['errors']:
                    f.write(f"{error_msg}\n")

            print(f"✓ 错误日志已保存到: {error_log_file}")

            # 保存所有错误到总文件
            all_errors_file = error_logs_dir / "all_errors.txt"
            with open(all_errors_file, 'w', encoding='utf-8') as f:
                f.write("LaTeX2PNG 渲染错误详细日志\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总错误数: {len(results['errors'])}\n")
                f.write(f"错误率: {len(results['errors'])/results['total']*100:.1f}%\n\n")

                for i, error_msg in enumerate(results['errors'], 1):
                    f.write(f"错误 {i}:\n")
                    f.write(f"{error_msg}\n")
                    f.write("-" * 80 + "\n\n")

            print(f"✓ 详细错误日志已保存到: {all_errors_file}")

        # 生成处理报告
        report = f"""LaTeX2PNG 处理报告
==================

处理统计:
- 总数: {results['total']}
- 成功: {results['success']}
- 失败: {results['failed']}
- 成功率: {results['success']/results['total']*100:.1f}%

性能指标:
- 总耗时: {total_time:.2f}秒
- 处理速率: {results['success']/total_time:.1f}公式/秒

输出文件:
- 图像目录: {images_dir}
- 映射文件: {mapping_file}
- 成功生成: {results['success']} 个PNG文件

错误分析:
- 错误日志目录: {error_logs_dir if results['errors'] else '无错误'}
- 详细错误日志: {error_logs_dir / 'all_errors.txt' if results['errors'] else '无'}
"""

        report_file = output_path / "processing_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"\n处理完成!")
        print(f"成功: {results['success']}/{results['total']} ({results['success']/results['total']*100:.1f}%)")
        print(f"耗时: {total_time:.2f}秒")
        print(f"速率: {results['success']/total_time:.1f}公式/秒")

        if results['failed'] > 0:
            print(f"失败: {results['failed']} 个公式")
            if verbose:
                for error in results['errors'][:5]:  # 只显示前5个错误
                    print(f"  - {error}")

        return results['success'] > 0
        
    except Exception as e:
        print(f"转换过程出错: {e}")
        return False

def find_config_file(config_path):
    """智能查找配置文件"""
    # 尝试多个可能的路径
    possible_paths = [
        config_path,  # 用户指定的路径
        Path(__file__).parent / config_path,  # 脚本目录下的相对路径
        Path(__file__).parent / "config" / "user_config.yaml",  # 脚本目录下的config目录
        Path.cwd() / config_path,  # 当前工作目录下的相对路径
    ]

    for path in possible_paths:
        if Path(path).exists():
            return str(path)

    return config_path  # 如果都找不到，返回原路径

def main():
    """主函数"""
    args = parse_arguments()

    print("LaTeX2PNG 配置文件启动器")
    print("=" * 50)

    # 智能查找配置文件
    config_file = find_config_file(args.config)
    print(f"查找配置文件: {config_file}")

    # 加载配置
    config = load_user_config(config_file)
    
    if not config:
        print("配置加载失败，退出")
        return 1
    
    # 验证配置
    if not validate_config(config):
        print("配置验证失败，退出")
        return 1
    
    # 设置环境
    if not setup_environment_from_config(config):
        print("环境设置失败，但继续运行")
    
    # 打印配置摘要
    if args.verbose:
        print_config_summary(config)
    
    # 执行相应操作
    if args.config_test:
        print("✓ 配置测试完成")
        return 0
    
    elif args.test_engines:
        if test_engines_with_config(config):
            print("✓ 引擎测试成功")
            return 0
        else:
            print("✗ 引擎测试失败")
            return 1
    
    else:
        # 运行转换
        start_time = time.time()
        
        success = run_conversion_with_config(
            config, 
            args.input_file, 
            args.output_dir,
            args.verbose,
            args.dry_run
        )
        
        elapsed_time = time.time() - start_time
        
        if success:
            print(f"✓ 转换完成，耗时: {elapsed_time:.2f}秒")
            return 0
        else:
            print("✗ 转换失败")
            return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
