包/命令错误日志
==============================

错误数量: 13

1. 公式 200: \hat{u}_n^{(j)}(t_0)=\begin{cases}\sqrt{\langle |u_n|^2 \rangle_T} \left[ \cos(\phi_n^{(j)}) + i \sin(\phi_n^{(j)}) \right] & n \neq m \\u_n(t_0) + \epsilon_n^{(j)} & n = m\\end{cases}\ , - 错误: latex was not able to process the following string:
b'$\\\\hat{u}_n^{(j)}(t_0)=\\\\text{\\\\sqrt{\\\\langle |u_n|^2 \\\\rangle_T} \\\\left[ \\\\cos(\\\\phi_n^{(j)}) + i \\\\sin(\\\\phi_n^{(j)}) \\\\right] n \\\\neq m ext{ or } u_n(t_0) + \\\\epsilon_n^{(j)} n = m\\\\}\\\\ ,$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmpyjvfq1n8 53eff5fc5e7900ae9304f8e9188661b0d0208054f3b3010ff19175264064459c.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./53eff5fc5e7900ae9304f8e9188661b0d0208054f3b3010ff19175264064459c.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file 53eff5fc5e7900ae9304f8e9188661b0d0208054f3b3010ff19175264064459c.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))

! Missing $ inserted.

<inserted text> 

                $

l.35 ...} u_n(t_0) + \epsilon_n^{(j)} n = m\}\ ,$}

                                                  %

No pages of output.

Transcript written on tmpyjvfq1n8/53eff5fc5e7900ae9304f8e9188661b0d0208054f3b30

10ff19175264064459c.log.





2. 公式 556: \tilde{\mathbf{f}}(x,y,z) = \begin{cases} \mathbf{f}(x,y,z), &\text{ if } y\leq 2, \\     -\mathbf{f}(x,4-y,z), &\text{ if } y > 2.    \end{cases} - 错误: latex was not able to process the following string:
b'$\\\\tilde{\\\\mathbf{f}}(x,y,z) = \\\\text{\\\\mathbf{f}(x,y,z), \\\\text{ if } y\\\\leq 2, ext{ or } -\\\\mathbf{f}(x,4-y,z), \\\\text{ if } y > 2.}$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmp_jv6k68v 8e95490e60083c12d16a237a95b7fab8b519327589789b1559a380a3f14ed553.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./8e95490e60083c12d16a237a95b7fab8b519327589789b1559a380a3f14ed553.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file 8e95490e60083c12d16a237a95b7fab8b519327589789b1559a380a3f14ed553.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))



! LaTeX Error: \mathbf allowed only in math mode.



See the LaTeX manual or LaTeX Companion for explanation.

Type  H <return>  for immediate help.

 ...                                              

                                                  

l.35 ... -\mathbf{f}(x,4-y,z), \text{ if } y > 2.}

                                                  $}%

No pages of output.

Transcript written on tmp_jv6k68v/8e95490e60083c12d16a237a95b7fab8b519327589789

b1559a380a3f14ed553.log.





3. 公式 655: \begin{align}    R_{[l,m]} &= \mathcal{F}^{-1} \hat{R}_{[l,m]} \mathcal{F}, \\    \hat{R}_{[l, m]} \,\hat{\mathbf{u}}_{\bf k} &=    \begin{cases}        \hat{\mathbf{u}}_{\mathbf{k}} & \mathrm{if} \quad l-\frac{1}{2} \leq \lVert{\bf k}\rVert_2 < m + \frac{1}{2} \\        0 & \mathrm{otherwise}    \end{cases}.\end{align} - 错误: latex was not able to process the following string:
b'$R_{[l,m]} = \\\\mathcal{F}^{-1} \\\\hat{R}_{[l,m]} \\\\mathcal{F}, \\\\hat{R}_{[l, m]} \\\\,\\\\hat{\\\\mathbf{u}}_{\\\\bf k} = \\\\text{\\\\hat{\\\\mathbf{u}}_{\\\\mathbf{k}} \\\\mathrm{if} \\\\quad l-\\\\frac{1}{2} \\\\leq \\\\lVert{\\\\bf k}\\\\rVert_2 < m + \\\\frac{1}{2} 0 \\\\mathrm{otherwise}}.$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmp7m9z9xej 21361b067cb5b918e8a4861720c443c32c6af720879bb1ab0226c681b8056d79.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./21361b067cb5b918e8a4861720c443c32c6af720879bb1ab0226c681b8056d79.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file 21361b067cb5b918e8a4861720c443c32c6af720879bb1ab0226c681b8056d79.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))



! Package amsmath Error: \hat allowed only in math mode.



See the amsmath package documentation for explanation.

Type  H <return>  for immediate help.

 ...                                              

                                                  

l.35 ..._2 < m + \frac{1}{2} 0 \mathrm{otherwise}}

                                                  .$}%

No pages of output.

Transcript written on tmp7m9z9xej/21361b067cb5b918e8a4861720c443c32c6af720879bb

1ab0226c681b8056d79.log.





4. 公式 1457: \begin{align}    H(x, t) =\begin{cases} 1, & \text{if } x \in \Omega_c , \\0, & \text{if } x \in \Omega_d .\end{cases}\end{align} - 错误: latex was not able to process the following string:
b'$H(x, t) =\\\\text{1, \\\\text{if } x \\\\in \\\\Omega_c , 0, \\\\text{if } x \\\\in \\\\Omega_d .}$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmpcquh2__p c1c58fcb5ac125b319840ce70596f5685ea2d6111dde65a22da9d6a534bcef1a.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./c1c58fcb5ac125b319840ce70596f5685ea2d6111dde65a22da9d6a534bcef1a.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file c1c58fcb5ac125b319840ce70596f5685ea2d6111dde65a22da9d6a534bcef1a.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))

! Missing $ inserted.

<inserted text> 

                $

l.35 ...\Omega_c , 0, \text{if } x \in \Omega_d .}

                                                  $}%

No pages of output.

Transcript written on tmpcquh2__p/c1c58fcb5ac125b319840ce70596f5685ea2d6111dde6

5a22da9d6a534bcef1a.log.





5. 公式 1537: w(x,z,t)=\begin{cases} 0,& z<h_{bl},\\                      -\frac{z-h_{bl}}{t+t_0},&z>h_{bl}.\end{cases} - 错误: latex was not able to process the following string:
b'$w(x,z,t)=\\\\text{0, z<h_{bl}, ext{ or } -\\\\frac{z-h_{bl}}{t+t_0},z>h_{bl}.}$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmpku8bot0r e0064777692182cb66f2ae00593d7f147d6b23d001b809a747578d5a6cc4cec1.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./e0064777692182cb66f2ae00593d7f147d6b23d001b809a747578d5a6cc4cec1.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file e0064777692182cb66f2ae00593d7f147d6b23d001b809a747578d5a6cc4cec1.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))

! Missing $ inserted.

<inserted text> 

                $

l.35 ...t{ or } -\frac{z-h_{bl}}{t+t_0},z>h_{bl}.}

                                                  $}%

No pages of output.

Transcript written on tmpku8bot0r/e0064777692182cb66f2ae00593d7f147d6b23d001b80

9a747578d5a6cc4cec1.log.





6. 公式 2624: \tag{'} \phi_z \rightarrow 0 \text{ as } z \rightarrow \infty - 错误: latex was not able to process the following string:
b"$\\\\tag{'} \\\\phi_z \\\\rightarrow 0 \\\\text{ as } z \\\\rightarrow \\\\infty$"

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmpi6yvt4do d4641cd01ccb93e5f05b518c0103184bec0f218ebb773dcaedb10798bce5e99c.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./d4641cd01ccb93e5f05b518c0103184bec0f218ebb773dcaedb10798bce5e99c.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file d4641cd01ccb93e5f05b518c0103184bec0f218ebb773dcaedb10798bce5e99c.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))



! Package amsmath Error: \tag not allowed here.



See the amsmath package documentation for explanation.

Type  H <return>  for immediate help.

 ...                                              

                                                  

l.35 {\sffamily $\tag

                     {'} \phi_z \rightarrow 0 \text{ as } z \rightarrow \inf...



No pages of output.

Transcript written on tmpi6yvt4do/d4641cd01ccb93e5f05b518c0103184bec0f218ebb773

dcaedb10798bce5e99c.log.





7. 公式 2674: \begin{cases}        \dfrac{d X}{d t}=P(X+x^*)\left[(1-x^*)Y+y^*X\right] \\        \dfrac{d Y}{d t}=-P(Y+y^*)\left[(1-y^*)X+x^*Y\right].    \end{cases} - 错误: latex was not able to process the following string:
b'$\\\\text{\\\\dfrac{d X}{d t}=P(X+x^*)\\\\left[(1-x^*)Y+y^*X\\\\right] ext{ or } \\\\dfrac{d Y}{d t}=-P(Y+y^*)\\\\left[(1-y^*)X+x^*Y\\\\right].}$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmpcncevm38 132c54a22d30e5ec32594a36071d4f9da4299e9006729de4fe5f9891e1fa3aa2.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./132c54a22d30e5ec32594a36071d4f9da4299e9006729de4fe5f9891e1fa3aa2.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file 132c54a22d30e5ec32594a36071d4f9da4299e9006729de4fe5f9891e1fa3aa2.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))

! Missing $ inserted.

<inserted text> 

                $

l.35 ...d t}=-P(Y+y^*)\left[(1-y^*)X+x^*Y\right].}

                                                  $}%

No pages of output.

Transcript written on tmpcncevm38/132c54a22d30e5ec32594a36071d4f9da4299e9006729

de4fe5f9891e1fa3aa2.log.





8. 公式 2696: \begin{cases}    \xi=Py^*(1-y^*)x+Px^*y^*y \\    \eta=\sqrt{\Delta}y    \end{cases} - 错误: latex was not able to process the following string:
b'$\\\\text{\\\\xi=Py^*(1-y^*)x+Px^*y^*y ext{ or } \\\\eta=\\\\sqrt{\\\\Delta}y}$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmpcdqkqbhg 7b8644f4f9cca0c7dadee2023a9e804abb22c31dee8e2c2912608a4db9aa9be9.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./7b8644f4f9cca0c7dadee2023a9e804abb22c31dee8e2c2912608a4db9aa9be9.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file 7b8644f4f9cca0c7dadee2023a9e804abb22c31dee8e2c2912608a4db9aa9be9.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))

! Missing $ inserted.

<inserted text> 

                $

l.35 ...)x+Px^*y^*y ext{ or } \eta=\sqrt{\Delta}y}

                                                  $}%

No pages of output.

Transcript written on tmpcdqkqbhg/7b8644f4f9cca0c7dadee2023a9e804abb22c31dee8e2

c2912608a4db9aa9be9.log.





9. 公式 2710: \begin{cases}  \dot{x}=x\left[(1-x)(f_x-f_z)-y(f_y-f_z) \right]\\  \dot{y}=y\left[(1-y)(f_y-f_z)-x(f_x-f_z) \right],\end{cases} - 错误: latex was not able to process the following string:
b'$\\\\text{\\\\dot{x}=x\\\\left[(1-x)(f_x-f_z)-y(f_y-f_z) \\\\right] ext{ or } \\\\dot{y}=y\\\\left[(1-y)(f_y-f_z)-x(f_x-f_z) \\\\right],}$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmp4uh_mi4f f99165186b819bc447cd785eacd7b603de273de093e338f1cd9975d5e27aba6e.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./f99165186b819bc447cd785eacd7b603de273de093e338f1cd9975d5e27aba6e.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file f99165186b819bc447cd785eacd7b603de273de093e338f1cd9975d5e27aba6e.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))



! Package amsmath Error: \dot allowed only in math mode.



See the amsmath package documentation for explanation.

Type  H <return>  for immediate help.

 ...                                              

                                                  

l.35 ...y\left[(1-y)(f_y-f_z)-x(f_x-f_z) \right],}

                                                  $}%

No pages of output.

Transcript written on tmp4uh_mi4f/f99165186b819bc447cd785eacd7b603de273de093e33

8f1cd9975d5e27aba6e.log.





10. 公式 2766: \begin{cases}          \dfrac{dx}{dt}=-y+a_{20}x^2+a_{11}xy+a_{02}y^2\\          \dfrac{dy}{dt}=x+b_{20}x^2+b_{11}xy+b_{02}y^2,        \end{cases} - 错误: latex was not able to process the following string:
b'$\\\\text{\\\\dfrac{dx}{dt}=-y+a_{20}x^2+a_{11}xy+a_{02}y^2 ext{ or } \\\\dfrac{dy}{dt}=x+b_{20}x^2+b_{11}xy+b_{02}y^2,}$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmpf7ol3tmz f85ff14556559acb4466947a386bb0429317eb011d6660dabe43b9bcee5634b5.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./f85ff14556559acb4466947a386bb0429317eb011d6660dabe43b9bcee5634b5.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file f85ff14556559acb4466947a386bb0429317eb011d6660dabe43b9bcee5634b5.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))

! Missing $ inserted.

<inserted text> 

                $

l.35 ...c{dy}{dt}=x+b_{20}x^2+b_{11}xy+b_{02}y^2,}

                                                  $}%

No pages of output.

Transcript written on tmpf7ol3tmz/f85ff14556559acb4466947a386bb0429317eb011d666

0dabe43b9bcee5634b5.log.





11. 公式 2780: \begin{cases}      \dot{x}=x(f_x-\phi) \\      \dot{y}=y(f_y-\phi) \\      \dot{z}=z(f_z-\phi),    \end{cases} - 错误: latex was not able to process the following string:
b'$\\\\text{\\\\dot{x}=x(f_x-\\\\phi) ext{ or } \\\\dot{y}=y(f_y-\\\\phi) ext{ or } \\\\dot{z}=z(f_z-\\\\phi),}$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmp4zd9e2e_ 67f6a53dfa51878462d3f9b65324069ae2347df27aeb1ddaec1d803723c5535c.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./67f6a53dfa51878462d3f9b65324069ae2347df27aeb1ddaec1d803723c5535c.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file 67f6a53dfa51878462d3f9b65324069ae2347df27aeb1ddaec1d803723c5535c.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))



! Package amsmath Error: \dot allowed only in math mode.



See the amsmath package documentation for explanation.

Type  H <return>  for immediate help.

 ...                                              

                                                  

l.35 ...(f_y-\phi) ext{ or } \dot{z}=z(f_z-\phi),}

                                                  $}%

No pages of output.

Transcript written on tmp4zd9e2e_/67f6a53dfa51878462d3f9b65324069ae2347df27aeb1

ddaec1d803723c5535c.log.





12. 公式 2830: \begin{cases}      \dot{x}=x(f_x-\phi) \\      \dot{y}=y(f_y-\phi),    \end{cases} - 错误: latex was not able to process the following string:
b'$\\\\text{\\\\dot{x}=x(f_x-\\\\phi) ext{ or } \\\\dot{y}=y(f_y-\\\\phi),}$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmpvxz03tsb c3917230d1c020462aac0e527f0cfce1e07ae24b5b8f9cc873888fbe06242e9f.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./c3917230d1c020462aac0e527f0cfce1e07ae24b5b8f9cc873888fbe06242e9f.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file c3917230d1c020462aac0e527f0cfce1e07ae24b5b8f9cc873888fbe06242e9f.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))



! Package amsmath Error: \dot allowed only in math mode.



See the amsmath package documentation for explanation.

Type  H <return>  for immediate help.

 ...                                              

                                                  

l.35 ...(f_x-\phi) ext{ or } \dot{y}=y(f_y-\phi),}

                                                  $}%

No pages of output.

Transcript written on tmpvxz03tsb/c3917230d1c020462aac0e527f0cfce1e07ae24b5b8f9

cc873888fbe06242e9f.log.





13. 公式 2881: \begin{cases}          \dfrac{dx}{dt}=x\left[(1-x)\varphi(y)-y\psi(x) \right]\\          \dfrac{dy}{dt}=y\left[(1-y)\psi(x)-x\varphi(y) \right],        \end{cases} - 错误: latex was not able to process the following string:
b'$\\\\text{\\\\dfrac{dx}{dt}=x\\\\left[(1-x)\\\\varphi(y)-y\\\\psi(x) \\\\right] ext{ or } \\\\dfrac{dy}{dt}=y\\\\left[(1-y)\\\\psi(x)-x\\\\varphi(y) \\\\right],}$'

Here is the full command invocation and its output:

latex -interaction=nonstopmode --halt-on-error --output-directory=tmpgu4cnhqe 8d30746cf3c3d30d5125656bea4d13e9803f66ba00c3d96c9e9eb4e9ea4e1f59.tex

This is pdfTeX, Version 3.141592653-2.6-1.40.28 (TeX Live 2025) (preloaded format=latex)

 restricted \write18 enabled.

entering extended mode

(./8d30746cf3c3d30d5125656bea4d13e9803f66ba00c3d96c9e9eb4e9ea4e1f59.tex

LaTeX2e <2025-06-01> patch level 1

L3 programming layer <2025-07-19>

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/article.cls

Document Class: article 2025/01/22 v1.4n Standard LaTeX document class

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/size10.clo))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/type1cm/type1cm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/cm-super/type1ec.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/t1cmr.fd))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/inputenc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty)))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsmath.sty

For additional information on amsmath, use the `?' option.

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amstext.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsgen.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsbsy.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsmath/amsopn.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amsfonts.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/amsfonts/amssymb.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mathtools.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/calc.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/mathtools/mhsetup.sty))

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/tools/bm.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/underscore/underscore.sty)

==> First Aid for underscore.sty applied!



(d:/Program Files/texlive/2025/texmf-dist/tex/latex/firstaid/underscore-ltx.sty

) (d:/Program Files/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-dvips.d

ef)

No file 8d30746cf3c3d30d5125656bea4d13e9803f66ba00c3d96c9e9eb4e9ea4e1f59.aux.

*geometry* driver: auto-detecting

*geometry* detected driver: dvips

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg)

(d:/Program Files/texlive/2025/texmf-dist/tex/latex/graphics-def/dvips.def)))

! Missing $ inserted.

<inserted text> 

                $

l.35 ...=y\left[(1-y)\psi(x)-x\varphi(y) \right],}

                                                  $}%

No pages of output.

Transcript written on tmpgu4cnhqe/8d30746cf3c3d30d5125656bea4d13e9803f66ba00c3d

96c9e9eb4e9ea4e1f59.log.





