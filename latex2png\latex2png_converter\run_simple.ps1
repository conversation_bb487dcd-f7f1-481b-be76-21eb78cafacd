# Simple LaTeX2PNG runner

param(
    [string]$InputFile,
    [string]$OutputDir = "./output",
    [switch]$TestEngines,
    [switch]$Verbose
)

Write-Host "LaTeX2PNG Converter" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

# Configure TeX Live environment
$texlivePath = "D:\Program Files\texlive\2025\bin\windows"
$pythonPath = "D:\miniforge3\envs\formula\python.exe"

Write-Host "Configuring environment..." -ForegroundColor Yellow

# Add TeX Live to PATH
if (Test-Path $texlivePath) {
    $env:PATH = "$texlivePath;$env:PATH"
    Write-Host "TeX Live path added: $texlivePath" -ForegroundColor Green
} else {
    Write-Host "TeX Live path not found: $texlivePath" -ForegroundColor Yellow
}

# Remove MiKTeX paths to avoid conflicts
$env:PATH = ($env:PATH -split ';' | Where-Object { $_ -notlike '*miktex*' }) -join ';'

# Check Python
if (-not (Test-Path $pythonPath)) {
    Write-Host "Python not found: $pythonPath" -ForegroundColor Red
    exit 1
}

# Build arguments
$args = @()

if ($TestEngines) {
    $args += "--test-engines"
} else {
    if (-not $InputFile) {
        Write-Host "Input file required when not testing engines" -ForegroundColor Red
        exit 1
    }
    if (-not (Test-Path $InputFile)) {
        Write-Host "Input file not found: $InputFile" -ForegroundColor Red
        exit 1
    }
    $args += $InputFile
    $args += "--output-dir", $OutputDir
}

if ($Verbose) {
    $args += "--verbose"
}

# Run converter
Write-Host "Running LaTeX2PNG..." -ForegroundColor Green
Write-Host "Command: $pythonPath main.py $($args -join ' ')" -ForegroundColor Gray

try {
    & $pythonPath main.py @args
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Conversion completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Conversion failed (exit code: $LASTEXITCODE)" -ForegroundColor Red
        exit $LASTEXITCODE
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
