# LaTeX to PNG 转换器默认配置文件

# 渲染配置
render:
  dpi: 300                          # 图像DPI设置（完全可调）
  transparent_background: true      # 背景透明度
  font_family: "Computer Modern"    # 字体族
  font_size: 12                     # 字体大小
  image_format: "png"               # 图像格式
  output_dir: "./output"            # 输出目录

# 处理配置
process:
  max_workers: 16                   # 最大并发数（基于20核28线程优化）
  batch_size: 2000                  # 批次大小
  timeout_seconds: 30               # 单项处理超时时间
  retry_attempts: 2                 # 重试次数

# 输出配置
output:
  output_dir: "./output"            # 输出根目录
  images_subdir: "images"           # 图像子目录
  json_filename: "mapping.json"     # JSON映射文件名
  error_log_dir: "error_logs"       # 错误日志目录

# 预处理配置
preprocessing:
  level1_enabled: true              # 启用Level1基础规范化
  level2_enabled: false             # Level2结构规范化（迭代1暂不启用）
  level3_enabled: false             # Level3语义规范化（迭代1暂不启用）
  enabled_levels:                   # 启用的规范化级别列表
    - "level1"
