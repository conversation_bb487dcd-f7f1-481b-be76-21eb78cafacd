{"formula_000001": "320", "formula_000025": "\\hat{\\Sigma}_{\\mathbb{C}}", "formula_000026": "n = 16", "formula_000007": "E_n", "formula_000027": "u_1", "formula_000028": "19\\%", "formula_000029": "\\tilde{E}_n=\\langle |\\tilde{u}_n|^2 \\rangle_{T}", "formula_000005": "\\epsilon_n(t_{k+1})", "formula_000003": "\\tau_{7}", "formula_000006": "\\alpha_n = \\alpha'_n / \\tau", "formula_000008": "\\tilde{U}_m^{(j)}", "formula_000004": "\\lfloor \\cdot \\rfloor", "formula_000011": "\\tilde{\\bm{u}}(t) = \\Phi_{0 \\to t}(\\tilde{\\bm{u}}(0))", "formula_000013": "N_{\\text{seq}}", "formula_000010": "\\lambda_{5}=\\lambda_{7}=0.2", "formula_000014": "\\mathcal{E}^{\\Pi}_n/\\sqrt{\\tilde{\\Delta}_n \\Delta_n}", "formula_000015": "n=13", "formula_000002": "L = 40", "formula_000021": "\\| \\bm{a} \\|_{\\mathcal{L}}^2 = \\bm{a}^\\dagger \\mathcal{L}^{-1} \\bm{a}", "formula_000012": "27.89 \\pm 5.20", "formula_000009": "\\langle |u_{13} - \\tilde{u}_{13}|^2 \\rangle_L = 2 |u_{13}|^2", "formula_000030": "\\hat{\\bm{U}}^\\top \\hat{\\bm{\\Sigma}} \\hat{\\bm{U}} \\ge 0", "formula_000019": "{\\hat{\\Sigma}_{nn}}", "formula_000018": "\\tilde{E}_n=\\langle |\\tilde{u}_n|^2 \\rangle_{T,L}", "formula_000023": "\\theta - \\tilde{\\theta}", "formula_000022": "\\tilde{U}_n^{(j)} = \\hat{U}_n^{(j)} + \\frac{\\sigma_{nm}}{\\sigma_{mm}^2 + \\sigma^2} \\left[Z_{m} - \\hat{U}_{m}^{(j)} - V^{(j)}\\right]\\ ,", "formula_000016": "\\hat{U}_n^{(j)}", "formula_000020": "\\mathcal{E}_n = \\langle |u_n - \\tilde{u}_n|^2 \\rangle_{T,L}\\ ,", "formula_000017": "\\langle \\cdot \\rangle_T = \\frac{1}{N_T} \\sum_{t_k=1}^{N_T} (\\cdot)", "formula_000031": "N", "formula_000032": "S_p(k_n)= \\left\\langle|u_n|^p\\right\\rangle_T\\ ,", "formula_000024": "6", "formula_000033": "N_{\\text{inflation}}", "formula_000034": "\\bm{H} \\in \\mathbb{R}^{2M \\times 2N}", "formula_000035": "\\sum_{n=0}^2 \\langle |u_n - \\tilde{u}_n|^2 \\rangle_T", "formula_000036": "\\hat{U}_m^{(j)}", "formula_000038": "\\alpha'", "formula_000039": "u_{n-1}^* u_n^* u_{n+1}", "formula_000037": "\\bm{Z}(t_{\\text{obs}}) \\in \\mathbb{R}^{2M}", "formula_000040": "T_n(\\bm{u}(t), t) = \\frac{(t_{k+1} - t) z_n(t_k) + (t - t_k) z_n(t_{k+1})}{t_{k+1} - t_k},", "formula_000041": "u^*_{n-1} u^*_n u_{n+1}", "formula_000043": "7", "formula_000045": "u_n(t) = y_n(t) I_n(t)", "formula_000044": "T_a", "formula_000047": "\\lambda_{5}=\\lambda_{11}=0.05", "formula_000042": "\\hat{\\bm{\\Sigma}} = \\begin{bmatrix}\\Re(\\hat{\\bm{\\Sigma}}_{\\mathbb{C}} + \\hat{\\bm{\\Sigma}}_p) & -\\Im(\\hat{\\bm{\\Sigma}}_{\\mathbb{C}} - \\hat{\\bm{\\Sigma}}_p) \\\\\\Im(\\hat{\\bm{\\Sigma}}_{\\mathbb{C}} + \\hat{\\bm{\\Sigma}}_p) & \\ \\ \\Re(\\hat{\\bm{\\Sigma}}_{\\mathbb{C}} - \\hat{\\bm{\\Sigma}}_p)\\end{bmatrix}\\ ,", "formula_000052": "L", "formula_000048": "\\Delta t_{\\textit{obs}} > \\tau_9", "formula_000050": "\\mathcal{P}(n,n) = N_{\\text{reg}} / (2 E_n)", "formula_000051": "u_n^* u_{n+1}^* u_{n+2}", "formula_000049": "(R_\\mathbb{C})_{mn} = \\delta_{mn}\\ 2\\ (0.05)^2 \\langle |u_n|^2 \\rangle", "formula_000053": "\\alpha'_n", "formula_000046": "23.15 \\pm 1.36", "formula_000054": "k_{\\eta}", "formula_000056": "\\theta \\in [0, 2\\pi]", "formula_000055": "\\tilde{\\bm{u}}^{(i,j)}(t)", "formula_000057": "n=8", "formula_000058": "n = 5", "formula_000059": "\\hat{\\bm{\\Sigma}}(t_{\\text{obs}})", "formula_000060": "\\epsilon_n(t_k)", "formula_000061": "Z_m", "formula_000062": "\\lambda_{10}=0.01", "formula_000063": "\\tau_n", "formula_000064": "\\alpha", "formula_000077": "27.74 \\pm 2.47", "formula_000065": "\\langle (\\bm{U}_n - \\tilde{\\bm{U}}_n)^2 \\rangle_{T, L}=0", "formula_000066": "u_n \\mapsto u_n e^{i\\theta}", "formula_000067": "\\langle|\\tilde{u}_{13}|^2\\rangle_L = |u_{13}|^2", "formula_000068": "\\langle u_{n-1} u_{n}^* u_{n+1} \\rangle", "formula_000070": "\\bm{U}(t)", "formula_000071": "N = 20", "formula_000069": "\\langle|u_{13}-\\tilde{u}_{13}|^2\\rangle_L", "formula_000072": "\\Delta t_{\\text{obs}}=\\tau_{9} = 0.02\\tau_0", "formula_000073": "\\bm{u} = \\{u_0, u_1, \\ldots, u_{N-1}\\} \\in \\mathbb{C}^N", "formula_000074": "\\Delta t_{\\text{obs}}=\\tau_{9}=0.02\\tau_0", "formula_000075": "I_n(t) = \\exp\\left(-\\int dt\\, \\nu k_n^2\\right) = \\exp(-\\nu k_n^2 t)\\ .", "formula_000076": "\\mathcal{E}_n/\\sqrt{E_n \\tilde{E}_n}", "formula_000078": "\\hat{\\Sigma}_{nn}", "formula_000080": "0\\%", "formula_000079": "\\mathcal{E} = \\sum_{n=1}^{15} \\mathcal{E}_n / \\sqrt{\\tilde{E}_n E_n}", "formula_000081": "20\\tau_0", "formula_000082": "\\bm{v}(t)", "formula_000083": "\\theta_{n+2}+\\theta_{n+1}-\\theta_{n}=0\\ .", "formula_000084": "\\tilde{\\bm{\\Sigma}}(t_{\\text{obs}}) = (\\bm{I} - \\bm{K}\\bm{H}) \\hat{\\bm{\\Sigma}}(t_{\\text{obs}})", "formula_000085": "\\bm{U}(t_{\\text{obs}})", "formula_000086": "\\langle|u_{13}-\\tilde{u}_{13}|^2\\rangle_L=2|u_{13}|^2", "formula_000087": "\\mathcal{E}_n", "formula_000088": "N_T", "formula_000089": "E_n = \\tilde{E}_n", "formula_000091": "|u_{13}|", "formula_000093": "(\\hat{\\Sigma}_p)_{nm}", "formula_000090": "t_{\\text{obs}} = t + \\Delta t_{\\textit{obs}}", "formula_000092": "12\\leq t/\\tau_0\\leq 14", "formula_000095": "\\tilde{\\bm{u}}^{(i)}(0)", "formula_000094": "H(t)=\\sum_{n=0}^N (-1)^n |u_n(t)|^2 k_n", "formula_000096": "\\lambda_i=0", "formula_000098": "n", "formula_000101": "u_{n-1} u_n u^*_{n+1}", "formula_000097": "\\hat{\\bm{\\mu}} = \\frac{1}{L} \\sum_{j=1}^L \\hat{\\bm{U}}^{(j)}", "formula_000100": "\\mathcal{E}^{\\Pi}_n / \\sqrt{\\Delta_n \\tilde{\\Delta}_n}", "formula_000099": "\\nu k_n^2 u_n", "formula_000102": "\\lambda_{13}=0.005", "formula_000103": "\\Delta t_{\\textit{obs}} = \\tau_{15}", "formula_000104": "j", "formula_000105": "\\dagger", "formula_000106": "dt = 10^{-5}", "formula_000107": "\\alpha'_n = \\alpha'", "formula_000108": "\\mathcal{E}_n = \\langle |u_n - \\tilde{u}_n|^2 \\rangle_{T,L}", "formula_000109": "\\lambda", "formula_000110": "\\bm{y}", "formula_000111": "\\Re(\\tilde{u}_{13}^{(j)}) < 0", "formula_000112": "8", "formula_000113": "\\tilde{\\Sigma}_{nm}", "formula_000115": "|\\tilde{u}_n|^2 k_n^{2/3}", "formula_000114": "\\langle \\hat{\\Sigma}_{nn} \\rangle_T \\geq \\langle \\tilde{\\Sigma}_{nn} \\rangle_T", "formula_000117": "\\mathcal{E}_n < E_n", "formula_000116": "\\Delta t_{\\text{obs}}=\\tau_{6} = 0.1\\tau_0", "formula_000119": "n_{\\text{seq}}=0", "formula_000118": "\\Delta t_{\\textit{obs}}", "formula_000120": "\\delta_{nm}", "formula_000122": "K_{nm}", "formula_000124": "\\tilde{\\bm{\\Sigma}}", "formula_000123": "\\left\\| \\tilde{\\bm{u}}^{(i-1,j)}(0) - \\tilde{\\bm{u}}^{(i,j)}(0) \\right\\|_{\\mathcal{P}}", "formula_000121": "\\tilde{E}_n = \\langle |\\tilde{u}_n|^2 \\rangle_{T,L}", "formula_000125": "\\mathcal{E}_\\Pi = \\sum_{n=1}^{15} \\mathcal{E}_n^\\Pi / \\sqrt{\\tilde{\\Delta}_n \\Delta_n}", "formula_000126": "L=4", "formula_000127": "\\bm{I} = \\{e^{-\\nu dt k_0^2/2}, e^{-\\nu dt k_1^2/2}, \\ldots, e^{-\\nu dt k_{N-1}^2/2}\\}", "formula_000128": "\\frac{dy_n(t)}{dt} = (G_n[I \\circ y] + f_n) I_n^{-1}(t)\\ .", "formula_000129": "j = 1, \\ldots, L", "formula_000130": "\\begin{eqnarray}J^{(i)} &=& \\left\\| \\hat{\\bm{u}}(0) - \\tilde{\\bm{u}}^{(i)}(0) \\right\\|_{\\mathcal{B}}^2  \\\\&+& \\sum_{t=0}^{T_a} \\left\\| \\bm{z}(t) - \\bm{H}_{\\mathbb{C}} \\tilde{\\bm{u}}^{(i)}(t) - \\bm{v}(t) \\right\\|_{\\bm{R}_\\mathbb{C}}^2\\,,\\end{eqnarray}", "formula_000131": "\\tilde{\\bm{u}}^{(i)}(t)", "formula_000132": "\\tau_{15}", "formula_000133": "\\mathcal{E}^\\Pi = \\sum_{n=1}^{15} \\mathcal{E}_n^\\Pi / \\sqrt{\\tilde{\\Delta}_n \\Delta_n}", "formula_000134": "I_n(t)", "formula_000135": "R_{mn} = \\delta_{mn}(0.05)^2\\langle |u_{\\lfloor m/2 \\rfloor}|^2 \\rangle_T\\ ,", "formula_000136": "\\tilde{\\bm{u}}^{(i-1,j)} \\approx \\tilde{\\bm{u}}^{(i,j)}", "formula_000137": "\\phi_n^{(j)} \\in [0, 2\\pi]", "formula_000141": "|u_n-\\tilde{u}_n^{(j)}|^2", "formula_000138": "\\Delta t_{\\text{obs}} = \\tau_{15}", "formula_000140": "N_{\\text{exp}} = 16", "formula_000139": "\\mathcal{E}_n^\\Pi", "formula_000142": "u_{13}", "formula_000144": "\\alpha' = 0.1", "formula_000143": "\\left(\\frac{{\\rm d}}{{\\rm d}t}+\\nu k_n^2\\right)u_n(t) = G_n[\\bm{u}] + f_n(t)\\ ,", "formula_000145": "4.85 \\pm 0.26", "formula_000146": "n=9", "formula_000147": "\\lambda\\in{\\mathbb R}^+", "formula_000149": "E_n=\\langle |u_n|^2 \\rangle_T", "formula_000150": "\\sigma_{mm}^2", "formula_000148": "\\left(\\frac{d}{dt} + \\nu k_n^2\\right)\\tilde{u}_n(t) = G_n[\\bm{u}] + f_n + \\alpha_n \\delta_{nm} \\left[T_n(\\bm{u},t) - \\tilde{u}_n(t)\\right]\\,,", "formula_000151": "\\hat{u}_n^{(j)}(t_0) = \\sqrt{|u_n(t_0)|^2} \\left[ \\cos\\left(\\phi_n^{(j)}\\right) + i \\sin\\left(\\phi_n^{(j)}\\right) \\right]\\ .", "formula_000152": "\\Delta t_{\\text{obs}}=\\tau_{7}=0.06\\tau_0", "formula_000153": "n < 15", "formula_000154": "\\bm{\\xi}(t_{\\text{obs}})", "formula_000155": "\\Phi_{0 \\to t}", "formula_000156": "\\hat{\\bm{\\Sigma}}_{\\mathbb{C}}", "formula_000157": "\\sigma^2", "formula_000158": "\\langle u_n u_{m}^* \\rangle =0", "formula_000160": "9.10 \\pm 0.69", "formula_000159": "I_{nm} = e^{-dt (\\nu k_n^2 + \\alpha_n \\delta_{nm})/2}\\ ,", "formula_000161": "\\mathcal{E}\\pm\\Delta\\mathcal{E}", "formula_000162": "u_n\\rightarrow u_n e^{i\\theta}", "formula_000163": "k_n=2^n", "formula_000164": "\\partial\\,\\mathrm{tr}(\\tilde{\\bm{\\Sigma}})/\\partial \\bm{K} = 0", "formula_000165": "10.07 \\pm 3.21", "formula_000166": "\\bm{I}", "formula_000168": "13\\%", "formula_000169": "\\zeta(p)", "formula_000167": "S_p(k_n)\\propto k_n^{-\\zeta(p)}", "formula_000172": "z_n(t_{k+1}) = u_n(t_{k+1}) + \\epsilon_n(t_{k+1})", "formula_000170": "\\bm{Z}(t_{\\text{obs}}) = \\bm{H}\\bm{U}(t_{\\text{obs}}) + \\bm{\\xi}(t_{\\text{obs}})\\,,    \\quad \\bm{\\xi}(t_{\\text{obs}}) \\sim \\mathcal{N}(\\bm{0}, \\bm{R})\\,.", "formula_000173": "\\mathcal{P}(n,n) = N_{\\text{reg}}", "formula_000174": "|u_{13} - \\tilde{u}_{13}^{(j)}|^2", "formula_000171": "\\begin{aligned}&\\frac{|\\max_{N_\\textit{exp}}(\\mathcal{E}_n/\\sqrt{\\tilde{E}_nE_n}) + \\min_{N_\\textit{exp}}(\\mathcal{E}_n/\\sqrt{\\tilde{E}_nE_n})|}{2}\\\\\\pm&\\frac{|\\max_{N_\\textit{exp}}(\\mathcal{E}_n/\\sqrt{\\tilde{E}_nE_n}) - \\min_{N_\\textit{exp}}(\\mathcal{E}_n/\\sqrt{\\tilde{E}_nE_n})|}{2}\\,,\\end{aligned}", "formula_000175": "T = 10\\tau_0", "formula_000176": "28.65 \\pm 1.05", "formula_000177": "u_2", "formula_000178": "\\circ", "formula_000179": "\\lambda_{2}=0.1", "formula_000180": "25.81 \\pm 1.88", "formula_000181": "n\\neq m", "formula_000183": "m, n = 0, \\ldots, 2N-1", "formula_000184": "u^*_n u^*_{n+1} u_{n+2}", "formula_000185": "\\langle |u_{13} - \\tilde{u}_{13}|^2 \\rangle_L", "formula_000182": "I_n(dt) = \\exp(-\\nu\\, dt\\, k_n^2 /2)", "formula_000186": "f_n(t)", "formula_000187": "\\langle \\cdot \\rangle_L = \\frac{1}{L} \\sum_{j=1}^{L} (\\cdot)", "formula_000188": "n \\gtrsim 15", "formula_000190": "\\Delta t_{\\text{obs}}=\\tau_{15} = 0.002\\tau_0", "formula_000189": "\\bm{R}", "formula_000192": "\\Delta t_{\\text{obs}}", "formula_000191": "g_n(t) \\tilde{U}_n+(1-g_n(t))\\tilde{\\mu}_n\\to \\tilde{U}_n\\ ,", "formula_000193": "k_n^{2/3}|u_n|^2", "formula_000195": "n = 6", "formula_000194": "22.44 \\pm 1.14", "formula_000196": "f_n", "formula_000198": "g_n g_m \\tilde{\\Sigma}_{nm}", "formula_000197": "E_n=\\langle |u_n|^2 \\rangle_T\\ , \\qquad \\tilde{E}_n=\\langle |\\tilde{u}_n|^2 \\rangle_{T,L}", "formula_000199": "\\hat{U}^{(j)}_n", "formula_000202": "\\lambda_{5}=\\lambda_{10}=0.1", "formula_000201": "\\langle\\cdot\\rangle_T", "formula_000203": "\\tau_{15} \\le \\Delta t_{\\textit{obs}} \\le \\tau_{4}", "formula_000205": "(a, b, c) = (1, -0.5, -0.5)", "formula_000204": "N{_\\text{exp}} = 16", "formula_000207": "\\hat{\\bm{\\Sigma}} = \\langle(\\hat{\\bm{U}} - \\langle \\hat{\\bm{U}} \\rangle_L)(\\hat{\\bm{U}} - \\langle \\hat{\\bm{U}} \\rangle_L)^{T}\\rangle_L", "formula_000206": "\\lambda_{14}=0.25", "formula_000209": "u_n", "formula_000208": "\\lambda_i", "formula_000210": "M \\leq N", "formula_000211": "\\mathcal{E}^{\\Pi}_n = \\langle |u_{n-1} u_n u^*_{n+1} - \\tilde{u}_{n-1} \\tilde{u}_n \\tilde{u}^*_{n+1}|^2 \\rangle_{T,L}\\ .", "formula_000212": "\\mathcal{E}^\\Pi_n", "formula_000214": "\\tilde{E}_n", "formula_000215": "26.83 \\pm 1.21", "formula_000216": "\\tilde{\\bm{u}}", "formula_000217": "m,n = 0,\\ldots,N-1", "formula_000213": "\\begin{aligned}J^{(i,j)}_{n_{\\text{seq}}} =&\\left\\| \\hat{\\bm{u}}^{(j)}(n_{\\text{seq}}T_a) - \\tilde{\\bm{u}}^{(i,j)}(n_{\\text{seq}}T_a) \\right\\|_{\\hat{\\bm{\\Sigma}}_{\\mathbb{C}}} \\\\+\\sum_{t = n_{\\text{seq}}T_a}^{(n_{\\text{seq}}+1)T_a}&\\left\\| \\bm{z}(t) - \\bm{H}_c \\tilde{\\bm{u}}^{(i,j)}(t) - \\bm{v}^{(j)}(t) \\right\\|_{\\bm{R}_\\mathbb{C}}\\,,\\end{aligned}", "formula_000219": "\\hat{\\bm{u}}(0)", "formula_000222": "\\epsilon_n^{(j)}", "formula_000220": "\\tau = dt \\Delta t_{\\textit{obs}}", "formula_000221": "0.016 \\pm 0.004", "formula_000218": "\\tau_0 \\approx 0.5", "formula_000223": "\\mathbf{R}", "formula_000225": "6\\%", "formula_000224": "\\bm{V}^{(j)}(t_{\\text{obs}}) \\sim \\mathcal{N}(\\bm{0}, \\bm{R})", "formula_000226": "n = 0", "formula_000227": "j = 1, \\dots, L", "formula_000228": "E_n = \\langle |u_n|^2 \\rangle_T", "formula_000229": "n = 15", "formula_000231": "\\lambda_{14}=0.2", "formula_000230": "14.68 \\pm 3.90", "formula_000232": "n=15", "formula_000233": "\\Delta t_{\\text{obs}}=\\tau_{8}=0.04\\tau_0", "formula_000235": "29.49 \\pm 4.23", "formula_000234": "1 \\leq n \\leq 8", "formula_000236": "\\bm{K}(t_{\\text{obs}}) \\in \\mathbb{R}^{2N \\times 2m}", "formula_000238": "\\hat{\\bm{u}}^{(j)}", "formula_000237": "E(t)=\\sum_{n=0}^N |u_n(t)|^2", "formula_000239": "u_6", "formula_000240": "28.74 \\pm 4.26", "formula_000241": "\\Delta t_{\\text{obs}}=\\tau_{6}=0.1\\tau_0", "formula_000244": "\\Delta t_{\\text{obs}}=\\tau_{15}=0.002\\tau_0", "formula_000243": "(\\tilde{\\bm{u}}(0))", "formula_000242": "|u_n|^2 k_n^{2/3}", "formula_000245": "t_{k+1}", "formula_000246": "n=0", "formula_000247": "\\langle |\\tilde{u}_n|^2\\rangle_{T,L}", "formula_000248": "\\Delta_n=\\langle |u_{n-1} u_n u^*_{n+1}|^2 \\rangle_T", "formula_000250": "G_n[\\bm{u}]", "formula_000249": "\\langle \\cdot \\rangle_{T,L} = \\langle \\langle \\cdot \\rangle_L \\rangle_T", "formula_000251": "\\tau_{15} = \\Delta t_{\\textit{obs}}", "formula_000252": "\\ell^2", "formula_000253": "f_n=\\nu=0", "formula_000254": "\\{\\hat{\\bm{U}}^{(1)}, \\hat{\\bm{U}}^{(2)}, \\dots, \\hat{\\bm{U}}^{(L)}\\} \\subset \\mathbb{R}^{2N}", "formula_000255": "90\\%", "formula_000257": "n_{seq} > 0", "formula_000256": "\\lambda_i = 0", "formula_000258": "L = 1000", "formula_000259": "\\Delta t_{\\textit{obs}}=\\tau_{15}=0.002\\tau_0", "formula_000261": "n = 4", "formula_000260": "\\Delta t_{\\text{obs}}=\\tau_{4}=0.2\\tau_0", "formula_000262": "u_n(t + dt) = I_n^2(dt) \\left(u_n(t) + \\frac{A_{1n}}{6}\\right) + I_n \\frac{(A_{2n} + A_{3n})}{3} + \\frac{A_{4n}}{3}\\ ,", "formula_000268": "n=6,7,8", "formula_000263": "g_n(t)=\\textit{max}\\left(1,1+\\lambda\\frac{\\hat{\\Sigma}_{nn}-\\tilde{\\Sigma}_{nn}}{\\hat{\\Sigma}_{nn}}\\right)\\ ,", "formula_000269": "\\mathcal{B}", "formula_000264": "12 \\leq t / \\tau_0 \\leq 14", "formula_000266": "\\mathcal{P}", "formula_000267": "9 \\le n \\le 16", "formula_000265": "23.73 \\pm 0.50", "formula_000270": "\\Delta t_{\\text{obs}} = \\tau_{4}", "formula_000271": "\\langle |\\tilde{u}_n|^2\\rangle_{T}", "formula_000272": "k=0.2k_{\\eta}", "formula_000273": "g_n(t)\\ge 1", "formula_000274": "\\Delta t_{\\textit{obs}} = \\tau_n", "formula_000275": "c", "formula_000277": "\\hat{\\bm{\\Sigma}}_{\\mathbb{C}} = \\langle(\\hat{\\bm{u}} - \\langle \\hat{\\bm{u}} \\rangle_L)(\\hat{\\bm{u}} - \\langle \\hat{\\bm{u}} \\rangle_L)^\\dagger\\rangle_L", "formula_000278": "0.73 \\pm 0.53", "formula_000276": "28.40 \\pm 3.23", "formula_000281": "\\lambda_{5}=\\lambda_{7}=\\lambda_{11}=\\lambda_{15}=\\lambda_{16}=0.05", "formula_000279": "\\langle |u_{m}|^2 \\rangle_T", "formula_000280": "\\hat{\\bm{U}} = (\\Re(\\hat{\\bm{u}}),\\,\\Im(\\hat{\\bm{u}}))\\in\\mathbb{R}^{2N}", "formula_000282": "n \\ge 17", "formula_000283": "50\\%", "formula_000284": "0.18 \\pm 0.04", "formula_000285": "\\tilde{\\Delta}_n=\\langle |\\tilde{u}_{n-1} \\tilde{u}_n \\tilde{u}^*_{n+1}|^2 \\rangle_{T,L}", "formula_000286": "n, m", "formula_000287": "\\begin{cases}A_{1n} = dt(f_n + G_n[\\bm{u}]) \\\\A_{2n} = dt(f_n + G_n[\\bm{I} \\circ (\\bm{u} + \\frac{\\bm{A}_1}{2})]) \\\\A_{3n} = dt(f_n + G_n[\\bm{I} \\circ \\bm{u} + \\frac{\\bm{A}_2}{2}]) \\\\A_{4n} = dt(f_n + G_n[\\bm{I} \\circ \\bm{I} \\circ \\bm{u} + \\bm{I} \\circ \\bm{A}_3])\\end{cases}", "formula_000288": "\\theta_n - \\tilde{\\theta}_n", "formula_000291": "\\Pi_n", "formula_000290": "a+b+c=0", "formula_000293": "n = 0,1,\\ldots,N-1", "formula_000292": "\\langle |u_n|^2 \\rangle_T", "formula_000289": "\\begin{eqnarray}    G_n[u] &=& i(ak_{n+1}u_{n+1}^*u_{n+2} + bk_n u_{n-1}^*u_{n+1}  \\\\    &-& ck_{n-1}u_{n-1}u_{n-2})\\,. \\end{eqnarray}", "formula_000294": "\\tau_0", "formula_000295": "T_a = 1000\\,dt \\approx 0.02\\,\\tau_0 \\approx \\tau_9", "formula_000297": "\\Delta t_{\\text{obs}}=\\tau_{8} = 0.04\\tau_0", "formula_000296": "\\Delta\\mathcal{E}", "formula_000298": "\\hat{\\bm{u}}^{(j)}(n_{seq}T_{a}) = \\tilde{\\bm{u}}^{(j)}(n_{seq}T_{a})", "formula_000299": "\\bm{z}", "formula_000301": "10\\tau_0", "formula_000300": "\\sim13\\%", "formula_000302": "u_n\\in\\mathbb{C}", "formula_000303": "n = 13", "formula_000304": "c=-\\frac{1}{2}", "formula_000305": "N_\\textit{exp}=16", "formula_000307": "z_n(t_k) = u_n(t_k) + \\epsilon_n(t_k)", "formula_000306": "\\mathbf{R}\\in \\mathbb{R}^{2M \\times 2M}", "formula_000309": "u_7", "formula_000308": "\\tau_9", "formula_000310": "u_8", "formula_000311": "7.29 \\pm 0.95", "formula_000315": "c<0", "formula_000316": "\\alpha_n", "formula_000314": "\\hat{\\bm{u}}^{(j)}(t)", "formula_000312": "f_n = (1 + i)\\delta_{n0}", "formula_000313": "\\mathcal{E}_n / \\sqrt{E_n \\tilde{E}_n}", "formula_000317": "38\\%", "formula_000318": "L=1000", "formula_000319": "\\mathcal{N}(\\mathbf{0}, \\mathbf{R})", "formula_000320": "\\tilde{u}_n", "formula_000321": "\\tilde{\\mu}_n", "formula_000322": "\\Delta t_{\\text{obs}} = \\tau_{15} = 0.002\\tau_0", "formula_000323": "2N", "formula_000324": "\\bm{V}^{(j)}", "formula_000326": "\\Delta t_{\\text{obs}}=\\tau_{4} = 0.2\\tau_0", "formula_000325": "\\bm{H}_{\\mathbb{C}}", "formula_000327": "n = 10", "formula_000328": "\\bm{R}_\\mathbb{C}", "formula_000329": "\\hat{\\bm{\\Sigma}}(t_{\\text{obs}}) = \\frac{1}{L-1} \\sum_{j=1}^L \\left[\\hat{\\bm{U}}^{(j)} - \\hat{\\bm{\\mu}}\\right] \\left[\\hat{\\bm{U}}^{(j)} - \\hat{\\bm{\\mu}}\\right]^{T}\\ ,", "formula_000331": "\\lambda_{1}=\\lambda_{8}=\\lambda_{12}=\\lambda_{14}=\\lambda_{15}=0.01", "formula_000330": "\\mathcal{E}_n \\approx 2 E_n", "formula_000333": "m", "formula_000332": "\\mathcal{E}_n/\\sqrt{\\tilde{E}_n E_n}", "formula_000335": "\\sigma_{mn}", "formula_000334": "K_{nm}\\left[\\bm{Z} - \\bm{H}\\hat{\\bm{U}}^{(j)} - \\bm{V}^{(j)}\\right]_m", "formula_000336": "|u_{13}|^2", "formula_000337": "5.11 \\pm 0.85", "formula_000338": "2^5 = 32", "formula_000341": "\\langle \\cdot \\rangle_L", "formula_000340": "0.04 \\pm 0.03", "formula_000339": "\\begin{eqnarray}        \\Pi_n = \\Im\\left[a\\, k_{n+1} u^*_n u^*_{n+1} u_{n+2} - c\\, k_n u^*_{n-1} u^*_n u_{n+1}\\right]\\,.\\end{eqnarray}", "formula_000342": "\\nu = 10^{-6}", "formula_000343": "I_n\\frac{dy_n}{dt} + y_n\\frac{dI_n}{dt} + \\nu k_n^2 I_n y_n = G_n[\\bm{I} \\circ \\bm{y}] + f_n(t)\\ .", "formula_000344": "i", "formula_000345": "t_k", "formula_000346": "u_{-2} = u_{-1} = u_{N} = u_{N+1} = 0", "formula_000347": "\\frac{\\Re(u_n-\\tilde{u}_n)^2}{\\sqrt{\\langle\\Re(u_n)^2\\rangle_T}\\sqrt{\\langle\\Re(\\tilde{u}_n)^2\\rangle_T}}", "formula_000348": "\\tau_n = \\frac{1}{k_n\\sqrt{\\langle{|u_n|^2}\\rangle_T}}\\ .", "formula_000349": "y_n", "formula_000350": "\\bm{H}", "formula_000352": "n=6", "formula_000353": "\\{\\hat{\\bm{u}}^{(1)}, \\hat{\\bm{u}}^{(2)}, \\ldots, \\hat{\\bm{u}}^{(L)}\\} \\in \\mathbb{C}^{N \\times L}", "formula_000351": "n < 6", "formula_000355": "\\Re(u_{13}) > 0", "formula_000354": "17.76 \\pm 8.35", "formula_000356": "8.13 \\pm 0.98", "formula_000357": "\\tau_{15} = 0.002 \\tau_0", "formula_000358": "\\Delta\\mathcal{E}^\\Pi", "formula_000359": "13", "formula_000360": "n=1", "formula_000362": "\\tilde{\\Sigma}_{nn}", "formula_000361": "n = 1,2,3", "formula_000363": "\\bm{u}", "formula_000364": "T_n(\\bm{u}, t)", "formula_000365": "\\Delta t_{\\text{obs}} = \\tau_{9}", "formula_000366": "n_{\\text{seq}}", "formula_000367": "27.71 \\pm 4.01", "formula_000368": "y_n\\frac{dI_n}{dt} + \\nu k_n^2 I_n y_n = 0\\ ,", "formula_000369": "\\bm{K}(t_{\\text{obs}}) = \\hat{\\bm{\\Sigma}}(t_{\\text{obs}})\\,\\bm{H}^{T} \\left(\\bm{H} \\hat{\\bm{\\Sigma}}(t_{\\text{obs}}) \\bm{H}^{T} + \\bm{R}\\right)^{-1}\\ ,", "formula_000370": "\\tilde{E}_n = \\langle |\\tilde{u}_n|^2 \\rangle_T", "formula_000371": "\\hat{\\bm{\\Sigma}}_p = \\langle(\\hat{\\bm{u}} - \\langle \\hat{\\bm{u}} \\rangle_L)(\\hat{\\bm{u}} - \\langle \\hat{\\bm{u}} \\rangle_L)^{T}\\rangle_L", "formula_000374": "n = 0,1,2", "formula_000373": "u_n(t) = y_n(t) I_n(t)\\ ,", "formula_000375": "\\mathcal{E}_{\\Pi}\\pm\\Delta\\mathcal{E}_{\\Pi}", "formula_000377": "\\nu", "formula_000372": "\\lambda_{3}=0.1", "formula_000376": "\\lambda_{3}\\!=\\!\\lambda_{4}\\!=\\!\\lambda_{5}\\!=\\!\\lambda_{7}\\!=\\!\\lambda_{9}\\!=\\!\\lambda_{13}\\!=\\!\\lambda_{14}\\!=\\!0.25", "formula_000378": "\\hat{u}_n^{(j)}(t_0)", "formula_000379": "L = 160", "formula_000380": "\\Delta t_{\\text{obs}}=\\tau_{7} = 0.06\\tau_0", "formula_000382": "n = 6,7,8", "formula_000381": "7.94 \\pm 0.43", "formula_000383": "7.75 \\pm 0.17", "formula_000384": "\\begin{eqnarray}\\tilde{\\bm{U}}^{(j)}(t_{\\text{obs}}) &=& \\hat{\\bm{U}}^{(j)}(t_{\\text{obs}})  \\\\&&\\hspace{-5em} +\\!\\bm{K}(t_{\\text{obs}})\\! \\left[ \\bm{Z}(t_{\\text{obs}})\\! -\\!\\bm{H} \\hat{\\bm{U}}^{(j)}(t_{\\text{obs}}) \\!-\\! \\bm{V}^{(j)}(t_{\\text{obs}}) \\right]\\,,\\end{eqnarray}", "formula_000385": "\\sim90\\%", "formula_000388": "\\omega(t)", "formula_000386": "\\sigma_E = \\delta E/\\sqrt{4\\ln(2)}", "formula_000389": "I(\\mathbf{r},t)", "formula_000387": "\\begin{align}   & \\tau'-\\tau'_0 = \\tau - \\tau_0 + \\frac{[v(E)-v_0]\\theta_{\\text{prop}}}{v_0},  \\\\    &E'-E_0' = E - E_0, \\end{align}", "formula_000390": "v_{2\\hbar\\omega_0}=v(E_0+2\\hbar\\omega_0)", "formula_000391": "n", "formula_000392": "\\omega(t) = \\omega_{0}\\left[1 + m\\cos{\\left(\\Omega t\\right)}\\right] + 2a t,", "formula_000393": "n=11", "formula_000394": "\\mathbf{f}_0(\\mathbf{r},\\omega)", "formula_000395": "\\omega_0", "formula_000400": "\\hat{H}_{\\text{int}}", "formula_000401": "W(E,\\tau)", "formula_000396": "\\varphi''=-\\alpha", "formula_000399": "\\begin{split}    \\tilde{\\mathcal{E}}(\\omega)\\propto\\\\ \\exp\\left[-\\frac{(\\omega - \\omega_0)^2}{2\\sigma^2_\\omega} - i\\frac{\\varphi''(\\omega - \\omega_0)^2}{2}-i\\frac{\\varphi'''(\\omega-\\omega_0)^3}{6}\\right]\\end{split}", "formula_000398": "\\omega(\\tau)", "formula_000397": "(\\omega_0 - \\Delta\\omega/2; \\omega_0 + \\Delta\\omega/2 )", "formula_000402": "\\sigma_\\omega = \\sigma_E/\\hbar", "formula_000403": "v_0", "formula_000406": "\\omega_n = \\omega_0-\\Delta\\omega (1/2 - n/N))", "formula_000407": "g = 1.75", "formula_000405": "\\mathbf{A}", "formula_000404": "z_{\\text{com}} \\approx \\frac{m_e v^2 \\alpha}{\\hbar(n-1)}", "formula_000408": "g", "formula_000409": "\\delta E = 0.5\\,\\mathrm{eV}", "formula_000411": "\\tau_{\\text{com}}", "formula_000410": "30\\,\\mathrm{keV}", "formula_000412": "\\varphi'''", "formula_000413": "S(E,\\tau) = W(E,\\tau)*\\exp{[-\\tau^2/\\tau_s^2]},", "formula_000414": "e>0", "formula_000415": "\\tau = t - z/v_0", "formula_000416": "\\tau_{\\text{pe}}", "formula_000418": "\\tau_0", "formula_000417": "\\beta", "formula_000419": "\\mathbf{A}(\\mathbf{r},t) = (-ic/\\omega)\\mathbf{E}(\\mathbf{r},t) + c.c.", "formula_000420": "N", "formula_000422": "z", "formula_000423": "\\mathbf{v}_0", "formula_000421": ". The compression occurs at", "formula_000424": "\\Omega = \\omega/20", "formula_000425": "\\textbf{E}_i(\\mathbf{r}, t) = \\int_0^\\infty \\textrm{d}\\omega\\,\\sqrt{I(\\omega)}e^{i\\varphi(\\omega)}e^{-i\\omega( t+ \\mathbf{s_i}\\cdot \\mathbf{r}/c)},", "formula_000426": "W_0(E,t)", "formula_000427": "\\hbar \\omega", "formula_000428": "\\alpha = 328\\,\\mathrm{fs^2}", "formula_000430": "\\hbar \\omega(\\tau)", "formula_000431": "H_0", "formula_000429": "\\varphi'''=0", "formula_000432": "\\tau_{\\text{opt}}", "formula_000433": "\\approx9.9\\,\\mathrm{fs}", "formula_000434": "p_0", "formula_000436": "f_{0z}", "formula_000437": "\\tau_s \\approx 5\\,\\mathrm{fs}", "formula_000435": "\\begin{split}    U(z,t) =\\\\ \\exp{\\left\\{-i2 \\text{Im}\\int_0^\\infty \\mathrm{d}\\omega\\, g(\\omega)\\sqrt{I(\\omega)}    e^ {i \\varphi(\\omega) }e^{-i\\omega(t-z/v_0)} \\right\\}},\\end{split}", "formula_000438": "\\phi(\\textbf{r},t) = \\left[-\\frac{1}{\\hbar}\\int^{t}_{-\\infty} \\hat{H}_{\\text{int}}(\\textbf{r}+\\textbf{v}_0(\\theta-t),\\theta)\\,\\mathrm{d}\\theta\\right],", "formula_000439": "\\beta = 256\\,\\mathrm{fs^3}", "formula_000441": "E_0", "formula_000440": "\\mathbf{p}_0=(0,0,p_0)", "formula_000442": "\\sqrt{I(t)}\\propto \\left[1+\\cos{\\left(\\Omega t\\right)}\\right]/2", "formula_000443": "\\rho_0(t,t^*)", "formula_000444": "\\hat{\\mathbf{p}}", "formula_000445": "\\mathcal{N}_E", "formula_000447": "\\rho_2(\\tau,\\tau^*) = U(\\tau)\\rho_1(\\tau,\\tau^*)U^{\\dagger}(\\tau^*),", "formula_000448": "M", "formula_000449": "(t - \\mathbf{s}\\cdot \\mathbf{r}/c)", "formula_000446": "0.5 \\,\\mathrm{eV}", "formula_000450": "\\approx", "formula_000451": "I(\\omega)", "formula_000452": "W_1(E,\\tau)", "formula_000453": "I(\\tau)", "formula_000454": "\\theta_{\\text{com}}", "formula_000455": "\\tau", "formula_000456": "\\tau_\\text{coh} = 3.65\\,\\mathrm{fs}", "formula_000457": "\\tau_\\text{coh}=\\hbar /\\delta E\\approx", "formula_000458": "a", "formula_000459": "m", "formula_000460": "\\tilde{\\mathcal{E}}(\\omega)=\\sqrt{I(\\omega)}e^{i\\varphi(\\omega)}", "formula_000461": "\\mathcal{N}_{w}", "formula_000462": "g(x,y,\\omega) = \\frac{e}{\\hbar\\omega} \\int_{-\\infty}^{z}\\mathrm{d}z'\\,f_{0z}(x,y,z';\\omega)e^{i\\omega z' /v_0}.", "formula_000464": "H_\\text{int}(\\mathbf{r},t)=e\\mathbf{p_0}\\cdot\\textbf{A}(\\mathbf{r},t)/m_e", "formula_000463": "S(E,\\tau)", "formula_000466": "w_{\\tau_0}", "formula_000465": "\\theta", "formula_000467": "\\rho_1(\\tau,\\tau^*)", "formula_000468": "E(\\tau) + 2\\hbar\\omega(\\tau)", "formula_000471": "\\varphi''=-\\alpha/n", "formula_000470": "1.2\\,\\mathrm{eV}", "formula_000472": "20\\%", "formula_000473": "g(\\omega)", "formula_000469": "\\textbf{E}_i(\\mathbf{r}, t) = \\frac{\\Delta\\omega}{N}\\sum_{n=1}^{N} \\tilde{\\mathcal{E}}(\\omega_n)e^{-i\\omega_n( t+ \\mathbf{s_i}\\cdot \\mathbf{r}/c)},", "formula_000474": "\\beta = 0", "formula_000475": "\\Delta \\omega=", "formula_000477": "E_0\\gg\\hbar\\omega", "formula_000476": "\\tau_0 = \\sqrt{\\tau_{\\text{coh}}^2 + \\tau_{\\text{pe}}^2}", "formula_000478": "\\varphi'' = -\\alpha", "formula_000479": "g=1", "formula_000480": "\\hat{H}_0=\\hat{\\textbf{p}}^2/2m_{e}", "formula_000481": "0<m\\leq1", "formula_000482": "\\mathbf{s}_i", "formula_000483": "m = 0.25", "formula_000485": "\\alpha", "formula_000484": "\\rho(\\tau,\\tau^*) = \\sum_{\\tau_0} w_{\\tau_0} \\psi(\\tau - \\tau_0) \\psi^{\\dagger}(\\tau^*-\\tau_0),", "formula_000486": "\\Omega", "formula_000487": "\\Delta E", "formula_000488": "\\begin{split}    \\tilde{\\mathcal{E}}(\\omega)\\propto \\sum_{k=-1}^{k=1} \\sum_{n=-\\infty}^{\\infty} (1/2)^{|k|}i^nJ_n \\left(\\frac{m\\omega_0}{\\Omega}\\right)\\times \\\\  \\exp\\left[-\\frac{(\\omega - \\omega_{0,kn})^2}{2\\sigma^2_\\omega} - i\\frac{\\varphi''(\\omega - \\omega_{0,kn})^2}{2}\\right] ,\\end{split}", "formula_000489": "\\tilde \\psi(E) = \\mathcal{N}_E e^{ \\left[-\\frac{(E-E_0)^2}{2\\sigma_E^2}\\right]}e^{ \\left[-i\\frac{\\alpha (E-E_0)^2}{2\\hbar^2}\\right]}e^{ \\left[-i\\frac{\\beta (E-E_0)^3} {6 \\hbar^3}\\right]},", "formula_000491": "\\varphi(\\mathbf{r},t)", "formula_000490": "z_{\\text{com}}=\\frac{m_e v_{2\\hbar\\omega}^2\\alpha}{\\hbar}\\approx 21\\, \\mathrm{cm}", "formula_000492": "z_{\\text{prop}}", "formula_000496": "E_0+2.4", "formula_000493": "with the duration of the compressed pulse of", "formula_000494": "\\omega", "formula_000495": "\\alpha =321 \\,\\mathrm{fs^2}", "formula_000497": "\\tau_{\\text{chirp}}", "formula_000498": "\\omega_{0} = 1.8 \\,\\mathrm{fs^{-1}}", "formula_000499": "\\tau_{\\text{el}} = 242", "formula_000501": "\\delta E", "formula_000502": "1/2\\pi\\Omega", "formula_000500": "g(\\omega)\\approx g", "formula_000503": "N=100", "formula_000504": "\\varphi''' = -\\beta/2=128\\,\\mathrm{fs^3}", "formula_000505": "\\delta E=", "formula_000506": "\\tau_{\\text{com}}\\approx 6\\,\\mathrm{fs}", "formula_000507": "\\alpha = \\beta = 0", "formula_000508": "U(\\tau) = \\exp{\\left\\{-i2 |g|\\sqrt{I(\\tau)}    \\sin{[\\varphi(\\tau)+\\arg{g}]} \\right\\}}.", "formula_000509": "z_{\\text{com}} \\approx 32\\,\\mathrm{cm}", "formula_000510": "\\theta_{\\text{prop}}", "formula_000511": "J_n", "formula_000512": "[Fig. (f,g)]. The structured side-band contains around", "formula_000515": "\\tau_\\text{coh}", "formula_000514": "W_2(E,\\tau)", "formula_000513": "\\begin{split}    \\tilde{\\mathcal{E}}(\\omega)\\propto\\sum_{k=-1}^{k=1} \\sum_{n=-\\infty}^{\\infty} (1/2)^{|k|}i^nJ_n \\left(\\frac{m\\omega_0}{\\Omega}\\right)\\times \\exp\\left[-\\frac{(\\omega - \\omega_{0,kn})^2}{2\\sigma^2_\\omega} - i\\frac{\\varphi''(\\omega - \\omega_{0,kn})^2}{2}\\right]\\end{split}", "formula_000516": "5\\,\\mathrm{keV}", "formula_000517": "\\tilde{\\mathcal{E}}(\\omega)\\propto\\exp\\left[-\\frac{(\\omega - \\omega_0)^2}{2\\sigma^2_\\omega} - i\\frac{\\varphi''(\\omega - \\omega_0)^2}{2}\\right],", "formula_000519": "\\tau = t-z/v_0", "formula_000520": "\\omega_{0}", "formula_000521": "E \\approx E_0 + v_0(p-p_0)", "formula_000518": "W_3(E,\\tau,\\theta_{\\text{prop}}) = W_2(E',\\tau')", "formula_000522": "\\varphi(\\omega)", "formula_000524": "\\hat{H}_0", "formula_000523": "\\tau_{\\text{com}} \\approx \\tau_{\\text{0}}/(n-1)", "formula_000526": "z_{\\text{el}}", "formula_000525": "\\mathbf{v}_0=(0,0,v_0)", "formula_000527": "\\alpha = 0 = \\beta", "formula_000528": "\\hbar\\omega(\\tau)", "formula_000529": "\\tau^*", "formula_000530": "w_{\\tau_0} = \\mathcal{N}_{w}\\exp{ \\left[ -\\frac{8\\ln{2}\\tau_0^2}{\\tau_{\\text{pe}}^2} \\right]},", "formula_000531": "v_0 = 0.14c", "formula_000532": "E_0/\\hbar", "formula_000533": "\\tau_\\text{pe} \\approx 9.30 \\,\\mathrm{fs}", "formula_000534": "W_2(E,t)", "formula_000535": "\\tau_\\text{el} = 250\\,\\mathrm{fs}", "formula_000536": "\\tau_0 = 9.9\\,\\mathrm{fs}", "formula_000538": "\\tau_{\\text{el}} = \\sqrt{\\tau_{0}^2 + \\tau^2_{\\text{chirp}}}", "formula_000537": "\\varphi(\\textbf{r},t) = \\omega_0(t-\\mathbf{s}_i\\cdot \\mathbf{r}/c) + a(t-\\mathbf{s}_i\\cdot \\mathbf{r}/c)^2 + ...", "formula_000539": "\\psi(\\tau)=\\mathcal{F}\\left\\{\\tilde{\\psi}(E)\\right\\}", "formula_000540": "\\rho(\\tau,\\tau^*)", "formula_000541": "\\omega_{0,kn}=\\omega_0 - (k+n)\\Omega", "formula_000543": "v[E(p)]=\\partial E(p)/\\partial p", "formula_000542": "\\textbf{E}_i(\\mathbf{r}, t) \\propto \\sqrt{I(\\mathbf{r},t)} \\exp{\\left[-i\\varphi(\\mathbf{r},t)\\right]}.", "formula_000544": "\\tau_0 = 50\\,\\mathrm{fs}", "formula_000545": "4\\%", "formula_000546": "\\textbf{E}(\\mathbf{r}, t) = \\int_0^\\infty \\textrm{d}\\omega\\,\\sqrt{I(\\omega)}e^{i\\varphi(\\omega)}\\mathbf{f}_0(\\mathbf{r},\\omega)e^{-i\\omega t},", "formula_000547": "[v(E)-v_0] \\propto E", "formula_000551": "\\varphi'' = -\\beta/2", "formula_000548": "\\hat{H} = \\frac{1}{2m_{e}}\\left( \\hat{\\mathbf{p}}+e\\mathbf{A}\\right)^{2}=\\hat{H}_{0}+\\hat{H}_{\\text{int}},", "formula_000549": "\\tilde{\\mathcal{E}}(\\omega)\\propto    \\exp\\left[-\\frac{(\\omega - \\omega_0)^2}{2\\sigma^2_\\omega} - i\\frac{\\varphi''(\\omega - \\omega_0)^2}{2}-i\\frac{\\varphi'''(\\omega-\\omega_0)^3}{6}\\right],", "formula_000550": "\\hat{U}(\\textbf{r},t) = e^{i\\phi(\\textbf{r},t)}", "formula_000552": "n", "formula_000553": "\\mathbf{Q}^\\text{ref}(t^n)", "formula_000554": "v_x(y=1)", "formula_000555": "y^+ = 180y", "formula_000557": "T=10", "formula_000558": "\\mathbf{O}_i", "formula_000559": "\\mathbf{m}(\\mathbf{v}, t) = \\sum_{i=1}^{N_Q} \\tau_i(t) \\mathbf{O}_i(\\mathbf{v}),", "formula_000560": "\\mathfrak{S}^d_{ij} = \\frac{1}{2}(g_{ik}g_{kj}+g_{jk}g_{ki}).", "formula_000562": "R_{[l,m]}\\mathbf{u}", "formula_000561": "q^{n^*}", "formula_000564": "4\\pi \\times 4 \\times 4/3\\pi", "formula_000563": "\\hat{Z}[l, m] = \\frac{\\lvert \\Omega \\rvert}{(N_xN_yN_z)^2} \\sum_{\\mathbf{k}} \\hat{R}_{[l, m]}\\hat{\\boldsymbol{\\omega}}_\\mathbf{k} \\cdot \\text{conj}(\\hat{R}_{[l, m]}\\hat{\\boldsymbol{\\omega}}_\\mathbf{k}).", "formula_000566": "[11, 17]", "formula_000569": "dQ^{n-1}", "formula_000568": "\\lambda", "formula_000567": "Re_\\text{int}", "formula_000565": "\\mathbf{q}^{n}", "formula_000570": "t=75", "formula_000571": "\\bar{\\mathbf{u}}", "formula_000573": "q^{n-1}", "formula_000575": "(1, 0, 0)^T", "formula_000574": "0.1", "formula_000572": "\\lVert R_{[4,10]}\\bar{\\mathbf{u}}\\rVert", "formula_000576": "\\nabla \\cdot \\mathbf{u} = 0,", "formula_000577": "\\mathbf{u}_1", "formula_000578": "\\lVert \\boldsymbol{k} \\rVert \\leq C_F", "formula_000579": "C_s = 0.071", "formula_000580": "\\frac{\\mathrm{d} Q_i}{\\mathrm{d} t} = \\int_\\Omega \\mathbf{V}_i \\cdot R_i \\bar{P} (\\bar{\\mathbf{F}}(\\mathbf{v}) + \\mathbf{m}(\\mathbf{v})) \\mathrm{d} \\boldsymbol{x}.", "formula_000581": "Z_{[0,6]}", "formula_000583": "q^{n-1^*}", "formula_000582": "Q = \\frac{1}{2}(\\lVert \\Omega \\rVert^2 - \\lVert S \\rVert^2)", "formula_000585": "N_x \\times N_y \\times N_z", "formula_000584": "N", "formula_000586": "\\underline{\\mathbf{q}}_h", "formula_000587": "dQ^n", "formula_000588": "\\bar{\\mathbf{u}} = \\Phi \\mathbf{u},", "formula_000590": "R_i", "formula_000589": "z", "formula_000592": "dQ_i", "formula_000593": "\\Delta", "formula_000591": "\\mathbf{v}(t) \\approx \\bar{\\mathbf{u}}(t).", "formula_000596": "Z_{[16,32]}", "formula_000598": "\\mathbf{q}^{n^*}", "formula_000597": "L_x \\times L_y \\times L_z", "formula_000594": "\\hat{\\boldsymbol{f}}_{\\boldsymbol{k}}(t+\\Delta t_f) = \\hat{\\boldsymbol{f}}_{\\boldsymbol{k}}( t)\\left(1 - \\frac{\\Delta t_f}{T_L}\\right) + W(t)\\left(2 \\sigma^2 \\frac{\\Delta t_f}{T_L}\\right)^{1 / 2},", "formula_000595": "\\int_\\Omega q_i \\frac{\\partial \\boldsymbol{\\varphi}}{\\partial R_i \\mathbf{v}} \\mathrm{d} \\boldsymbol{x}=-\\int_\\Omega \\mathbf{V}_i \\boldsymbol{\\varphi} \\mathrm{d} \\boldsymbol{x}", "formula_000599": "\\tau_i(t)", "formula_000600": "t=4", "formula_000601": "\\lambda = 0", "formula_000602": "\\mathbf{u}(x,y,z) \\approx \\frac{1}{N_x N_y N_z}     \\sum_{k_1 = 0}^{N_x-1} \\sum_{k_2 = 0}^{N_y-1} \\sum_{k_3 = 0}^{N_z-1}    \\exp \\left[2 \\pi i \\left(\\frac{xk_1}{L_xN_x}+\\frac{yk_2}{L_yN_y}+\\frac{zk_3}{L_zN_z}\\right)\\right] \\hat{\\mathbf{u}}_\\mathbf{k},", "formula_000603": "\\mathbf{k}", "formula_000604": "1/\\nu", "formula_000605": "T_j", "formula_000606": "\\mathbf{c}(\\mathbf{u}, \\bar{\\mathbf{u}}) = \\Phi P \\mathbf{F}(\\mathbf{u}) - \\bar{P} \\bar{\\mathbf{F}}(\\bar{\\mathbf{u}})", "formula_000607": "\\int_\\Omega \\mathbf{V}_i \\cdot \\mathbf{O}_j \\, d\\boldsymbol{x} = 0 \\quad \\text{for } i \\neq j.", "formula_000608": "z=0", "formula_000609": "C \\in \\mathbb{R}^{ len(\\underline{\\mathbf{q}}_h) \\times N_Q}", "formula_000611": "2000", "formula_000610": "KS\\Big(F(x),G(x)\\Big) = \\max_x \\Big\\lvert F(x)-G(x) \\Big\\rvert.", "formula_000612": "E_{[16,32]}", "formula_000615": "C_w", "formula_000613": "C", "formula_000617": "\\Delta t_{LF} = 5 \\cdot 10^{-3}", "formula_000614": "2.5 \\cdot 10^{-4}", "formula_000616": "q^n", "formula_000620": "\\bar{\\boldsymbol{F}}", "formula_000618": "\\hat{\\mathbf{u}}", "formula_000622": "N = 512^3", "formula_000619": "0.01", "formula_000621": "E = \\frac{1}{2} \\int_\\Omega \\|\\bar{\\mathbf{u}}\\|^2 \\, \\mathrm{d} \\boldsymbol{x}.", "formula_000623": "t = 4", "formula_000624": "[l, m]", "formula_000625": "\\hat{\\mathbf{u}}_\\mathbf{k}", "formula_000626": "\\mathbf{u} = (u_1, u_2, u_3)^T", "formula_000627": "\\tilde{\\mathbf{f}}", "formula_000628": "u_\\text{avg}", "formula_000629": "dQ_i^n", "formula_000631": "\\sqrt{2}", "formula_000630": "\\mathbf{D}", "formula_000633": "G(x)", "formula_000632": "\\lVert R_{[0,3]}\\bar{\\boldsymbol{\\omega}}\\rVert", "formula_000634": "q^{n-2}", "formula_000635": "\\lVert R_{[4,10]}\\bar{\\boldsymbol{\\omega}}\\rVert", "formula_000636": "\\frac{d\\mathbf{v}}{dt} = \\bar{P} (\\bar{\\mathbf{F}}(\\mathbf{v}) + \\mathbf{m}(\\mathbf{v})),", "formula_000637": "C_w = 0.53", "formula_000638": "E_{[7,15]}", "formula_000639": "[0, 3]", "formula_000642": "\\hat{\\boldsymbol{\\omega}}", "formula_000640": "\\lambda = 0.1", "formula_000643": "\\mathbf{v}^{n^*}", "formula_000644": "\\begin{align}    \\frac{\\mathrm{d} Z_{[l,m]}}{\\mathrm{d} t} &= C \\int_\\Omega \\sum_{\\alpha=1}^3 \\frac{\\partial (R_{[l,m]} {\\omega}_\\alpha  R_{[l,m]} {\\omega}_\\alpha)}{\\partial R_{[l,m]} {\\omega}_\\alpha}  \\frac{\\partial R_{[l,m]} {\\omega}_\\alpha}{\\partial t} \\mathrm{d} \\boldsymbol{x} \\\\    &= C \\int_\\Omega 2 R_{[l,m]} \\boldsymbol{\\omega} \\cdot  \\frac{\\partial R_{[l,m]} ( \\nabla \\times  \\mathbf{v})}{\\partial t} \\mathrm{d} \\boldsymbol{x} \\\\     &= C \\int_\\Omega (2 \\nabla \\times R_{[l,m]}\\boldsymbol{\\omega}) \\cdot \\frac{\\partial R_{[l,m]} \\mathbf{v}}{\\partial t} \\mathrm{d} \\boldsymbol{x},\\end{align}", "formula_000641": "180y", "formula_000645": "R_{[l,m]}", "formula_000646": "\\begin{align}    E &=  \\frac{1}{2} \\int_\\Omega {\\mathbf{u} \\cdot \\mathbf{u}} \\, \\mathrm{d} \\boldsymbol{x} ,\\\\    &\\approx \\frac{1}{2} \\int_\\Omega \\frac{1}{(N_x N_y N_z)^2}     \\sum_{\\mathbf{k}} \\sum_{\\mathbf{l}}    \\exp \\left[2 \\pi i \\left(\\frac{x_1(k_1+l_1)}{L_xN_x}+\\frac{x_2(k_2+l_2)}{L_yN_y}+\\frac{x_3(k_3+l_3)}{L_zN_z}\\right)\\right] \\hat{\\mathbf{u}}_\\mathbf{k}\\cdot     \\hat{\\mathbf{u}}_\\mathbf{l} \\, \\mathrm{d} \\boldsymbol{x} ,\\\\    & = \\frac{1}{2}  \\frac{\\lvert \\Omega \\rvert}{(N_x N_y N_z)^2} \\sum_{\\mathbf{k}} \\hat{\\mathbf{u}}_\\mathbf{k}\\cdot     \\hat{\\mathbf{u}}_\\mathbf{-k},\\\\    & = \\frac{1}{2}  \\frac{\\lvert \\Omega \\rvert}{(N_x N_y N_z)^2} \\sum_{\\mathbf{k}} \\hat{\\mathbf{u}}_\\mathbf{k}\\cdot     \\text{conj}(\\hat{\\mathbf{u}}_\\mathbf{k}),\\end{align}", "formula_000647": "4\\pi \\times 2 \\times 4/3\\pi", "formula_000648": "y = 0", "formula_000649": "[16,32]", "formula_000650": "\\mathbf{F}(\\mathbf{u})", "formula_000651": "q_i", "formula_000652": "L", "formula_000653": "k_f", "formula_000654": "\\mathcal{N}", "formula_000656": "2.5 \\cdot 10^{-3}", "formula_000657": "q^{n-2^*}", "formula_000658": "N_x", "formula_000659": "\\hat{E}    =  \\frac{1}{2} \\frac{\\lvert \\Omega \\rvert}{(N_xN_yN_z)^2} \\sum_{\\mathbf{k}} \\hat{\\mathbf{u}}_\\mathbf{k} \\cdot \\text{conj}(\\hat{\\mathbf{u}}_\\mathbf{k}),", "formula_000660": "e^*", "formula_000661": "\\begin{align}    \\tau_i(t^n) &= \\frac{\\mathrm{d} Q_i^u}{\\mathrm{d} t} \\Big/ \\int_\\Omega \\bar{P} \\mathbf{V}_i \\cdot R_i \\mathbf{O}_i \\mathrm{d} \\boldsymbol{x}, \\\\    \\frac{\\mathrm{d} Q_i^u}{\\mathrm{d} t} &= \\frac{1}{\\Delta t} \\left(Q^\\text{ref}_i(t^n) - Q_i(\\mathbf{v}^{n^*})\\right). \\end{align}", "formula_000663": "Re_\\lambda", "formula_000662": "\\lambda = 0.01", "formula_000665": "N_Q", "formula_000666": "\\bar{P}", "formula_000664": "y=2", "formula_000670": "Q = 2000", "formula_000668": "x", "formula_000667": "i", "formula_000669": "\\mathbf{O}_i = \\sum_{j=1}^{N_Q} c_{i,j}T_j(\\mathbf{v}, \\mathbf{x}), \\quad \\textrm{for}\\;\\;\\; 1 \\leq i \\leq N_Q", "formula_000671": "[0,6]", "formula_000672": "\\sigma^2", "formula_000673": "\\eta", "formula_000674": "\\lVert R_{[0,3]}\\bar{\\mathbf{u}}\\rVert", "formula_000675": "L_x", "formula_000676": "\\mathbf{V}_i = 2 \\nabla \\times R_{[l,m]}\\boldsymbol{\\omega}", "formula_000677": "S_{ij} = 1/2(\\partial v_i/ \\partial x_j + \\partial v_j/ \\partial x_i)", "formula_000678": "256 \\times 128 \\times 128", "formula_000679": "\\Omega", "formula_000681": "\\epsilon", "formula_000680": "1/(4\\pi)", "formula_000682": "\\mathbf{m}", "formula_000683": "\\sigma", "formula_000684": "[7,15]", "formula_000686": "e^* = \\sigma^2 T_L", "formula_000685": "\\begin{align}    \\frac{\\mathrm{d} E_{[l,m]}}{\\mathrm{d} t} &= \\frac{1}{2}C\\int_\\Omega \\sum_{\\alpha=1}^3 \\frac{\\partial (R_{[l,m]} {v}_\\alpha  R_{[l,m]} {v}_\\alpha)}{\\partial R_{[l,m]} {v}_\\alpha}  \\frac{\\partial R_{[l,m]} {v}_\\alpha}{\\partial t} \\mathrm{d} \\boldsymbol{x} \\\\    &= C\\int_\\Omega R_{[l,m]} \\mathbf{v} \\cdot \\frac{\\partial R_{[l,m]} \\mathbf{v}}{\\partial t} \\mathrm{d} \\boldsymbol{x},\\end{align}", "formula_000688": "T=100", "formula_000689": "c_{i,j}", "formula_000691": "Z_{[7,15]}", "formula_000687": "\\hat{\\boldsymbol{f}}_{\\boldsymbol{k}}", "formula_000693": "y", "formula_000690": "Z = \\int_\\Omega \\|\\bar{\\boldsymbol{\\omega}}\\|^2 \\, \\mathrm{d}\\boldsymbol{x} = \\int_\\Omega \\|\\nabla \\times \\bar{\\mathbf{u}}\\|^2 \\, \\mathrm{d}\\boldsymbol{x},", "formula_000692": "\\hat{E}[l, m]    =  \\frac{1}{2} \\frac{\\lvert \\Omega \\rvert}{(N_xN_yN_z)^2} \\sum_{\\mathbf{k}} \\hat{R}_{[l, m]}\\hat{\\mathbf{u}}_\\mathbf{k} \\cdot \\text{conj}(\\hat{R}_{[l, m]}\\hat{\\mathbf{u}}_\\mathbf{k}).", "formula_000694": "\\Delta t_\\text{LF} = 2.5 \\cdot 10^{-3}", "formula_000695": "T_j = V_j", "formula_000696": "\\Delta t = 2.5 \\cdot 10^{-4}", "formula_000697": "512^3", "formula_000698": "\\boldsymbol{\\varphi}", "formula_000699": "p", "formula_000700": "\\Delta t", "formula_000701": "t_\\text{int}", "formula_000702": "\\frac{d\\bar{\\mathbf{u}}}{dt} = \\bar{P} \\bar{\\mathbf{F}}(\\bar{\\mathbf{u}}) + \\mathbf{c}(\\mathbf{u}, \\bar{\\mathbf{u}}),", "formula_000703": "R_i \\mathbf{v}", "formula_000704": "E_{[0,6]}", "formula_000705": "c_{ii} = 1", "formula_000707": "64 \\times 64 \\times 32", "formula_000708": "Q = \\int_{\\Omega} q(\\mathbf{v}) \\mathrm{d} \\boldsymbol{x}.", "formula_000709": "Q_i(\\mathbf{v}^{n^*})", "formula_000713": "\\mathbf{Q}", "formula_000714": "\\mathbf{u}", "formula_000711": "W(t)", "formula_000712": "\\begin{align}    \\mathbf{v}^{n^*} &= S(\\mathbf{v}^{n-1}), \\\\    \\mathbf{v}^{n} &= \\mathbf{v}^{n^*} + \\int_{t^n-1}^{t^{n}} \\mathbf{m}(\\mathbf{v}^{n^*}) \\mathrm{d} t, \\\\    &= \\mathbf{v}^{n^*} + \\mathbf{M}(\\mathbf{v}^{n^*}),\\end{align}", "formula_000715": "P", "formula_000710": "\\frac{\\mathrm{d} Q_i}{\\mathrm{d} t} =  \\frac{\\mathrm{d} Q_i^r}{\\mathrm{d} t} +  \\frac{\\mathrm{d} Q_i^u}{\\mathrm{d} t}     =    \\frac{\\mathrm{d} Q_i^r}{\\mathrm{d} t} +  \\tau_i(t)     \\int_\\Omega \\bar{P} \\mathbf{V}_i \\cdot R_i \\mathbf{O}_i \\mathrm{d} \\boldsymbol{x}.", "formula_000717": "\\begin{align}    \\mathbf{m}(\\mathbf{v}, C_w) &= \\nabla \\cdot (2 \\nu_t S_{ij}), \\\\    \\nu_t &=  C_w^2 \\Delta^2 \\frac{(\\mathfrak{S}^d_{ij} \\mathfrak{S}^d_{ij})^{3/2}}{(S_{ij} S_{ij})^{5/2}+(\\mathfrak{S}^d_{ij} \\mathfrak{S}^d_{ij})^{5/4}},\\end{align}", "formula_000716": "\\mathbf{V}_i = R_{[l,m]} \\mathbf{v}", "formula_000718": "\\mathbf{Q}_h", "formula_000719": "N = 64^3", "formula_000720": "L_x \\times L_y \\times L_z = 4\\pi \\times 2 \\times 4/3\\pi", "formula_000721": "\\lvert \\Omega \\rvert", "formula_000722": "\\tau_i", "formula_000723": "t^n", "formula_000724": "\\frac{\\mathrm{d} Q_i}{\\mathrm{d} t} =     \\int_\\Omega \\frac{{\\mathrm{d} q_i(\\mathbf{v})}}{\\mathrm{d} t} \\mathrm{d} \\boldsymbol{x}    =    \\int_\\Omega \\mathbf{V}_i \\cdot \\frac{{\\partial R_i \\mathbf{v}}}{\\partial t} \\mathrm{d} \\boldsymbol{x}.", "formula_000725": "\\lambda = 10^{-4}", "formula_000726": "\\begin{align}    \\mathbf{m}(\\mathbf{v}, C_s) &= \\nabla \\cdot (2 \\nu_t S_{ij}), \\\\    \\nu_t &= C_s^2 \\Delta^2 \\sqrt{2 S_{ij}S_{ij}}.\\end{align}", "formula_000727": "F(x)", "formula_000728": "\\mathbf{v}", "formula_000729": "dQ", "formula_000730": "[4, 10]", "formula_000731": "\\nu", "formula_000732": "C_s = 0.13", "formula_000734": "\\tau_i(t^n)", "formula_000735": "\\begin{align}        \\frac{d\\mathbf{u}}{dt} &= P \\mathbf{F}(\\mathbf{u}), \\\\    \\mathbf{F}(\\mathbf{u})&=-\\mathbf{G} (\\mathbf{u} \\mathbf{u}^T) + \\nu \\mathbf{D} \\mathbf{u} + \\mathbf{f},\\end{align}", "formula_000733": "dQ^{n-2}", "formula_000737": "y^+", "formula_000738": "\\begin{align}        \\mathbf{LRS}(\\underline{\\mathbf{q}}_h) = \\underline{\\mathbf{q}}_h C + \\boldsymbol{\\eta}, \\quad \\boldsymbol{\\eta} \\sim \\mathcal{N}(\\boldsymbol{\\mu}, \\Sigma), \\\\        \\underline{\\mathbf{q}}_h = [\\mathbf{q}^{n-1}, \\dots, \\mathbf{q}^{n-h}, \\mathbf{q}^{n^*}, \\mathbf{q}^{n-1^*}, \\dots, \\mathbf{q}^{n-h^*}, 1],     \\end{align}", "formula_000736": "5 \\cdot 10^{-4}", "formula_000741": "Q^n", "formula_000740": "h", "formula_000739": "Re_\\tau = 180", "formula_000742": "\\lVert R_{[11,17]}\\bar{\\boldsymbol{\\omega}}\\rVert", "formula_000743": "\\mathbf{G}", "formula_000744": "\\lVert R_{[11,17]}\\bar{\\mathbf{u}}\\rVert", "formula_000745": "t=100", "formula_000746": "512 \\times 512 \\times 256", "formula_000747": "T_L", "formula_000748": "\\mathbf{V}_i", "formula_000749": "Q = 100", "formula_000750": "C_s", "formula_000751": "\\underline{\\mathbf{q}}_h^n", "formula_000752": "\\Delta t_f", "formula_000753": "\\frac{\\partial \\mathbf{u}}{\\partial t} + \\nabla \\cdot (\\mathbf{u} \\mathbf{u}^T) = -\\nabla p + \\nu \\nabla^2 \\mathbf{u} + \\mathbf{f},", "formula_000754": "R_{[l,m]}\\boldsymbol{\\omega}", "formula_000755": "1 \\leq t \\leq 2.5", "formula_000758": "\\mathbf{M}", "formula_000756": "S", "formula_000757": "Re_\\tau", "formula_000761": "g_{ij} = \\partial v_i / \\partial x_j", "formula_000759": "C=\\lvert \\Omega \\rvert/(N_xN_yN_z)^2", "formula_000762": "\\mathbf{f}", "formula_000760": "\\mathbf{O}_i(\\mathbf{v})", "formula_000765": "V'_1 (x_1) = A \\tilde{b}_0 \\left( \\frac{x_1}{\\lambda R_1} \\right) \\cdot \\sum_{n=-(N-1)/2}^{(N-1)/2} b_1 (x_1 - n d_1).", "formula_000763": "\\begin{eqnarray} \\frac{\\bar{m}\\lambda'R_3}{d_2}=\\frac{\\left(\\bar{m}+1\\right)\\lambda R_3}{d_2},   \\end{eqnarray}", "formula_000764": "V'_3 (x_3) = A\\sum_{n=-(N-1)/2}^{(N-1)/2} b_1 \\left(\\frac{x_3 R_2}{R_3} - \\frac{\\bar{m} \\lambda R_2}{d_2} - n d_1 \\right),", "formula_000766": "d_1 = \\frac{\\lambda R_2 }{d_2 N}.", "formula_000767": "\\tau_2(x_2) = e^{i2\\pi \\bar{m} \\frac{ x_2}{d_2}}.", "formula_000768": "M+1", "formula_000769": "d_2=20  \\mu", "formula_000770": "M=50", "formula_000771": "V_0 (x_0) = A b_0 (x_0)", "formula_000772": "d_3", "formula_000773": "b_0(x_0)", "formula_000776": "\\Delta L=6118.2 \\mu", "formula_000774": "F=\\frac{R_2d_3} {R_2d_3-R_3d_1}>1,", "formula_000775": "\\begin{eqnarray}&&V'_3(x_3) = Ae^{-\\left(\\frac{\\pi w_2 x_3}{\\lambda R_3}\\right)^2}\\sum_{n=-(N-1)/2}^{(N-1)/2}e^{-\\left(\\frac{\\pi w_0 n d_1}{\\lambda R_1}\\right)^2}\\\\&&\\cdot \\sum_{m=-M/2}^{M/2}e^{-\\left(\\frac{\\pi w_1 m d_2}{\\lambda R_2}\\right)^2}  e^{-i \\frac{2\\pi m d_2 }{\\lambda R_3}\\left(x_3 - \\frac{n d_1 R_3}{  R_2}-\\frac{ \\bar{m} \\lambda R_3}{d_2}\\right)}. \\end{eqnarray}", "formula_000777": "w_2", "formula_000778": "\\Delta \\lambda = \\frac{d_3 d_2}{\\bar{m} R_3}.", "formula_000779": "\\Delta \\lambda' = \\frac{\\lambda}{\\bar{m} N^2} = \\frac{\\lambda^2}{\\Delta L N^2},", "formula_000780": "1", "formula_000782": "\\lambda", "formula_000783": "d_1", "formula_000784": "8", "formula_000781": "n_{s}", "formula_000786": "10 log_{10} N", "formula_000785": "FSR' = \\frac{d_1 d_2}{\\bar{m}R_2},", "formula_000787": "6", "formula_000789": "F=19", "formula_000788": "\\tilde{b}_1 \\left( \\frac{x_2 }{\\lambda R_2}\\right)", "formula_000790": "N", "formula_000791": "d_1 = 15   \\mu", "formula_000792": "n_{eff}", "formula_000793": "144", "formula_000794": "b_1\\left(x_3  \\right)", "formula_000795": "\\begin{eqnarray}  FSR=\\lambda' -\\lambda=\\frac{\\lambda}{\\bar{m}}.   \\end{eqnarray}", "formula_000796": "F=N+1", "formula_000797": "A \\tilde{b}_0 (x_1 / \\lambda R_1)", "formula_000798": "\\delta(x_2)", "formula_000799": "V'_1 (x_1) = A  \\sum_{n=-(N-1)/2}^{(N-1)/2} \\tilde{b}_0 \\left( \\frac{n d_1}{\\lambda R_1} \\right) b_1 (x_1 - n d_1),", "formula_000801": "x_3= R_3 d_1/R_2", "formula_000802": "\\Delta \\lambda' = \\frac{\\Delta \\lambda}{N+1}.", "formula_000800": "\\begin{eqnarray}V_3(x_3) = A b_1\\left(\\frac{x_3 R_2}{R_3}\\right)  * \\sum_{m=-M/2}^{M/2} e^{-i \\frac{2\\pi m d_2 }{\\lambda R_3}\\left(x_3 - \\frac{ \\bar{m} \\lambda R_3}{d_2}\\right)}\\\\ =A b_1\\left(\\frac{x_3 R_2}{R_3}\\right)   * \\frac{\\sin\\left[ \\left(M+1\\right)\\frac{\\pi d_2 }{\\lambda R_3}\\left(x_3 - \\frac{ \\bar{m} \\lambda R_3}{d_2}\\right)\\right]}{\\sin\\left[ \\frac{\\pi d_2 }{\\lambda R_3}\\left(x_3 - \\frac{ \\bar{m} \\lambda R_3}{d_2}\\right)\\right]},\\end{eqnarray}", "formula_000803": "M", "formula_000805": "N=7", "formula_000804": "7", "formula_000808": "n_{eff}=2.279", "formula_000811": "FSR' = \\frac{d_1 d_2}{\\bar{m}R_2}< FSR=\\frac{\\lambda}{\\bar{m}}.", "formula_000807": "V'_1 (x_1) = A\\sum_{n=-(N-1)/2}^{(N-1)/2} b_1 (x_1 - n d_1).", "formula_000809": "n_{s}=2.039", "formula_000810": "\\begin{eqnarray}    \\tau'_2(x_2)= \\sum_{m=-M/2}^{M/2} \\delta (x_2 - m d_2) e^{i 2\\pi m \\Delta L/\\lambda} \\\\    =\\sum_{m=-M/2}^{M/2} \\delta (x_2 - m d_2)\\cdot \\tau_2(x_2).\\end{eqnarray}", "formula_000806": "d_2", "formula_000813": "=N \\cdot", "formula_000812": "d_3= 5 \\mathrm{\\mu m}", "formula_000814": "<", "formula_000815": "\\Delta \\lambda'= 8", "formula_000816": "b_1(x_1)", "formula_000817": "R_1", "formula_000818": "d_3 = 31 \\mathrm{\\mu m}", "formula_000819": "b_1 (x_3) = 1", "formula_000820": "b_2(x_2)", "formula_000821": "i", "formula_000823": "N \\Delta \\lambda ' \\le", "formula_000822": "1 \\times N", "formula_000825": "x_3/\\lambda R_3", "formula_000824": "\\begin{eqnarray}&&V'_3(x_3) = A  b_1\\left(\\frac{x_3 R_2}{R_3}\\right) \\\\&&*  \\sum_{n=-(N-1)/2}^{(N-1)/2}\\sum_{m=-M/2}^{M/2} e^{-i \\frac{2\\pi m d_2 }{\\lambda R_3}\\left(x_3 - \\frac{n d_1 R_3}{  R_2}-\\frac{ \\bar{m} \\lambda R_3}{d_2}\\right)}\\\\&& =A b_1\\left(\\frac{x_3 R_2}{R_3}\\right) \\\\&& *  \\sum_{n=-(N-1)/2}^{(N-1)/2}\\frac{\\sin\\left[ \\left(M+1\\right)\\frac{\\pi d_2 }{\\lambda R_3}\\left(x_3 - \\frac{n d_1 R_3}{  R_2}-\\frac{ \\bar{m} \\lambda R_3}{d_2}\\right)\\right]}{\\sin\\left[ \\frac{\\pi d_2 }{\\lambda R_3}\\left(x_3 - \\frac{n d_1 R_3}{  R_2}-\\frac{ \\bar{m} \\lambda R_3}{d_2}\\right)\\right]}.\\\\\\end{eqnarray}", "formula_000827": "A", "formula_000826": "w_0", "formula_000828": "M=400", "formula_000832": "R_2=R_3=7892.9 \\mathrm{\\mu m}", "formula_000835": "*", "formula_000829": "\\begin{eqnarray}&&V'_2 (x_2) =A  \\tilde{b}_1 \\left( \\frac{x_2 }{\\lambda R_2} \\right) \\\\&&\\cdot \\sum_{n=-(N-1)/2}^{(N-1)/2} \\sum_{m=-M/2}^{M/2} \\delta\\left(x_2 -m d_2\\right) e^{i 2\\pi  x_2 \\left(\\frac{n d_1}{\\lambda R_2} +\\frac{\\bar{m}}{d_2}\\right)} \\\\\\end{eqnarray}", "formula_000837": "d_2= 3 \\mathrm{\\mu m}", "formula_000831": "\\Delta L=52.61 \\mathrm{\\mu m}", "formula_000833": "R_2=R_3=1354.8 \\mu", "formula_000834": "M+1 > \\frac{\\lambda R_2}{w_1 d_2}.", "formula_000836": "\\begin{eqnarray}b_0(x_0) = \\frac{1}{\\sqrt{\\pi}w_0} e^{-x_0^2/w_0^2}\\\\b_1(x_1) = \\frac{1}{\\sqrt{\\pi}w_1} e^{-x_1^2/w_1^2}\\\\b_2(x_2) = \\frac{1}{\\sqrt{\\pi}w_2} e^{-x_2^2/w_2^2} \\end{eqnarray}", "formula_000830": "V_1(x_1) = A b_1(x_1)", "formula_000838": "V'_3(x_3)", "formula_000839": "\\Delta \\lambda=50", "formula_000840": "5", "formula_000841": "0.31", "formula_000842": "\\begin{eqnarray}V'_2 (x_2) =A   \\sum_{n=-(N-1)/2}^{(N-1)/2}\\sum_{m=-M/2}^{M/2}\\tilde{b}_0 \\left( \\frac{n d_1}{\\lambda R_1} \\right) \\\\\\cdot \\tilde{b}_1 \\left( \\frac{m d_2 }{\\lambda R_2} \\right) b_2\\left(x_2 -m d_2\\right) e^{-i 2\\pi  x_2 \\left(\\frac{n d_1}{\\lambda R_2} -\\frac{\\bar{m}}{d_2}\\right)}.\\end{eqnarray}", "formula_000843": "R_3", "formula_000844": "\\bar{m}", "formula_000845": "1550", "formula_000847": "w_1=w_2=5 \\mathrm{\\mu m}", "formula_000846": "\\lambda_c=1550", "formula_000851": "x_2/\\lambda R_2", "formula_000849": "V_1(x_1) =A b_1(x_1)", "formula_000848": "\\begin{eqnarray}&&V'_3(x_3) = Ab_1\\left(\\frac{x_3 R_2}{R_3}\\right) \\\\&& *  \\sum_{n=-(N-1)/2}^{(N-1)/2}\\frac{\\sin\\left[\\pi \\left(M+1\\right)\\left(\\frac{ x_3d_2 }{\\lambda R_3} - \\frac{n }{N }-\\bar{m}\\right)\\right]}{\\sin\\left[\\pi \\left(\\frac{ x_3d_2 }{\\lambda R_3} - \\frac{n }{N  }-\\bar{m}\\right)\\right]}.\\end{eqnarray}", "formula_000850": "V_3(x_3) = A b_1\\left(\\frac{x_3 R_2}{R_3}-\\frac{ \\bar{m}\\lambda R_2}{d_2}\\right).", "formula_000852": "d_3 =  17.14 \\mu", "formula_000854": "d_1=29.37 \\mathrm{\\mu m}", "formula_000855": "\\lambda_c=1550  \\mu", "formula_000856": "10 log_{10} N=-8.45", "formula_000853": "1\\times N", "formula_000858": "\\bar{m}=\\frac{\\Delta L}{\\lambda}", "formula_000860": "F \\ge N+1", "formula_000857": "d_3 = \\frac{R_3 d_1}{R_2} \\frac{F}{F-1} = \\frac{R_3 d_1}{R_2} \\left( 1 + \\frac{1}{N} \\right),", "formula_000861": "\\lambda R_3/d_2", "formula_000859": "\\begin{eqnarray}&&V'_3(x_3) = A\\tilde{b}_2\\left(\\frac{x_3}{\\lambda R_3}\\right)\\cdot\\sum_{n=-(N-1)/2}^{(N-1)/2}\\sum_{m=-M/2}^{M/2}\\tilde{b}_0 \\left( \\frac{n d_1}{\\lambda R_1} \\right)\\\\&&\\cdot \\tilde{b}_1 \\left( \\frac{m d_2 }{\\lambda R_2} \\right)  e^{-i \\frac{2\\pi m d_2 }{\\lambda R_3}\\left(x_3 - \\frac{n d_1 R_3}{  R_2}-\\frac{ \\bar{m} \\lambda R_3}{d_2}\\right)}. \\end{eqnarray}", "formula_000862": "\\Delta \\lambda'", "formula_000863": "\\lambda_c", "formula_000864": "\\Delta \\lambda' = \\left(\\frac{d_3}{R_3}-\\frac{d_1}{R_2}\\right)\\frac{d_2}{\\bar{m}} = \\Delta \\lambda \\left( 1 - \\frac{R_3 d_1}{R_2 d_3} \\right)=  \\frac{\\Delta \\lambda}{F}", "formula_000865": "\\Delta L", "formula_000866": "V'_1 (x_1) = A  \\sum_{n=-(N-1)/2}^{(N-1)/2} b_1 (x_1 - n d_1),", "formula_000867": "R_2", "formula_000868": "w_1", "formula_000870": "m = \\pm1", "formula_000873": "600", "formula_000875": "\\left[0, \\pi/2\\right] \\ni \\theta \\mapsto \\theta", "formula_000872": "\\ell", "formula_000876": "500", "formula_000879": "k=\\ell \\cdot {R_\\mathrm{JP}}^{-1}", "formula_000880": "x", "formula_000877": "x = (2\\pi n r)/\\lambda \\ll 1", "formula_000883": "\\vert m\\vert = \\ell-2", "formula_000885": "2 \\cdot {R_\\mathrm{JP}}^{-1}", "formula_000889": "\\lambda", "formula_000888": "\\theta=\\pi/2", "formula_000891": "\\left( \\lambda \\gg R \\right)", "formula_000892": "\\left( \\lambda \\ll R \\right)", "formula_000894": "m=\\pm1", "formula_000895": "\\left( \\ell-\\vert m \\vert , m \\right)", "formula_000897": "\\ell \\pm m", "formula_000898": "\\epsilon_1", "formula_000901": "\\theta=0", "formula_000903": "\\ell \\pm m=\\text{even}", "formula_000904": "\\lambda_{\\text{circ}} > L_{\\text{scat}}", "formula_000906": "\\phi", "formula_000908": "\\left[\\pi/2, \\pi\\right] \\ni \\theta \\mapsto \\pi-\\theta \\in \\left[0, \\pi/2\\right]", "formula_000909": "700", "formula_000912": "k \\ll R_\\mathrm{JP}^{-1}", "formula_000910": "\\mathcal{S}^2", "formula_000913": "m", "formula_000914": "\\vert k_\\mathrm{SP} \\vert = \\frac{\\omega}{c} \\sqrt{ \\frac{\\epsilon_1 \\cdot \\epsilon_2}{\\epsilon_1 + \\epsilon_2} }", "formula_000916": "Y_\\ell^m", "formula_000917": "\\vert m \\vert = \\ell-2", "formula_000919": "\\ell>0", "formula_000918": "\\vert m \\vert = 1", "formula_000920": "k_\\mathrm{SP}=1 \\cdot {R_\\mathrm{JP}}^{-1}", "formula_000921": "\\ell-\\vert m \\vert", "formula_000924": "\\theta", "formula_000926": "\\epsilon_2", "formula_000928": "R_\\mathrm{JP}", "formula_000929": "k_\\mathrm{SP} = \\frac{\\ell}{R_\\mathrm{JP}}\\ ,", "formula_000934": "8.8^\\circ", "formula_000933": "\\Delta \\phi", "formula_000932": "n_m\\lesssim 1.01", "formula_000931": "Y_\\ell^m (\\theta,\\phi)", "formula_000935": "R", "formula_000936": "\\pm 1", "formula_000937": "k=k_0(1+\\eta\\Delta\\varepsilon_m-\\kappa\\Delta\\phi^2+O(\\Delta\\varepsilon_m^2)-O(\\Delta\\phi_m^4)),", "formula_000938": "\\phi=8.8", "formula_000939": "\\Delta\\phi\\sim\\Delta ak_z", "formula_000940": "0.518 \\pm 0.02", "formula_000941": "\\text{E}({\\textbf{k}})=c_{x}(\\textbf{k}) + {i}c_{z}(\\textbf{k})", "formula_000942": "\\{N\\}_m", "formula_000943": "\\Delta\\varepsilon^{\\textbf{II}}_2=16.47", "formula_000944": "\\varepsilon_{m}", "formula_000945": "\\{\\Phi_i\\}_m", "formula_000946": "\\text{E}(\\textbf{k})", "formula_000947": "57", "formula_000948": "-0.119 \\pm 0.001", "formula_000949": "-0.227\\pm0.001", "formula_000950": "74", "formula_000951": "64^\\circ", "formula_000952": "0.1", "formula_000954": "\\varepsilon_m=1", "formula_000953": "\\Delta\\varepsilon=\\varepsilon_r-\\varepsilon_m", "formula_000955": "59", "formula_000956": "n_m\\gtrsim1.15", "formula_000957": "\\Phi_{i3}", "formula_000958": "{n_m}/{n_1}<2", "formula_000959": "n_m = 1", "formula_000960": "\\kappa", "formula_000961": "\\phi", "formula_000962": "{k_x}", "formula_000963": "a=500", "formula_000964": "\\varepsilon_m = 1", "formula_000965": "\\Delta k_x\\simeq \\Delta{n_m}^{0.5}", "formula_000966": "S_\\phi=124", "formula_000967": "n_m = 1.45", "formula_000969": "\\Delta a k_0 = ak - ak_0", "formula_000968": "4", "formula_000970": "k", "formula_000971": "n_1", "formula_000972": "\\Delta\\varepsilon^{\\textbf{II}}_1=16.27", "formula_000973": "z", "formula_000975": "S_{\\lambda}^{\\text{max}}", "formula_000974": "\\phi=\\theta-\\theta_0", "formula_000976": "\\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}", "formula_000977": "0.75", "formula_000978": "0.1\\%", "formula_000979": "\\Delta\\varepsilon_m=n_m^2-n_1^2=(n_m-n_1)(n_m+n_1)\\approx 2 \\Delta n_m n_1", "formula_000981": "ak_{x}", "formula_000980": "n_m = 1.33", "formula_000983": "Q", "formula_000982": "\\Delta\\phi=\\sqrt{A+B\\Delta n_m}.", "formula_000984": "\\varepsilon_m", "formula_000985": "-0.272\\pm0.001", "formula_000986": "\\sim{50}", "formula_000987": "\\Delta ak_z", "formula_000989": "C", "formula_000988": "\\varepsilon_{r}", "formula_000990": "-0.054\\pm0.001", "formula_000991": "50", "formula_000994": "L_1(2L_1-1)", "formula_000992": "^b", "formula_000995": "\\Delta n_m=1.250-1.225=0.025", "formula_000993": "q=\\frac{1}{2\\pi}\\oint_C \\text{d} \\textbf{k}\\nabla \\arg(\\text{E}({\\textbf{k}})),", "formula_000996": "{k_z}", "formula_000997": "10^{3}", "formula_000998": "\\varepsilon_{r}=12", "formula_000999": "0.104 \\pm 0.002", "formula_001000": "0.01", "formula_001001": "ak_z \\neq 0", "formula_001002": "-1.84\\pm0.22", "formula_001003": "0.46 \\pm 0.01", "formula_001004": "\\Delta\\varepsilon^{\\textbf{II}}_3 = 16.5", "formula_001005": "\\Delta ak_{(x,z)}", "formula_001007": "ak_x", "formula_001006": "\\dag", "formula_001008": "\\Delta\\varepsilon^{\\textbf{II}}_3=16.5", "formula_001009": "7", "formula_001010": "a = 500", "formula_001011": "\\Delta\\varepsilon_m", "formula_001012": "\\varepsilon_{r}=15", "formula_001014": "\\varepsilon_r", "formula_001013": "\\Delta n_m = n_m - n_1", "formula_001015": "\\varepsilon_r = 17.5", "formula_001016": "(i=x,y,z)", "formula_001019": "a", "formula_001018": "c_{z}(\\textbf{k})", "formula_001017": "0.02", "formula_001021": "S^{\\text{min}}_{\\lambda}", "formula_001022": "n_1=1", "formula_001023": "q=\\frac{1}{2\\pi}\\oint_C \\text{d} \\textbf{k}\\nabla \\arg(\\text{{E}}({\\textbf{k}})),", "formula_001024": "\\Gamma", "formula_001020": "-0.159\\pm0.001", "formula_001025": "R=219.5", "formula_001026": "k_z", "formula_001029": "n_m=1.1", "formula_001027": "\\Delta n_m=0.11", "formula_001028": "m = 1", "formula_001030": "a\\gamma", "formula_001031": "\\Delta\\phi\\sim\\Delta ak_x", "formula_001032": "\\theta", "formula_001034": "c_{z}(k)", "formula_001033": "ak_z = 0", "formula_001035": "\\frac{k}{k_0} \\approx 1+2\\eta\\Delta n_m n_1-\\kappa\\Delta\\phi^2.", "formula_001039": "m = 3", "formula_001036": "\\phi=64(\\deg)", "formula_001041": "\\phi=8.8 (\\deg)", "formula_001037": "R=0.43a", "formula_001043": "x", "formula_001038": "R=0.439a", "formula_001040": "S^{\\text{max}}_{\\phi}", "formula_001042": "^{a,b}", "formula_001049": "\\eta", "formula_001047": "1.31\\pm0.02", "formula_001048": "ak_x \\neq 0", "formula_001044": "\\text{arg}(\\text{E}(\\textbf{k}))", "formula_001045": "(-2.94 \\pm 0.04) \\cdot 10^{-2}", "formula_001046": "n_{m}=1.45", "formula_001050": "\\sqrt{\\Delta n_m}", "formula_001055": "S^{\\text{min}}_{\\phi}", "formula_001053": "\\alpha", "formula_001054": "ak_0", "formula_001052": "\\theta(64^\\circ)", "formula_001051": "\\Delta\\varepsilon^{{\\textbf{I}}}_2", "formula_001056": "1\\leq n_m< 2", "formula_001058": "A", "formula_001057": "\\varepsilon_r = 12", "formula_001059": "\\varepsilon_r=15", "formula_001060": "J(\\rho) = \\frac{\\gamma^2}{2} \\; \\sum_{k({\\rm even}) = -\\infty}^{\\infty}\t\\frac{(1 + k \\tau)}{ \\left[ (1 + k \\tau)^2 + (\\gamma  \\rho)^2  \\right]^{3/2} }.", "formula_001062": "1.51", "formula_001063": "n_{m}=1.33", "formula_001061": "\\varepsilon_{m}=1", "formula_001064": "\\Delta\\varepsilon^{\\textbf{I}}_2=12.92", "formula_001065": "\\Delta\\phi \\approx \\sqrt{\\frac{k_0-k+2k_0\\eta\\Delta n_m n_1}{k_0\\kappa}}.", "formula_001066": "y", "formula_001067": "0.46", "formula_001069": "16", "formula_001071": "13", "formula_001072": "\\Delta ak_0", "formula_001079": "R = 0.43a", "formula_001068": "c_{x}(\\textbf{k})", "formula_001070": "S_\\lambda=285.5", "formula_001081": "\\Delta\\lambda/a", "formula_001082": "2.8\\pm0.2", "formula_001075": "S^{\\text{max}}_{\\lambda}", "formula_001074": "ak_x = 0", "formula_001076": "\\Delta n_m", "formula_001083": "\\Delta\\varepsilon", "formula_001080": "\\theta(8.8^\\circ)", "formula_001087": "R = 0.439a", "formula_001086": "n_m", "formula_001078": "\\Phi_{i2}", "formula_001089": "\\Delta\\varepsilon_{r}=0.1", "formula_001077": "\\Phi_{i1}", "formula_001084": "\\varepsilon _{r}=16", "formula_001088": "ak_z", "formula_001090": "31", "formula_001092": "0.521 \\pm 0.003", "formula_001091": "ak_0=3.575", "formula_001073": "\\Delta n_m=0.265", "formula_001085": "\\varepsilon_{r}=17.49539", "formula_001093": "\\gamma=0", "formula_001095": "1.34\\%", "formula_001096": "n_m\\sim1.01", "formula_001094": "1.039\\pm0.005", "formula_001097": "n_{m}=1", "formula_001099": "L_2(2L_2-1)", "formula_001102": "\\Delta\\varepsilon^{\\textbf{I}}_3=11.9", "formula_001100": "R/a", "formula_001105": "\\Delta n_m^2", "formula_001103": "0.548\\pm0.007", "formula_001098": "m = 2", "formula_001114": "\\phi=64", "formula_001110": "^a", "formula_001112": "\\text{BIC}(k_x)", "formula_001116": "E_z", "formula_001106": "-0.059\\pm0.001", "formula_001108": "S_{\\lambda}^{\\text{min}}", "formula_001107": ":", "formula_001101": "\\text{E}({\\textbf{k}})=c_{x}(\\textbf{k}) + i c_{z}(\\textbf{k})", "formula_001109": "k_x", "formula_001115": "c_{x}(k)", "formula_001104": "ak_{z}", "formula_001111": "\\Delta\\phi=\\sqrt{A+B\\Delta n_m}", "formula_001113": "42", "formula_001117": "10^2", "formula_001118": "n_m=1.442", "formula_001119": "\\Delta\\phi", "formula_001121": "\\varepsilon_r=17.5", "formula_001120": "\\Delta\\varepsilon^{\\textbf{I}}_1=14", "formula_001122": "ak_0=3.805", "formula_001123": "L_3=4L_1L_2", "formula_001124": "B", "formula_001125": "\\text{BIC}(k_z)", "formula_001129": "Q\\approx500", "formula_001134": "r/h \\approx 0.698", "formula_001131": "r = 465", "formula_001128": "5\\times 10^4", "formula_001127": "0.33", "formula_001135": "r_0,\\,r_1, \\textrm{and } r_2", "formula_001130": "\\mathbf{\\hat{V}}", "formula_001132": "\\omega_0", "formula_001138": "s", "formula_001133": "\\mathbf{\\hat{T}}_\\mathrm{0}", "formula_001126": "\\Delta k_z\\simeq \\Delta{n_m}^3", "formula_001137": "\\Delta=3\\%", "formula_001136": "l_\\mathrm{max}=4,\\,\\textrm{and }J=7", "formula_001139": "l_\\mathrm{max}=6,\\,\\textrm{and }J=7", "formula_001140": "\\mathbf{A}_\\mathrm{sca}\\neq0", "formula_001141": "\\eta=1.018\\eta_\\mathrm{BIC}=0.611", "formula_001142": "Q\\approx90", "formula_001143": "M_\\mathrm{s}\\neq0", "formula_001144": "a=3.5r", "formula_001145": "\\begin{eqnarray}\\left[\\frac{\\partial^2}{\\partial t^2}+\\gamma\\frac{\\partial}{\\partial t}+\\omega_0^2\\right]\\mathbf{P}_\\mathrm{dyn}(\\mathbf{r},t)=\\frac{N(t)e^2}{m_\\mathrm{e}}\\mathbf{E}(\\mathbf{r},t)\\,.    \\end{eqnarray}", "formula_001146": "\\mathbf{k}^\\mathrm{M}_{\\parallel}", "formula_001147": "\\eta/\\eta_\\mathrm{BIC}", "formula_001148": "l_\\mathrm{max}=4,\\,\\textrm{and }J=1", "formula_001149": "\\eta=\\eta_\\mathrm{BIC}\\approx0.6", "formula_001151": "\\left| \\mathbf{\\Psi}\\left(\\mathbf{k_{\\parallel}}, \\omega \\right)\\right|", "formula_001150": "\\mathbf{\\hat{S}}=\\mathbf{\\hat{I}}+\\mathbf{\\hat{V}}\\hspace{2pt}\\mathbf{\\hat{T}_\\mathrm{eff}}\\hspace{2pt}\\mathbf{\\hat{U}}\\,.", "formula_001152": "\\phi", "formula_001153": "N", "formula_001156": "e", "formula_001154": "Q\\approx20", "formula_001155": "\\mathbf{\\hat{S}}_\\mathrm{0}", "formula_001157": "\\mathbf{A}_\\mathrm{inc}", "formula_001158": "k", "formula_001159": "\\mathbf{\\hat{C}}^{(3)}", "formula_001163": "r", "formula_001160": "\\mathbf{E}(\\mathbf{r},t)", "formula_001162": "\\Re({\\omega})=0.5\\omega_\\mathrm{m}", "formula_001161": "m_\\mathrm{e}", "formula_001164": "\\Delta", "formula_001165": "\\varepsilon\\gg1", "formula_001166": "\\Im{(\\omega)}>0", "formula_001168": "M_\\mathrm{s}\\rightarrow0", "formula_001167": "\\omega=2\\pi\\times150", "formula_001169": "A_{2g}", "formula_001170": "M_\\mathrm{s}=5\\times10^{-5}", "formula_001171": "M_\\mathrm{s}\\approx10^{-2}", "formula_001172": "N(t)", "formula_001173": "\\Im({\\omega})>0", "formula_001174": "M_\\mathrm{th}\\rightarrow 0", "formula_001175": "J", "formula_001177": "Q", "formula_001178": "r_\\mathbf{k_{\\parallel}}(\\omega) \\approx f\\left(\\mathbf{k_{\\parallel}}, \\omega\\right) = \\frac{1}{\\left| \\mathbf{\\Psi}\\left(\\mathbf{k_{\\parallel}}, \\omega \\right)\\right|}\\, ,", "formula_001179": "\\sigma_\\mathrm{sca}", "formula_001180": "\\mathbf{\\hat{I}}", "formula_001176": "\\varepsilon=2.15", "formula_001181": "\\varepsilon=0", "formula_001182": "N(t)=N_\\mathrm{A}\\left[1+M_\\mathrm{s}\\mathrm{cos}(\\omega_\\mathrm{m}t)\\right]", "formula_001183": "0.5\\omega_\\mathrm{m}", "formula_001184": "\\mathbf{R}", "formula_001185": "M_\\mathrm{s}=2.8\\times 10^{-4}", "formula_001188": "R_0", "formula_001187": "r_\\mathbf{k_{\\parallel}}(\\omega)", "formula_001186": "Q\\rightarrow\\infty", "formula_001189": "2J\\hspace{1pt}l_\\mathrm{max}(l_\\mathrm{max}+2)", "formula_001193": "^\\textrm{1}", "formula_001201": "D_{\\infty h}", "formula_001190": "N(t)=N_0\\left[1+M_\\mathrm{s}\\mathrm{cos}(\\omega_\\mathrm{m}t)\\right]", "formula_001192": "M_\\mathrm{s}\\approx10^{-3}", "formula_001191": "^\\textrm{1*}", "formula_001203": "a", "formula_001202": "r_0=120", "formula_001196": "\\omega_\\mathrm{m}=2\\pi\\times 459", "formula_001199": "r/h", "formula_001206": "\\Gamma", "formula_001194": "^\\textrm{1,4*}", "formula_001198": "l_\\mathrm{cut}>l_\\mathrm{max}", "formula_001204": "c_0", "formula_001200": "\\mathbf{P}_\\mathrm{dyn}(\\mathbf{r},t)", "formula_001205": "M_\\mathrm{s}=M_\\mathrm{th}", "formula_001210": "^\\textrm{5}", "formula_001212": "\\varepsilon_\\infty", "formula_001197": "M_\\mathrm{th}\\sim 1", "formula_001208": "^\\textrm{2}", "formula_001195": "\\eta/\\eta_\\mathrm{BIC}=1.018", "formula_001207": "\\eta=r_1/r_2", "formula_001209": "\\mathbf{A}_\\mathrm{sca}", "formula_001211": "N(t)=N_0(1+M_\\mathrm{s}\\mathrm{cos}(\\omega_\\mathrm{m}t))", "formula_001213": "\\varepsilon", "formula_001215": "\\mathbf{k}_\\parallel", "formula_001217": "^\\textrm{2,3}", "formula_001214": "e^{-i\\omega t}", "formula_001216": "M_\\mathrm{th}", "formula_001218": "L", "formula_001224": "^\\textrm{7*}", "formula_001220": "\\Im({\\omega})", "formula_001223": "r_2=200", "formula_001219": "N_\\mathrm{G}", "formula_001221": "a=3r_2", "formula_001226": "l_\\mathrm{cut}=7", "formula_001229": "^\\textrm{4}", "formula_001234": "30", "formula_001232": "\\varepsilon_0", "formula_001228": "\\Im{(\\omega)}", "formula_001235": "\\Delta =3\\%", "formula_001231": "\\mathbf{k_{\\parallel}}", "formula_001222": "\\gamma\\approx80", "formula_001230": "0", "formula_001233": "\\eta\\rightarrow \\eta_\\mathrm{BIC}", "formula_001236": "\\Re({\\omega})/\\omega_\\mathrm{m}=0.5", "formula_001225": "\\omega_\\mathrm{m}\\neq0", "formula_001227": "\\eta", "formula_001237": "r_0=66.67", "formula_001240": "\\Im({\\omega})<0", "formula_001241": "\\pi/2", "formula_001239": "M_\\mathrm{s}=1.35\\times10^{-7}", "formula_001238": "M_\\mathrm{s}=1.35\\times 10^{-7}", "formula_001243": "r/h\\approx0.698", "formula_001242": "\\mathbf{P}(\\mathbf{r},t)=\\varepsilon_0(\\varepsilon_\\infty-1)\\mathbf{E}(\\mathbf{r},t)+\\mathbf{P}_\\mathrm{dyn}(\\mathbf{r},t)", "formula_001244": "2\\pi\\times161", "formula_001245": "\\omega_\\mathrm{m}=2\\pi\\times300", "formula_001247": "16", "formula_001246": "M_\\mathrm{s}=8.3\\times10^{-4}", "formula_001248": "M_\\mathrm{s}", "formula_001256": "\\Re{(\\omega)}=0.5\\omega_\\mathrm{m}", "formula_001251": "p", "formula_001257": "\\omega_\\mathrm{m}", "formula_001249": "{\\mathbf{\\hat{T}}}_0", "formula_001258": "\\mathbf{k}^\\mathrm{X}_{\\parallel}", "formula_001260": "M_\\mathrm{s}=5\\times 10^{-5}", "formula_001259": "\\Im{(\\omega)}=0", "formula_001255": "n=1", "formula_001250": "\\omega", "formula_001253": "r=465", "formula_001254": "N_\\mathrm{A}", "formula_001252": "\\mathbf{A}_\\mathrm{sca}= \\left(\\mathbf{\\hat{I}}-{\\mathbf{\\hat{T}}_{0}(\\omega)}\\sum_{\\mathbf{R}\\neq0}\\mathbf{\\hat{C}}^{(3)}(-\\mathbf{R})\\hspace{1pt}\\mathrm{e}^{i\\mathbf{k_{{\\parallel}}}\\cdot\\mathbf{R}}\\right)^{-1}{\\mathbf{\\hat{T}}_{0}(\\omega)}\\mathbf{A}_\\mathrm{inc}\\,.", "formula_001261": "^\\textrm{6}", "formula_001263": "l_\\mathrm{max}", "formula_001262": "M_\\mathrm{s}=8.3\\times 10^{-4}", "formula_001264": "\\mathbf{\\hat{U}}", "formula_001265": "Q\\approx5\\times 10^4", "formula_001267": "r_1=\\eta r_2", "formula_001268": "N_0", "formula_001266": "N(t)=N_\\mathrm{G}\\left[1+M_\\mathrm{s}\\mathrm{cos}(\\omega_\\mathrm{m}t)\\right]", "formula_001269": "(\\sqrt{2}+1)\\frac{\\pi}{a}", "formula_001270": "l_\\mathrm{cut}", "formula_001271": "\\mathbf{\\hat{S}}", "formula_001272": "R_0= 245", "formula_001273": "a = 3r_2", "formula_001274": "m=0", "formula_001276": "\\mathbf{A}_\\mathrm{inc}=0", "formula_001278": "\\eta=0.9", "formula_001282": "^\\textrm{3}", "formula_001283": "m = 0", "formula_001275": "\\omega_\\mathrm{m}=2\\pi\\times 300", "formula_001284": "h", "formula_001281": "\\mathbf{\\hat{S}}_\\mathrm{0}= \\left(\\mathbf{\\hat{I}}+2\\mathbf{\\hat{T}}_\\mathrm{0}\\right)\\,.", "formula_001280": "\\Re \\{\\omega\\} =\\omega_{\\rm m}/2", "formula_001279": "\\Re{(\\omega)/\\omega_\\mathrm{m}}=0.5", "formula_001277": "\\Delta t=\\phi/\\omega_\\mathrm{m}", "formula_001285": "\\underbrace{\\left|\\mathbf{\\hat{I}}-\\mathbf{\\hat{T}}_0(\\omega)\\sum_{\\mathbf{R}\\neq0}\\mathbf{\\hat{C}}^{(3)}(-\\mathbf{R})\\hspace{1pt}\\mathrm{e}^{i\\mathbf{k_{\\parallel}}\\cdot\\mathbf{R}}\\right|}_{\\left| \\mathbf{\\Psi}\\left(\\mathbf{k_{\\parallel}}, \\omega \\right)\\right|}=0\\,.", "formula_001286": "\\mathbf{\\hat{T}}_\\mathrm{eff}", "formula_001288": "\\Re\\{\\omega\\}/\\omega_\\mathrm{m} = 0.5", "formula_001289": "a=3.5R_0", "formula_001290": "\\Delta=0.09\\%", "formula_001292": "M_\\mathrm{s}=0", "formula_001287": "l_\\mathrm{max}=6,\\,\\textrm{and }J=1", "formula_001291": "\\Im{(\\omega)>0}", "formula_001293": "\\gamma", "formula_001296": "n=2", "formula_001294": "M_\\mathrm{th}\\approx0.12", "formula_001297": "M_\\mathrm{s}=4.4\\times10^{-2}", "formula_001295": "\\Delta=\\frac{|{k}^\\mathrm{M}_{\\parallel}+{k}^\\mathrm{X}_{\\parallel}|}{L}\\,.", "formula_001299": "f_c", "formula_001298": "\\mathrm{KOH}", "formula_001300": "10^{-4}", "formula_001303": "t=20 \\mathrm{ms}", "formula_001301": "\\mathrm{C/mol}", "formula_001302": "f_c+f_d=1", "formula_001304": "1.27 \\times 10^{-4}", "formula_001305": "\\begin{align}    \\partial_t H + \\nabla \\cdot (H \\mathbf{u}) + \\frac{\\dot{m}}{\\rho_c} \\delta_\\Sigma = 0. \\end{align}", "formula_001307": "\\zeta=1", "formula_001306": "D_b=0.0127", "formula_001308": "A=\\pi/4 D_e^2", "formula_001309": "R=2\\beta t^{1/2}", "formula_001311": "c^1=\\rho^1/M^1", "formula_001310": "R", "formula_001312": "\\begin{align}    \\Vert \\rho^1 (\\mathbf{u^1}-\\mathbf{u_\\Sigma})\\cdot \\mathbf{n_\\Sigma}  \\vert \\vert = \\vert\\vert \\dot{m}^1 \\vert\\vert  =0. \\end{align}", "formula_001313": "I=100 \\mathrm{A/m^2}", "formula_001316": "l", "formula_001315": "\\Omega_d(t)", "formula_001314": "L_0 \\times L_0 \\times L_0", "formula_001318": "\\mathrm{He}", "formula_001319": "D^1", "formula_001317": "\\begin{align}    \\rho_c\\mathbf{u}=\\rho^1 \\mathbf{u^1}+\\rho^2 \\mathbf{u^2},\\end{align}", "formula_001320": "t>t_m", "formula_001321": "\\rho_d = 0.8 \\, \\text{kg/m}^3", "formula_001324": "\\begin{align}    \\frac{\\partial c}{\\partial z} &= \\frac{J}{D}  \\qquad \\text{for } r < \\frac{D_e}{2},    \\\\    \\frac{\\partial c}{\\partial z} &= 0    \\qquad \\text{for } r > \\frac{D_e}{2}.    \\end{align}", "formula_001325": "\\mu_d", "formula_001326": "I", "formula_001323": "\\bar{c_e}", "formula_001322": "z \\times r (L_{ref})", "formula_001327": "[0, 1]", "formula_001331": "g", "formula_001328": "\\begin{align}    \\mathrm{Sc}=\\frac{\\nu_c}{D},\\end{align}", "formula_001329": "R \\propto t^{0.8}", "formula_001330": "\\mathrm{N/m}", "formula_001332": "\\begin{align}    R_{det}=0.6 \\theta \\sqrt{\\frac{\\sigma} {(\\rho_c-\\rho_d)g}},    \\end{align}", "formula_001333": "8.35 \\times 10^{-7}", "formula_001335": "\\mathrm{H_2}", "formula_001334": "8.32 \\times 10^{-4}", "formula_001336": "\\mathrm{A/m^2}", "formula_001338": "c_{\\Sigma}=c_{sat}", "formula_001337": "M^1", "formula_001340": "t=0", "formula_001339": "90 \\degree", "formula_001341": "9.8 \\mathrm{m/s^2}", "formula_001342": "\\beta", "formula_001344": "\\mathrm{kg/(m \\cdot s)}", "formula_001343": "\\begin{align}    \\mathrm{Bo}=\\frac{\\rho_cgD_b^2}{\\sigma}.\\end{align}", "formula_001345": "c=c_c", "formula_001347": "\\rho^1", "formula_001348": "\\delta_\\Sigma", "formula_001346": "k", "formula_001349": "\\mathrm{mol/m^3}", "formula_001351": "(c_c)_{\\Sigma}", "formula_001352": "\\zeta>1", "formula_001353": "25\\times 25", "formula_001350": "\\Delta", "formula_001356": "F_b=(4/3)\\pi R^3(\\rho_c-\\rho_d)g", "formula_001355": "\\theta=90\\degree", "formula_001354": "I=i/A", "formula_001357": "D_e", "formula_001358": "D_b", "formula_001359": "\\mathrm{m/s^2}", "formula_001360": "\\theta=90^\\circ", "formula_001363": "\\mathrm{m}", "formula_001361": "J", "formula_001362": "f_d", "formula_001364": "\\begin{align}    \\overline{\\mathrm{Sh_{e}}}=\\frac{L_{ref} \\int_{\\Sigma} \\dot{m}\\,ds}{A_{\\Sigma} M D(\\bar{c_e}-c_{\\Sigma})},\\end{align}", "formula_001365": "\\begin{align}    (c_c)_\\Sigma=\\frac{(c_d)_\\Sigma}{\\mathrm{He}},    \\end{align}", "formula_001366": "\\Delta = L_0", "formula_001367": "4\\pi R", "formula_001368": "\\begin{align}f_c = \\begin{cases} 0 & \\text{if the cell is pure gas}, \\\\1 & \\text{if the cell is pure liquid}, \\\\[0,1] & \\text{if the cell is mixed}.\\end{cases}\\end{align}", "formula_001369": "L_{\\text{ref}}", "formula_001370": "t_n=20 \\mathrm{ms}", "formula_001372": "\\begin{align}  \\nabla \\cdot \\mathbf{u} &= 0 \\quad \\text{in } \\Omega \\backslash \\Sigma,   \\\\  \\begin{split}      \\partial_t (\\rho \\mathbf{u}) + \\nabla \\cdot (\\rho \\mathbf{u} \\otimes \\mathbf{u})       &= -\\nabla p + \\nabla \\cdot (2\\mu \\mathbf{D}) \\\\      &\\quad + \\rho \\mathbf{g} \\quad \\text{in } \\Omega \\backslash \\Sigma.  \\end{split} \\end{align}", "formula_001371": "\\theta = 90^\\circ", "formula_001373": "\\begin{align}    \\mathrm{Ga}=\\sqrt{\\frac{\\rho_cgD_b^2}{\\nu_c^2}}.\\end{align}", "formula_001375": "\\Omega_c", "formula_001377": "\\Omega_d", "formula_001380": "M", "formula_001379": "t_n=0.02", "formula_001378": "\\rho_c", "formula_001374": "\\delta_\\mathrm{H_2}=D\\Delta c /J", "formula_001376": "D_e=10 D_b=0.127", "formula_001381": "c_{\\text{sat}}", "formula_001383": "\\Delta V \\propto M/\\rho_d", "formula_001382": "\\begin{align}    \\mathbf{u} \\otimes (\\mathbf{u}-\\mathbf{u_\\Sigma}) + p\\mathbf{I} -2\\mu \\mathbf{D} \\cdot \\mathbf{n_\\Sigma} = \\sigma k \\mathbf{n_\\Sigma} + \\nabla_\\Sigma \\sigma. \\end{align}", "formula_001386": "D", "formula_001384": "8.96 \\times 10^{-6}", "formula_001387": "\\delta_\\mathrm{H_2}", "formula_001388": "\\mathrm{1000 A/m^2}", "formula_001385": "\\begin{align}  \\begin{split}      \\int_V \\partial_t c \\, dV       &+ \\oint_{\\partial V} (c \\mathbf{u} - D \\nabla c) \\cdot \\mathbf{n} \\, dS \\\\      &+ \\int_\\Sigma c (\\mathbf{u} - \\mathbf{u}_\\Sigma) \\cdot \\mathbf{n}_\\Sigma \\, dS \\\\      &= 0  \\end{split}  \\end{align}", "formula_001390": "7.5 \\times 10^{-2}", "formula_001391": "\\mu", "formula_001389": "t \\sim0.2", "formula_001392": "\\mathbf{D}", "formula_001393": "\\mathrm{Sh}=D_b/\\delta_\\mathrm{H_2}", "formula_001394": "\\begin{align}  \\rho (\\mathbf{u}-\\mathbf{u_\\Sigma})\\cdot \\mathbf{n_\\Sigma} = 0 ,\\end{align}", "formula_001395": "V", "formula_001396": "\\Sigma(t)", "formula_001397": "F", "formula_001399": "a", "formula_001400": "\\mathrm{kg/mol}", "formula_001403": "\\theta=35^\\circ", "formula_001398": "\\theta=35 \\degree", "formula_001404": "\\begin{align}    \\partial_t c + \\mathbf{u}\\cdot\\nabla c = \\nabla\\cdot\\left(D\\nabla c\\right) - \\frac{\\dot{m}}{M}\\delta_\\Sigma.    \\end{align}", "formula_001402": "c_0", "formula_001401": "\\mathcal{R}, T_0, P_0", "formula_001405": "_2", "formula_001407": "\\mathrm{kg/m^2 s}", "formula_001406": "R \\propto t^{1/3}", "formula_001409": "H(x,t)", "formula_001408": "R \\propto t^{1/2}", "formula_001410": "7.38 \\times 10^{-9}", "formula_001411": "1.27 \\times 10^{-5}", "formula_001412": "\\begin{align}  \\begin{split}      \\dot{m}^1       &= \\rho^1(\\mathbf{u} - \\mathbf{u}_\\Sigma) \\cdot \\mathbf{n}_\\Sigma       + \\rho^1(\\mathbf{u}^1 - \\mathbf{u}) \\cdot \\mathbf{n}_\\Sigma \\\\      &= \\frac{\\rho^1}{\\rho_c} \\cdot \\dot{m} + J^1 \\cdot \\mathbf{n}_\\Sigma  \\end{split} \\end{align}", "formula_001414": "\\Omega_c (\\Omega_d)", "formula_001416": "t=0.2 \\mathrm{s}", "formula_001415": "\\dot{m} = 0", "formula_001413": "\\theta", "formula_001417": "(c_d)_\\Sigma = \\rho_d/M", "formula_001418": "\\begin{align}    J^1=-D^1 \\nabla \\rho^1, \\end{align}", "formula_001419": "\\theta=35\\degree", "formula_001420": "96485.3", "formula_001421": "n_\\Sigma", "formula_001422": "i", "formula_001424": "R=0", "formula_001426": "I=\\mathrm{1000 A/m^2}", "formula_001425": "\\begin{align}    \\overline{\\mathrm{Sh_{e}}}=\\frac{J L_{ref}}{D(\\bar{c_e}-c_{sat})}.\\end{align}", "formula_001427": "25\\times 25 \\times 25", "formula_001428": "\\delta_{\\mathrm{H_2}}", "formula_001429": "\\begin{align}    \\mathrm{2H_2O} + \\mathrm{2e^-} \\rightarrow  \\mathrm{2H_2+2OH^-}.\\end{align}", "formula_001430": "\\rho", "formula_001431": "\\begin{align}    \\partial_t\\rho^1+\\nabla \\cdot (\\rho^1 \\mathbf{u^1})=R, \\end{align}", "formula_001433": "\\Omega", "formula_001432": "\\Omega_c(t)", "formula_001434": "c_e-c_{\\Sigma}", "formula_001435": "A", "formula_001436": "\\pi/2", "formula_001437": "\\sigma", "formula_001438": "R \\propto t", "formula_001439": "t_m", "formula_001440": "R = 2\\beta t^{1/2}", "formula_001441": "\\mathbf{I}", "formula_001442": "c_0=c_{sat}", "formula_001444": "\\Delta(l) = \\frac{L_0}{2^l}", "formula_001443": "p", "formula_001445": "\\dot{m}", "formula_001446": "\\begin{align}f_c &= \\frac{1}{V} \\int_V H \\, dV ,\\end{align}", "formula_001447": "L_0=25L_{ref}", "formula_001448": "\\theta=90 \\degree", "formula_001449": "^2", "formula_001450": "\\theta =90 \\degree", "formula_001451": "t=0.02 \\mathrm{s}", "formula_001452": "x \\times y \\times z (L_{ref})", "formula_001453": "\\begin{align}    \\int_{\\Sigma} \\dot{m}\\,ds = \\left(\\frac{P_0}{\\mathcal{R} T_0}\\right) 4\\pi R^2 \\frac{dR}{dt} M\\end{align}", "formula_001454": "A_{\\Sigma}", "formula_001455": "\\begin{align}    \\nabla \\cdot \\mathbf{u} &= \\dot{m}\\left(\\frac{1}{\\rho_d}-\\frac{1}{\\rho_c}\\right) \\delta_\\Sigma,   \\\\    \\partial_t \\mathbf{u} + \\nabla \\cdot ( \\mathbf{u} \\otimes \\mathbf{u}) &= \\frac{1}{\\rho}\\left[-\\nabla p + \\nabla \\cdot (2\\mu \\mathbf{D})\\right] + \\frac{\\sigma k \\mathbf{n_\\Sigma}}{\\rho}\\delta_\\Sigma, \\end{align}", "formula_001458": "2", "formula_001456": "\\rho_c/\\rho_d=12450", "formula_001459": "\\begin{align}  \\begin{split}      \\frac{\\partial}{\\partial t} \\int_V H \\, dV       &+ \\frac{1}{V} \\int_V \\nabla \\cdot (H \\mathbf{u}) \\, dV \\\\      &+ \\frac{1}{V} \\int_V \\frac{\\dot{m}}{\\rho_c} \\delta_\\Sigma \\, dV       = 0  \\end{split}   \\end{align}", "formula_001461": "c_{sat}", "formula_001460": "\\mathbf{u}", "formula_001462": "\\begin{align}    \\overline{\\mathrm{Sh_{e}}}=\\frac{P_0}{\\mathcal{R} T_0} \\frac{2R}{D(\\bar{c_e}-c_{\\Sigma})}\\frac{dR}{dt}    \\end{align}", "formula_001463": "\\mathrm{m^2/s}", "formula_001464": "\\mathrm{Sh_b}", "formula_001465": "\\begin{align}    J = \\frac{I}{2F},\\end{align}", "formula_001466": "F = 96485.3", "formula_001468": "\\Omega=\\Omega_d(t) \\cup \\Omega_c(t) \\cup \\Sigma(t)", "formula_001467": "L_0 \\times L_0", "formula_001469": "I(Am^{-2})", "formula_001470": "t=52.1 \\mathrm{ms}", "formula_001471": "\\begin{align}    \\partial_t\\rho^1+\\mathbf{u}\\cdot \\nabla \\rho^1 +\\nabla \\cdot J^1=R, \\end{align}", "formula_001473": "\\Omega_d (\\Omega_c)", "formula_001475": "I=1000 \\mathrm{A/m^2}", "formula_001472": "t>t_n", "formula_001474": "\\zeta=c_0/c_{sat}=1", "formula_001476": "\\begin{align}    \\rho_c=\\rho^1+\\rho^2,\\end{align}", "formula_001477": "\\rho_d", "formula_001478": "\\begin{align}    \\dot{m}=-\\frac{M^1D^1}{1-\\frac{\\rho^1}{\\rho_c}}\\frac{\\partial c^1}{\\partial \\mathbf{n_\\Sigma}}, \\end{align}", "formula_001479": "t<t_n", "formula_001480": "1/2", "formula_001481": "\\begin{align}  \\begin{split}      \\int_V \\partial_t H \\, dV       + \\oint_{\\partial V} H \\mathbf{u} \\cdot \\mathbf{n} \\, dS \\\\      + \\int_\\Sigma (\\mathbf{u}_c - \\mathbf{u}_\\Sigma) \\cdot \\mathbf{n}_\\Sigma \\, dS       &= 0  \\end{split}   \\end{align}", "formula_001482": "\\mathbf{u_\\Sigma}", "formula_001484": "\\mathrm{kg/m^3}", "formula_001483": "\\begin{align}    J^1=\\rho^1(\\mathbf{u^1}-\\mathbf{u}).\\end{align}", "formula_001487": "\\mathbf{u^1}", "formula_001486": "\\theta = 35^\\circ", "formula_001485": "\\nu_c", "formula_001491": "\\mu_c", "formula_001490": "2.0 \\times 10^{-2}", "formula_001489": "\\delta", "formula_001488": "\\begin{align}    \\left(p\\mathbf{I} -2\\mu \\mathbf{D} \\Vert \\cdot \\mathbf{n_\\Sigma}\\right)= \\sigma k \\mathbf{n_\\Sigma}  \\end{align}", "formula_001493": "45^\\circ", "formula_001492": "R(t)h(t)\\leq \\eta_{\\mathrm{cr},+}(t)", "formula_001495": "t=0:", "formula_001494": "\\mathrm{d} P/\\mathrm{d} t=\\rho\\lambda U (\\mathrm{d} V_{1/2}/\\mathrm{d} t)+\\rho\\lambda V_{1/2}(\\mathrm{d} U/\\mathrm{d} t)", "formula_001496": "\\alpha=1/2", "formula_001497": "P_{tot}=P+\\underbrace{\\rho\\lambda\\int_0^R \\frac{x}{t+t_0}\\left[h(x,t)-h_{bl}(t)\\right]\\mathrm{d} x}_{=P_{lam}}.", "formula_001498": "\\frac{\\Delta V}{h} \\leq 2(\\gamma/\\rho)(1-\\cos\\vartheta_a)I_h(t).", "formula_001499": "t=R_0/U_0=\\tau", "formula_001500": "c(t_*)t_*", "formula_001501": "\\frac{\\mathrm{d} \\Delta}{\\mathrm{d} t}+\\frac{\\Delta}{t+t_0}=-\\frac{2h}{V}\\left(\\Delta^2-c^2\\right),", "formula_001503": "V=V_{tot}-2R[(\\tau+t_0)/(t+t_0)]h_{init}", "formula_001502": "h(x,t)=\\frac{\\tau+t_0}{t+t_0}f\\left(x\\frac{\\tau+t_0}{t+t_0}\\right)+\\underbrace{\\tfrac{2}{3}\\left[h_{bl}(t)\\frac{t+t_1}{t+t_0}-h_{bl}(\\tau)\\frac{\\tau+t_1}{t+t_0}\\right]}_{=h_{PI}(t)}\\qquad t\\geq \\tau,", "formula_001504": "u_1", "formula_001505": "\\rho\\int_0^R \\mathrm{d} x\\int_0^{h(x,t)}\\mathrm{d} z", "formula_001506": "u_0", "formula_001507": "100^\\circ", "formula_001508": "\\alpha=1", "formula_001509": "\\mathrm{d} h/\\mathrm{d} t", "formula_001511": "\\frac{\\partial \\alpha}{\\partial t} + \\nabla \\cdot (\\alpha \\bm{u}) + \\nabla \\cdot [\\alpha(1 - \\alpha)\\bm{u}_r] = 0.", "formula_001510": "\\frac{V_{tot}^2}{8}\\sim  h_{init}^4,\\qquad \\mathrm{We}\\gg 1.", "formula_001513": "O", "formula_001512": "\\frac{\\tfrac{1}{2}V_{tot} - \\left[(\\gamma/\\rho)(1-\\cos\\vartheta_a)\\right]^{1/2}[\\Delta G(t_*)]^{1/2}}{h_*} \\leq \\max_t R(t)\\leq \\frac{V_{tot}}{2 h_*}", "formula_001516": "70^\\circ", "formula_001514": "=u_x", "formula_001517": "V_{init}=0", "formula_001515": "\\eta(t)=R(t)h(t)", "formula_001519": "a=\\sin\\vartheta_a\\sqrt{\\frac{2V}{2\\vartheta_a-\\sin(\\vartheta_a)}}", "formula_001521": "3000\\times 1000", "formula_001520": "k_t", "formula_001522": "h_{init}/(\\tau+t_0)", "formula_001523": "R[(\\tau+t_0)/(t+t_0)]", "formula_001524": "R_{max}/R_0\\sim 0.47\\,\\mathrm{We}/(1-\\cos\\vartheta_a)", "formula_001525": "\\alpha = 1", "formula_001526": "\\mu", "formula_001527": "V(\\tau)=0", "formula_001528": "{\\color{red}{2a}}", "formula_001530": "V", "formula_001529": "120^\\circ", "formula_001531": "\\tau", "formula_001532": "4\\left[\\epsilon^2 X^2+A \\left(X^3+X-2X^2\\right)+ \\epsilon B\\left(X^2-X\\right)\\right]=\\left[3A X^2+2X\\left(\\epsilon^2+\\epsilon B-2A\\right)+\\left(A-\\epsilon B\\right)\\right]^2.", "formula_001533": "\\frac{\\mathrm{d} V}{\\mathrm{d} t}=2\\left[\\frac{R}{t+t_0}\\left(1-\\frac{h_{bl}}{h}\\right)-\\dot R\\right]h,", "formula_001534": "g(\\eta)=\\int_0^\\eta f(\\eta)\\mathrm{d} \\eta", "formula_001535": "I_h(t)=\\int_\\tau^t \\frac{\\mathrm{d} t}{h}.", "formula_001536": "(\\alpha,t_0,t_1)", "formula_001539": "d", "formula_001538": "1.12", "formula_001541": "t_*", "formula_001540": "R(t)", "formula_001542": "X=[\\mathrm{We}/(1-\\cos\\vartheta_a)]^{1/2}", "formula_001544": "\\frac{\\tfrac{1}{2}V_{tot} - \\left[(\\gamma/\\rho)(1-\\cos\\vartheta_a)\\right]^{1/2}[\\Delta G(t_*)]^{1/2}}{h_*} \\leq \\max_t R(t)\\leq \\frac{V_{tot}}{2 h_*}.", "formula_001546": "L_x\\times L_z", "formula_001547": "2250", "formula_001548": "1500\\times 500", "formula_001550": "U=-c_*,\\qquad V=2Uh_*,", "formula_001549": "R(t_*)", "formula_001551": "k_1 \\mathrm{Re}^{1/3}-k_2(1-\\cos\\vartheta_a)^{1/2}(\\mathrm{Re}/\\mathrm{We})^{1/2}\\leq \\mathcal{R}_{max}/R_0\\leq k_1\\mathrm{Re}^{1/3}", "formula_001553": "b", "formula_001552": "c^2=[\\gamma/(\\rho h)](1-\\cos\\vartheta_a)", "formula_001555": "\\beta_{max}\\approx \\sqrt{\\frac{4}{1-\\cos\\vartheta}\\left[\\tfrac{1}{12}(1-b)\\mathrm{We}+1\\right]},", "formula_001557": "k_0", "formula_001558": "\\alpha", "formula_001554": "Y", "formula_001556": "\\frac{\\mathrm{d} P_{tot}}{\\mathrm{d} t}=F_{ext}=-\\lambda\\,\\gamma(1-\\cos\\vartheta_a).", "formula_001559": "\\mathcal{R}_{max}/R_0", "formula_001563": "h(x,t)=h_{init}(\\tau+t_0)/(t+t_0)+h_{PI}(t)", "formula_001561": "\\frac{\\pi}{2}(1-b)\\mathrm{We}+2\\pi=\\left[2\\beta_{max}(1-\\cos\\vartheta)+\\frac{\\pi}{\\beta_{max}}\\right],", "formula_001560": "110^\\circ", "formula_001562": "Y=X\\pm X\\sqrt{\\epsilon^2+A \\left(X+\\frac{1}{X}-2\\right)+ \\epsilon B \\left(1-\\frac{1}{X}\\right)}.", "formula_001565": "V(t=\\tau)=0", "formula_001564": "V\\frac{\\mathrm{d} U}{\\mathrm{d} t}=2\\left[ \\left(\\overline{u}-U\\right)^2 -c^2\\right]h,", "formula_001566": "2\\int_0^R \\mathrm{d} x\\int_0^{h(x,t)}\\mathrm{d} z", "formula_001567": "V=V_{tot}-2Rh", "formula_001568": "c(t_*)t_*\\sim \\mathrm{Re}^{2/5}/\\mathrm{We}^{1/2}", "formula_001569": "\\text{Kinetic Energy of sheet}+\\text{Surface Energy of sheet}=\\gamma(2\\pi R_0)+\\tfrac{1}{2}\\rho V_{tot}U_0^2.", "formula_001570": "U=\\mathrm{d} R/\\mathrm{d} t", "formula_001572": "R(t)h(t)\\geq \\eta_{\\mathrm{cr},-}(t)", "formula_001571": "\\ell_*^2=c_*^2(\\tau+t_0)^2", "formula_001574": "c_*", "formula_001573": "X", "formula_001575": "h(R,t)<h_{bl}", "formula_001576": "{\\color{red}{h}}", "formula_001577": "X\\sim 4/(9A)", "formula_001580": "\\gamma^{1/2}", "formula_001579": "\\mathrm{Re}^{1/3}", "formula_001578": "\\alpha\\in [0,1]", "formula_001582": "F(z)=1", "formula_001581": "\\eta=R[(\\tau+t_0)/(t+t_0)]", "formula_001583": "t_1", "formula_001584": "\\begin{eqnarray}\\frac{\\mathrm{d} V}{\\mathrm{d} t}&=&2\\left(u_0-U\\right)h,\\\\\\frac{\\mathrm{d} R}{\\mathrm{d} t}&=&U,\\\\V\\frac{\\mathrm{d} U}{\\mathrm{d} t}&=&2\\left(u_0-U\\right)^2h-2\\gamma\\left(1-\\cos\\vartheta_a\\right),\\end{eqnarray}", "formula_001587": "h_{bl}", "formula_001586": "\\frac{\\mathrm{d} h}{\\mathrm{d} t}=-\\frac{h-h_{bl}}{t+t_0},\\qquad \\tau<t<t_*,\\qquad h_{bl}=\\alpha \\sqrt{\\nu (t+t_1)}.", "formula_001585": "h(t_*)=h_{bl}(t_*).", "formula_001590": "h\\equiv h(t)\\equiv h(R,t).", "formula_001588": "(\\mathrm{We}/\\mathrm{Re})^{1/2}", "formula_001589": "\\mathcal{R}_{max}/R_0 \\sim k_1\\mathrm{Re}^{1/3}-k_0(1-\\cos\\vartheta_a)^{1/2}(\\mathrm{Re}/\\mathrm{We})^{1/2}", "formula_001591": "\\lambda_{\\mathrm{s}}", "formula_001592": "f=1", "formula_001593": "\\beta_{max}=k_1 \\mathrm{Re}^{1/3}-k_0 (1-\\cos\\vartheta_a)^{1/2}(\\mathrm{Re}/\\mathrm{We})^{1/2}.", "formula_001594": "\\frac{\\mathrm{d}}{\\mathrm{d} t}\\left(V_{tot}\\eta-\\eta^2\\right)\\geq 2(\\gamma/\\rho)(1-\\cos\\vartheta_a) h^2 I_h(t).", "formula_001595": "h(R,t)=[(\\tau+t_0)/(t+t_0)]h_{init}", "formula_001596": "b=0.536", "formula_001598": "R", "formula_001597": "\\mathrm{Re}\\rightarrow\\infty", "formula_001599": "h(t)>h_{bl}(t)", "formula_001600": "\\max_t R(t)\\geq R(t_*)", "formula_001603": "h(t)", "formula_001604": "\\bm{f}_\\gamma", "formula_001602": "h(R,t)=\\left(\\frac{\\tau+t_0}{t+t_0}\\right)h_{init},", "formula_001601": "U(t=\\tau)=\\overline{u}-\\sqrt{ \\frac{\\gamma\\left(1-\\cos\\vartheta_{ap}\\right)}{\\rho h}},", "formula_001606": "V_{tot}-2\\eta =V\\geq 0", "formula_001605": "\\lambda", "formula_001607": "V\\Delta=V_{init}\\Delta_{init}\\left(\\frac{\\tau+t_0}{t+t_0}\\right)+\\frac{\\gamma}{\\rho}\\left(1-\\cos\\vartheta_a\\right)\\left[t+t_0-\\frac{(\\tau+t_0)^2}{t+t_0}\\right].", "formula_001608": "\\mathrm{Re} \\geq 10^3", "formula_001610": "\\mathcal{R}_{max}", "formula_001609": "\\mathrm{We}\\gg 1", "formula_001611": "130^\\circ", "formula_001614": "U_0", "formula_001613": "z=0", "formula_001612": "Y=X\\pm \\sqrt{\\epsilon^2 X^2+A \\left(X^3+X-2X^2\\right)+\\epsilon B\\left(X^2-X\\right)}.", "formula_001615": "Y=k_1X", "formula_001616": "L_x=0.046\\,\\mathrm{m}", "formula_001619": "(u,w)_{z=h}\\neq 0", "formula_001620": "h_{init}", "formula_001622": "1.11\\times 10^{-5}\\,\\mathrm{m}", "formula_001621": "V_{tot}=\\pi R_0^2", "formula_001623": "R\\left(\\frac{\\tau+t_0}{t+t_0}\\right)=\\frac{V_{tot}\\pm \\sqrt{ V_{init}^2+4h_{init}[G(t)-G(\\tau)]\\bigg\\}}}{2h_{init}}", "formula_001624": "c(t)", "formula_001626": "\\pi-\\vartheta_a", "formula_001625": "Y_{max}\\approx (4/9)A^{-1}-\\sqrt{A (4/9)^3 A^{-3}}=\\tfrac{4}{27}A^{-1}.", "formula_001627": "h(t_{max})\\geq h_*", "formula_001628": "1.66\\times 10^{-5}\\,\\mathrm{m}", "formula_001630": "\\frac{\\mathrm{d} }{\\mathrm{d} t}(\\Delta V)+\\frac{\\Delta V}{t+t_0}\\left(1-\\frac{h_{bl}}{h}\\right)=2(\\gamma/\\rho)(1-\\cos\\vartheta_a)-\\frac{u_0}{t+t_0}(h_{bl}/h)\\Phi(t).", "formula_001631": "R_{max}\\leq \\frac{V_{tot}}{2 h_*}.", "formula_001629": "\\mathrm{We} \\geq 10^2", "formula_001632": "90^\\circ", "formula_001634": "\\beta_{max}=1.00\\mathrm{Re}^{1/5}-0.37\\mathrm{Re}^{2/5}\\mathrm{We}^{-1/2},\\qquad\\text{(3D axisymmetric)}.", "formula_001633": "V(\\mathrm{d} \\Delta/\\mathrm{d} t)+\\Delta(\\mathrm{d} V/\\mathrm{d} t)", "formula_001635": "R(t_*)\\geq \\frac{\\eta_{\\mathrm{cr}-}(t_*)}{h(t_*)}\\stackrel{\\text{Eq.~}}{=} \\tfrac{1}{2}\\frac{V_{tot}}{h(t_*)}-\\underbrace{\\left[(\\gamma/\\rho)(1-\\cos\\vartheta_a)\\right]^{1/2}\\frac{[\\Delta G(t_*)]^{1/2}}{h(t_*)}}.", "formula_001638": "<4\\,\\mathrm{ms}", "formula_001637": "\\nabla\\cdot\\bm{u}=0.", "formula_001636": "\\left[V_{tot}-2\\eta h_{init}\\right]\\mathrm{d} \\eta=\\bigg\\{-\\Delta_{init}V_{init}\\left(\\frac{\\tau+t_0}{t+t_0}\\right)^2-\\frac{\\gamma(\\tau+t_0)}{\\rho}\\left(1-\\cos\\vartheta_a\\right)\\left[1-\\left(\\frac{\\tau+t_0}{t+t_0}\\right)^2\\right]\\bigg\\}.", "formula_001639": "1000\\,\\mathrm{kg}\\cdot\\mathrm{m}^{-3}", "formula_001640": "\\sigma", "formula_001641": "\\bm{u} = (0,0,-1)", "formula_001644": "K=10^3-10^4", "formula_001642": "\\beta_{max}", "formula_001643": "F(z=0)=0", "formula_001645": "\\vartheta_a\\in [40^\\circ,130^\\circ]", "formula_001646": "V_{tot}=V+2\\int_0^R \\mathrm{d} x\\int_0^{h(x,t)}\\mathrm{d} z,", "formula_001648": "P", "formula_001647": "\\rho\\left(\\frac{\\partial\\bm{u}}{\\partial t} + \\bm{u}\\cdot\\nabla\\bm{u} \\right)= -\\nabla p + \\nabla \\cdot \\left[\\mu\\left( \\nabla \\bm{u}+\\nabla\\bm{u}^T\\right)\\right] +  \\bm{f}_\\gamma,", "formula_001649": "\\eta_{\\mathrm{cr},\\pm}(t)=\\frac{V_{tot}\\pm \\sqrt{ V_{tot}^2-4\\left[-(\\gamma/\\rho)(1-\\cos\\vartheta_a)[\\Delta G(t)]-\\eta_i^2+V_{tot}\\eta_i\\right]}}{2}.", "formula_001650": "\\frac{\\mathrm{d} }{\\mathrm{d} t}\\left(\\frac{R}{t+t_0}\\right)=-\\frac{V_{init}}{V}\\frac{\\Delta_{init}}{t+t_0}\\left(\\frac{\\tau+t_0}{t+t_0}\\right)-\\frac{\\gamma}{\\rho}\\frac{\\left(1-\\cos\\vartheta_a\\right)}{V}\\left[1-\\left(\\frac{\\tau+t_0}{t+t_0}\\right)^2\\right].", "formula_001653": "(u,w)_{z=h}=0", "formula_001652": "\\frac{\\partial h}{\\partial t}+\\frac{x}{t+t_0}\\frac{\\partial h}{\\partial x}=w\\big|_{z=h}=-\\frac{h-h_{bl}}{t+t_0}.", "formula_001655": "R_{init}", "formula_001651": "X\\geq 1", "formula_001654": ">100\\,\\mathrm{m}\\cdot\\mathrm{s}^{-1}", "formula_001656": "\\mathrm{d} V_{tot}/\\mathrm{d} t=0", "formula_001657": "1.225\\,\\mathrm{kg}\\cdot\\mathrm{m}^{-3}", "formula_001659": "h", "formula_001658": "\\vartheta_a=\\pi/2", "formula_001660": "k_1\\mathrm{Re}^{1/3}-k_1(1-\\cos\\vartheta_a)^{1/2}(\\mathrm{Re}/\\mathrm{We})^{1/2}\\leq \\mathcal{R}_{max}/R_0\\leq k_1 \\mathrm{Re}^{1/3}", "formula_001661": "h(\\tau)=R_0/2", "formula_001662": "\\beta_{max}=\\frac{\\omega+\\sqrt{\\omega^2-8\\pi(1-\\cos\\vartheta)}}{4(1-\\cos\\vartheta)},\\qquad \\omega=\\frac{\\pi}{2}(1-b)\\mathrm{We}+2\\pi.", "formula_001663": "(\\lambda_{\\mathrm{s}}/R_0)\\times 100<0.28\\%", "formula_001664": "z\\geq h_{bl}", "formula_001667": "\\partial_x u+\\partial_z w=0", "formula_001665": "u_0=R/(t+t_0)", "formula_001669": "L_z=0.012\\,\\mathrm{m}", "formula_001666": "f(\\cdot)=\\text{Const.}", "formula_001668": "4\\Delta_{init}(\\tau+t_0)h_{init}/V_{tot}=B", "formula_001670": "K=\\mathrm{We}\\sqrt{\\mathrm{Re}}", "formula_001672": "\\mathrm{Re}=3,000", "formula_001671": "\\mathrm{We} \\gg 1", "formula_001673": "h_{init}^2", "formula_001674": "u_o(x,t)=\\frac{x}{t+t_0},", "formula_001675": "k_2", "formula_001677": "R_0=0.003\\mathrm{m}", "formula_001678": "h(R,t)\\equiv h", "formula_001680": "(1/2)\\rho U_0^2", "formula_001679": "h_{bl}\\propto \\sqrt{\\nu (t+t_0)}", "formula_001676": "f", "formula_001681": "\\frac{\\mathrm{d} P_{lam}}{\\mathrm{d} t}=\\rho\\lambda\\left[\\frac{R}{t+t_0}\\left[h(R,t)-h_{bl}(t)\\right]\\left(\\dot R-\\frac{R}{t+t_0}\\right)-\\tfrac{1}{4}\\frac{h_{bl} R^2}{(t+t_0)(t+t_1)}\\right].", "formula_001682": "h(x,t)", "formula_001683": "\\nu=\\mu/\\rho", "formula_001684": "A=\\ell_*^2/V_{tot}", "formula_001685": "\\frac{\\partial h}{\\partial t}+u\\big|_{z=h}\\frac{\\partial h}{\\partial x}=w\\big|_{z=h}.", "formula_001686": "2\\underbrace{R_{max}}_{=R(t_{max})}h(t_{max})=V_{tot}-V(t_{max})\\leq V_{tot}.", "formula_001687": "\\rho U_0^2 R_0^2", "formula_001688": "k_1=1.24", "formula_001690": "O(1)", "formula_001692": "h_{init}\\eta^2-V_{tot}\\eta-h_{init}\\eta_{init}^2+V_{tot}\\eta_{init}-[G(t)-G(\\tau)]=0.", "formula_001689": "4\\left[\\epsilon^2 X^2+A \\left(X^3+X-2x^2\\right)+B \\epsilon\\left(X^2-X\\right)\\right]=\\left[3A X^2+2x\\left(\\epsilon^2+\\epsilon B-2A\\right)+\\left(A-\\epsilon B\\right)\\right]^2.", "formula_001693": "\\sim \\mathrm{Re}^{1/5}", "formula_001691": "5.45\\times 10^{-6}\\,\\mathrm{m}", "formula_001694": "u_x", "formula_001696": "\\frac{\\mathrm{d}\\eta}{\\mathrm{d} t}=-\\Delta_{init}\\frac{V_{init}}{V_{tot}-2\\eta h_{init}}\\left(\\frac{\\tau+t_0}{t+t_0}\\right)^2-\\frac{\\gamma(\\tau+t_0)}{\\rho}\\frac{\\left(1-\\cos\\vartheta_a\\right)}{V_{tot}-2\\eta h_{init}}\\left[1-\\left(\\frac{\\tau+t_0}{t+t_0}\\right)^2\\right].", "formula_001695": "R\\left(\\frac{\\tau+t_0}{t+t_0}\\right)=\\frac{V_{tot}\\pm \\sqrt{V_{tot}^2-4h_{init}\\bigg\\{ -h_{init}\\eta_{init}^2+V_{tot}\\eta_{init}-[G(t)-G(\\tau)]\\bigg\\}}}{2h_{init}}.", "formula_001699": "R_0", "formula_001697": "h(x,t)=(t+t_0)^{-1}f[(x/(t+t_0)]+h_{PI}(t)", "formula_001702": "k_h", "formula_001700": "\\beta_{max}=\\mathcal{R}_{max}/R_0", "formula_001698": "4^{*}", "formula_001703": "a", "formula_001701": "\\eta_{\\mathrm{cr},\\pm}(t)=\\tfrac{1}{2}V_{tot}\\pm \\left[(\\gamma/\\rho)(1-\\cos\\vartheta_a)\\right]^{1/2}[\\Delta G(t)]^{1/2}", "formula_001705": "2250\\times 750", "formula_001704": "\\begin{eqnarray}\\frac{\\mathrm{d} V}{\\mathrm{d} t}&=&2\\left(\\overline{u}-U\\right)h,\\\\\\frac{\\mathrm{d} R}{\\mathrm{d} t}&=&U,\\\\V\\frac{\\mathrm{d} U}{\\mathrm{d} t}&=&2\\left[ \\left(\\overline{u}-U\\right)^2 -c^2\\right]h.\\end{eqnarray}", "formula_001706": "\\bm{u}=(0,0,-1)\\,\\mathrm{m}\\cdot\\mathrm{s}^{-1}", "formula_001707": "R_{init}=V_{tot}/(2h_{init})", "formula_001708": "\\begin{eqnarray}X&=& \\frac{t+t_0}{\\tau+t_0},\\\\Y&=&\\frac{R}{V_{tot}/(2h_{init})},\\\\A&=&\\frac{c_*^2(\\tau+t_0)^2}{V_{tot}},\\\\c_*^2&=&4(h_{init}/V_{tot})(\\gamma/\\rho)(1-\\cos\\vartheta_a),\\\\B&=&\\frac{4\\Delta_{init}(\\tau+t_0)h_{init}}{V_{tot}}\\\\\\epsilon&=&\\frac{V_{init}}{V_{tot}}.\\end{eqnarray}", "formula_001709": "\\Delta x=L_x/n_x", "formula_001710": "750", "formula_001711": "w(x,z,t)", "formula_001712": "V_{tot}", "formula_001715": "\\beta_{max}\\sim \\mathrm{Re}^{1/3}", "formula_001713": "V_i=0", "formula_001716": "\\mathcal{R}_{max} = R_{max} + 2a", "formula_001718": "k_1", "formula_001714": "\\vartheta_a", "formula_001717": "x", "formula_001719": "\\Delta G(t_*)", "formula_001720": "1^{*}", "formula_001722": "\\begin{eqnarray}\\frac{\\mathrm{d} V}{\\mathrm{d} t}&=&2\\left[ u_0\\left(h-h_{bl}\\right)-U h\\right],\\\\\\frac{\\mathrm{d} R}{\\mathrm{d} t}&=&U,\\\\V\\frac{\\mathrm{d} U}{\\mathrm{d} t}&=&2\\left[(u_0-U)^2\\left(h-h_{bl}\\right)+U^2h_{bl}\\right]\\\\&\\phantom{=}&\\phantom{aaaaa}+\\tfrac{1}{2}\\frac{h_{bl} R^2}{(t+t_0)(t+t_1)}-2\\frac{\\gamma}{\\rho}(1-\\cos\\vartheta_a),\\end{eqnarray}", "formula_001725": "\\bm{u}_r", "formula_001723": "\\rho", "formula_001729": "\\mathrm{We}\\rightarrow\\infty", "formula_001728": "\\mathrm{Re}^0", "formula_001726": "\\beta_{max}\\sim \\mathrm{We}", "formula_001721": "\\frac{\\mathrm{d} R}{\\mathrm{d} t}-\\frac{R}{t+t_0}=-\\frac{V_{init}}{V}\\Delta_{init}\\left(\\frac{\\tau+t_0}{t+t_0}\\right)-\\frac{\\gamma}{\\rho}\\frac{\\left(1-\\cos\\vartheta_a\\right)}{V}\\left[t+t_0-\\frac{(\\tau+t_0)^2}{t+t_0}\\right].", "formula_001724": "L_x", "formula_001730": "u_0+\\lambda_{\\mathrm{s}}\\frac{\\left(u_0-u_1\\right)}{d/2}=0,", "formula_001732": "\\frac{\\mathrm{d} }{\\mathrm{d} t}\\left(\\frac{\\Delta V}{h}\\right)=2(\\gamma/\\rho)(1-\\cos\\vartheta_a)(1/h)-\\frac{u_0}{t+t_0}(h_{bl}/h^2)\\Phi(t)\\leq 2(\\gamma/\\rho)(1-\\cos\\vartheta_a)(1/h).", "formula_001733": "\\partial h/\\partial t=0", "formula_001734": "h_{bl}\\propto\\sqrt{\\nu (t+t_1)}", "formula_001735": "\\frac{R_{max}}{R_0}\\sim \\tfrac{\\pi(1-b)}{4}  \\frac{\\mathrm{We}}{1-\\cos\\vartheta_a}.", "formula_001736": "h(R,t)>h_{bl}", "formula_001738": "k_0=0.80", "formula_001741": "X\\gg 1", "formula_001742": "\\mu=\\mathrm{e}^{\\int \\frac{1}{t+t_0}[1-(h_{bl}/h)]\\mathrm{d} t}=\\mathrm{e}^{-\\int (1/h)(\\mathrm{d} h/\\mathrm{d} t)\\mathrm{d} t}=\\mathrm{e}^{- \\int\\mathrm{d} h/h}=1/h.", "formula_001739": "40^\\circ", "formula_001740": "t", "formula_001737": "\\mathcal{R}_{max}/R_0\\sim \\mathrm{We}", "formula_001743": "\\mathrm{Re}", "formula_001744": "R(t)\\geq \\frac{\\tfrac{1}{2}V_{tot}-\\left[(\\gamma/\\rho)(1-\\cos\\vartheta_a)\\right]^{1/2}[\\Delta G(t)]^{1/2}}{h(t)}.", "formula_001745": "Y=\\beta_{max} X/\\mathrm{Re}^{1/3}", "formula_001746": "k_G", "formula_001752": "1\\,\\mathrm{mm}", "formula_001747": "Y_{max}\\sim (4/9)A^{-1}-\\sqrt{A (4/9)^3 A^{-3}}=\\tfrac{4}{27}A^{-1},\\qquad X\\gg 1.", "formula_001748": "R_{max}\\leq \\frac{V_{tot}}{2 h(t_{max})}.", "formula_001753": "h>h_{bl}", "formula_001751": "\\Delta=[R/(t+t_0)][1-(h_{bl}/h)]-(\\mathrm{d} R/\\mathrm{d} t)", "formula_001749": "\\frac{\\mathrm{d}\\Delta}{\\mathrm{d} t}+\\frac{\\Delta}{t+t_0}\\left(1-\\frac{h_{bl}}{h}\\right)=-2(\\Delta^2-c^2)(h/V)-\\frac{u_0}{t+t_0}\\frac{h_{bl}}{h}\\underbrace{\\left[2\\left(1-\\frac{h_{bl}}{h}\\right)+\\tfrac{1}{2}\\frac{t+t_0}{t+t_1}\\right]}_{=\\Phi(t)\\geq 0}.", "formula_001750": "R_{max}/R_0\\sim 0.36 \\mathrm{We}/(1-\\cos\\vartheta_a)", "formula_001754": "V_{1/2}", "formula_001756": "u_x+\\lambda_{\\mathrm{s}}\\frac{\\partial u_x}{\\partial z}=0,\\qquad z=0,", "formula_001758": "k_1\\mathrm{Re}^{1/3}-k_2 (1-\\cos\\vartheta_a)^{1/2} (\\mathrm{Re}/\\mathrm{We})^{1/2} \\leq \\frac{\\max R(t)}{R_0}\\leq k_1\\mathrm{Re}^{1/3} ,\\qquad \\mathrm{Re} \\gg 1,", "formula_001757": "0.072\\,\\mathrm{N}\\cdot\\mathrm{m}^{-1}", "formula_001755": "\\mathrm{Re}=\\frac{\\rho U_0 R_0}{\\mu},\\qquad \\mathrm{We}=\\frac{\\rho U_0^2 R_0}{\\gamma},", "formula_001759": "60^\\circ", "formula_001760": "F(z)=\\begin{cases} 0,& z<h_{bl},\\\\                                                       1,& z>h_{bl}.\\end{cases}", "formula_001761": "\\Delta z", "formula_001762": "\\mathrm{Re}^{-1}", "formula_001763": "\\begin{eqnarray}\\frac{R_{max}}{R_0}&=& Y_{max}\\left(\\frac{V_{tot}}{2h_{init}}\\right),\\\\                                      \t\t\t\t\t\t\t\t\t \t\t\t\t\t\t\t\t\t \t\t\t\t\t\t\t\t\t &\\stackrel{\\text{Eq.~}}{\\sim }&  \\tfrac{4}{27}\\frac{V_{tot}^2}{4 h_{init} (\\gamma/\\rho)(1-\\cos\\vartheta_a)(\\tau+t_0)^2}\\left(\\frac{V_{tot}}{2h_{init}}\\right),\\qquad X\\gg 1.\\end{eqnarray}", "formula_001764": "c_*=\\sqrt{ \\frac{\\gamma}{\\rho h_*}(1-\\cos\\vartheta_a)}", "formula_001765": "c", "formula_001766": "\\frac{R_{max}}{R_0}\\approx \\tfrac{1}{27} \\frac{V_{tot}^2}{ h_{init} U_0^2 (\\tau+t_0)^2 R_0 }\\left(\\frac{V_{tot}}{2h_{init}R_0}\\right)\\frac{\\mathrm{We}}{1-\\cos\\vartheta_a}.", "formula_001768": "\\eta=Rh", "formula_001767": "h=\\text{Const.}", "formula_001769": "\\eta_{\\mathrm{cr},\\pm}(t)", "formula_001770": "X\\approx 4/(9A)", "formula_001772": "U(t=\\tau)=\\frac{R_{init}}{\\tau+t_0}\\left(1-\\frac{h_{bl}}{h_{init}}\\right)-c(\\tau).", "formula_001776": "U\\equiv \\dot R", "formula_001774": "\\Delta \\leq (\\gamma/\\rho)(1-\\cos\\vartheta_a)(h/V)I_h(t).", "formula_001775": "k_2=(2 k_G)^{1/2}/k_h", "formula_001771": "R(t)\\leq \\frac{\\tfrac{1}{2}V_{tot}+\\left[(\\gamma/\\rho)(1-\\cos\\vartheta_a)\\right]^{1/2}[\\Delta G(t)]^{1/2}}{h(t)}.", "formula_001773": "\\begin{eqnarray*}V_{tot}^2+4h_{init}^2\\eta_{init}^2-4h_{init}V_{tot}\\eta_{init}&=&\\left(V_{tot}-2h_{init}\\eta_{init}\\right)^2,\\\\                                                    &=&\\left(V_{tot}-2h_{init}R_{init}\\right)^2,\\\\\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t&=&V_{init}^2.\\end{eqnarray*}", "formula_001777": "\\frac{\\mathrm{d} \\eta}{\\mathrm{d} t}\\geq -2(\\gamma/\\rho)(1-\\cos\\vartheta_a)\\frac{h^2 I_h(t)}{V_{tot}-2\\eta}.", "formula_001778": "b=1/2", "formula_001779": "u_o", "formula_001782": "\\alpha = 0", "formula_001781": "V\\frac{\\mathrm{d} U}{\\mathrm{d} t}=2\\left[(u_0-U)^2\\left(h-h_{bl}\\right)+U^2h_{bl}\\right]+\\tfrac{1}{2}\\frac{h_{bl} R^2}{(t+t_0)(t+t_1)}-2\\frac{\\gamma}{\\rho}(1-\\cos\\vartheta_a).", "formula_001784": "\\beta_{max}\\sim \\mathrm{We}^{1/2}", "formula_001783": "V_{tot}=2R_{init}h_{init}=\\pi R_0^2", "formula_001780": "\\frac{\\mathrm{d} }{\\mathrm{d} t}(Rh)\\geq -2(\\gamma/\\rho)(1-\\cos\\vartheta_a)(h^2/V)I_h(t).", "formula_001785": "Y(X)", "formula_001786": "t=t_*", "formula_001787": "h_{init}=R_0/2", "formula_001788": "R_{max}", "formula_001789": "\\begin{eqnarray}\\frac{\\mathrm{d} V}{\\mathrm{d} t}&=&2\\Delta h,\\\\\\frac{\\mathrm{d} R}{\\mathrm{d} t}&=&U,\\\\V\\frac{\\mathrm{d} U}{\\mathrm{d} t}&=&2(\\Delta^2-c^2)h.\\end{eqnarray}", "formula_001791": "z", "formula_001792": "\\Delta", "formula_001790": "4A X^3\\approx 9 A^2 X^4,", "formula_001793": "h_*", "formula_001794": "Y=X- \\sqrt{\\epsilon^2 X^2+A \\left(X^3+X-2X^2\\right)+B \\epsilon\\left(X^2-X\\right)},\\qquad X\\geq 1,", "formula_001797": "R_{max}/R_0\\sim 0.39 \\mathrm{We}/(1-\\cos\\vartheta_a)", "formula_001798": "1.24", "formula_001795": "\\frac{\\mathrm{d} R}{\\mathrm{d} t}-\\frac{R}{t+t_0}\\left(1-\\frac{h_{bl}}{h}\\right)\\geq -2(\\gamma/\\rho)(1-\\cos\\vartheta_a)(h/V)I_h(t).", "formula_001801": "\\gamma\\propto \\mathrm{We}^{-1}", "formula_001800": "h_*=k_h \\mathrm{Re}^{-1/3},\\qquad t_*=k_t \\mathrm{Re}^{1/3},\\qquad \\mathrm{Re} \\gg 1,", "formula_001796": "\\mathrm{d} R/\\mathrm{d} t=0", "formula_001799": "k_1 \\mathrm{Re}^{1/3}-k_2 \\mathrm{Re}^{1/2}\\mathrm{We}^{-1/2}(1-\\cos\\vartheta_a)^{1/2} \\leq \\beta_{max}\\leq k_1 \\mathrm{Re}^{1/3}, \\qquad \\text{(2D Cartesian)},", "formula_001802": "\\eta_{\\mathrm{cr},\\pm}(t)=\\frac{V_{tot}\\pm \\sqrt{ V_i^2+4(\\gamma/\\rho)(1-\\cos\\vartheta_a)[\\Delta G(t)]}}{2}.", "formula_001804": "\\begin{eqnarray}\\frac{\\mathrm{d} V}{\\mathrm{d} t}&=& -2U h_*,\\\\\\frac{\\mathrm{d} R}{\\mathrm{d} t}&=& U,\\\\V\\frac{\\mathrm{d} U}{\\mathrm{d} t}&=&2\\left[U^2h_* -\\frac{\\gamma}{\\rho}(1-\\cos\\vartheta_a)\\right].\\end{eqnarray}", "formula_001805": "\\mathrm{We}", "formula_001803": "\\tau=1", "formula_001806": "h\\rightarrow h/R_0", "formula_001807": "c_*^2=4(h_{init}/V_{tot})(\\gamma/\\rho)(1-\\cos\\vartheta_a)", "formula_001808": "\\tau=U_0/R_0", "formula_001809": "1.5\\times 10^{-5}\\,\\mathrm{m}^2\\cdot\\mathrm{s}^{-1}", "formula_001810": "\\beta_{max}\\approx \\frac{\\mathrm{We}\\,\\pi(1-b)}{4(1-\\cos\\vartheta)}.", "formula_001811": "f=0", "formula_001812": "2R_{init}", "formula_001816": "\\vartheta_a=90^\\circ", "formula_001813": "\\rho V\\frac{\\mathrm{d} U}{\\mathrm{d} t}=2\\rho\\bigg\\{\\left[h-h_{bl}(t)\\right]\\left(u_0-U\\right)^2+U^2h_{bl}\\bigg\\}+\\tfrac{1}{2}\\frac{\\rho h_{bl} R^2}{(t+t_0)(t+t_1)}-2\\gamma(1-\\cos\\vartheta_a),", "formula_001814": "{\\color{red}{R}}", "formula_001815": "5^{*}", "formula_001817": "\\Delta G =k_G \\mathrm{Re}^{1/3},\\qquad \\mathrm{Re} \\gg 1,", "formula_001818": "\\eta_{init}=R_{init}", "formula_001820": "\\beta_{max}\\sim \\mathrm{Re}^{1/5}", "formula_001821": "\\pi/(2k_h)=k_1", "formula_001819": "2a", "formula_001824": "\\begin{eqnarray}\\frac{\\mathrm{d} V}{\\mathrm{d} t}&=&2\\Delta h,\\\\V\\frac{\\mathrm{d} U}{\\mathrm{d} t}&=&2(\\Delta^2-c^2)h.\\end{eqnarray}", "formula_001823": "1/h(t_{max})\\leq 1/h_*", "formula_001822": "h_{init}\\eta^2-V_{tot}\\eta=h_{init}\\eta_{init}^2-V_{tot}\\eta_{init}+[G(t)-G(\\tau)],", "formula_001825": "\\lambda_{\\mathrm{s}}=8.32\\times 10^{-6}\\,\\mathrm{m}", "formula_001826": "h_{bl}\\propto \\sqrt{\\nu x/u_o}", "formula_001827": "\\frac{R_{max}}{R_0}\\sim \\tfrac{1}{27} \\frac{V_{tot}^2}{ h_{init}^3 R_0 }\\left(\\frac{V_{tot}}{2h_{init}R_0}\\right)\\frac{\\mathrm{We}}{1-\\cos\\vartheta_a},\\qquad \\mathrm{We}\\gg 1.", "formula_001837": "A", "formula_001836": "\\overline{u}=u_0[1-(h_{bl}/h)]", "formula_001828": "\\Delta=u_0\\left(1-\\frac{h_{bl}}{h}\\right)-U.", "formula_001840": "\\frac{R_{max}}{R_0}\\sim \\tfrac{4\\pi}{27} \\frac{\\mathrm{We}}{1-\\cos\\vartheta_a},\\qquad \\mathrm{We} \\gg 1.", "formula_001830": "\\eta", "formula_001835": "\\mathrm{d} Y/\\mathrm{d} X=0", "formula_001841": "21\\%", "formula_001832": "\\mathrm{Re}=\\infty", "formula_001831": "V\\Delta", "formula_001838": "k_1=\\pi /(2 k_h)", "formula_001845": "\\bm{u}", "formula_001842": "50^\\circ", "formula_001839": "R_{max}\\leq V_{tot}/(2h_*)", "formula_001834": "(u_0-U)^2(h-h_{bl})+U^2h_{bl} = \\left[ u_0\\left(1-\\frac{h_{bl}}{h}\\right)-U\\right]^2h + u_0^2 (h_{bl}/h)\\left(1-\\frac{h_{bl}}{h}\\right).", "formula_001833": "V_{tot}\\eta-\\eta^2\\geq V_{tot}\\eta_{init}-\\eta_{init}^2 -(\\gamma/\\rho)(1-\\cos\\vartheta_a)\\underbrace{\\left[2\\int_\\tau^t h^2 I_h(t)\\mathrm{d} t\\right]}_{=\\Delta G(t)}.", "formula_001829": "\\tfrac{1}{3} \\underbrace{\\frac{h_{init}^2}{U_0^2(\\tau+t_0)^2}}_{=1}\\frac{1}{R_0^2}\\left[ \\left(\\frac{V_{tot}}{2h_{init}}\\right)^3 h_{init} +\\left(\\frac{V_{tot}}{2h_{init}}\\right)h_{init}^3\\right]\\sim \\tfrac{1}{2}\t\\pi h_{init}^2,\\qquad \\mathrm{We}\\gg 1.", "formula_001847": "R_{max}\\geq R_* \\geq V_{tot}/(2h_*).", "formula_001844": "\\mathrm{We}=41.67", "formula_001846": "\\frac{\\mathrm{d} }{\\mathrm{d} t}(V\\Delta)+\\frac{V\\Delta}{t+t_0}=2hc^2=2(\\gamma/\\rho)(1-\\cos\\vartheta_a).", "formula_001843": "u(x,z,t)=u_o(x,t)F(z),", "formula_001848": "\\Delta=u_0-U", "formula_001849": "\\mathcal{R}_{max}=R+2a", "formula_001850": "\\Delta=u_0\\left(1-\\frac{h_{bl}}{h}\\right)-U;", "formula_001853": "t_0", "formula_001857": "\\begin{eqnarray}\\frac{\\mathrm{d} V}{\\mathrm{d} t}&=&2\\left(u_0-U\\right)h,\\\\\\frac{\\mathrm{d} R}{\\mathrm{d} t}&=&U,\\\\V\\frac{\\mathrm{d} U}{\\mathrm{d} t}&=&2\\left(u_0-U\\right)^2h-2\\gamma\\left(1-\\cos\\vartheta_a\\right).\\end{eqnarray}", "formula_001863": "\\beta_{max}=k_1 \\mathrm{Re}^{1/3}", "formula_001852": "L_z", "formula_001861": "V_{tot}=V_{init}+2R_{init}h_{init}", "formula_001859": "\\alpha=0.6908,\\qquad t_0=2.0,\\qquad t_1 =2.8907.", "formula_001871": "\\gamma", "formula_001874": "R_\\alpha", "formula_001854": "V\\leq V_{tot}", "formula_001856": "\\nu", "formula_001872": "1.0\\times 10^{-6}\\,\\mathrm{m}^2\\cdot\\mathrm{s}^{-1}", "formula_001855": "P_{tot}=P+\\rho\\lambda\\int_0^R \\mathrm{d} x\\int_0^h u \\mathrm{d} z,", "formula_001860": "R_{max}/R_0\\sim \\mathrm{We}", "formula_001867": "R=R(t_*)-c_* t", "formula_001864": "h_{init}^4\\sim V_{tot}^2/8", "formula_001866": "t\\rightarrow t U_0/R_0", "formula_001870": "R(t_*)h(t_*)\\geq \\eta_{\\mathrm{cr},-}(t_*)", "formula_001873": "80^\\circ", "formula_001862": "t_{breakup}\\sim [\\rho R_0^3/\\sigma]^{1/2}", "formula_001858": "\\max_t R(t)\\geq R(t_*)\\geq \\tfrac{1}{2}\\frac{V_{tot}}{h_*}-\\left[(\\gamma/\\rho)(1-\\cos\\vartheta_a)\\right]^{1/2}\\frac{[\\Delta G(t_*)]^{1/2}}{h_*}>0.", "formula_001869": "\\underbrace{\\frac{R}{V_{tot}/(2h_{init})}}_{=Y}=X\\pm X\\sqrt{ \\epsilon^2+4(h_{init}/V_{tot})[G(t)-G(\\tau)]},\\qquad \\epsilon=\\frac{V_{init}}{V_{tot}},\\qquad X=\\frac{t+t_0}{\\tau+t_0}.", "formula_001868": "\\left(\\overline{u}-U\\right)^2h - \\frac{\\gamma\\left(1-\\cos\\vartheta_{ap}\\right)}{\\rho} =0.", "formula_001865": "Y=k_1 X + \\text{Const.}", "formula_001876": "\\boldsymbol{X}", "formula_001875": "\\vec{x}_{t}", "formula_001877": "\\sigma^{(Q)}_{i,j}", "formula_001878": "\\sim 5", "formula_001879": "T", "formula_001880": "\\varphi \\geq 0", "formula_001881": "\\boldsymbol{F}(t) = \\begin{pmatrix}        \\mathcal{F}(t) / C_1 \\\\        0     \\end{pmatrix}.", "formula_001882": "\\mathcal{N}\\left(1.258, 0.38\\right)", "formula_001884": "\\mathcal{N}\\left(-0.96, 0.29 \\right)", "formula_001886": "\\mathcal{N}(1.58, 0.128)", "formula_001889": "C_0^{(\\mathrm{SO}_2)}", "formula_001890": "\\lambda", "formula_001883": "\\begin{align}    J & = J_{guess} + J_{obs}  \\\\    & = \\dfrac{1}{2} \\left( \\vec{x}_0 - \\vec{\\chi} \\right)^T \\boldsymbol{P}^{-1} \\left( \\vec{x}_0 - \\vec{\\chi} \\right)  \\\\    & + \\dfrac{1}{2} \\left( \\vec{T}^{(1)} - \\vec{T}^{(obs)} \\right)^T \\boldsymbol{\\Sigma}_T^{-1} \\left( \\vec{T}^{(1)} - \\vec{T}^{(obs)} \\right)  \\\\    & + \\dfrac{1}{2} \\left( \\vec{Q} - \\vec{Q}^{(obs)} \\right)^T \\boldsymbol{\\Sigma}_Q^{-1} \\left( \\vec{Q} - \\vec{Q}^{(obs)} \\right). \\end{align}", "formula_001885": "f_2^{(CO_2)} = 0", "formula_001888": "\\boldsymbol{\\Sigma}_Q^{-1}", "formula_001896": "N", "formula_001893": "f_{2}^{(c)}", "formula_001895": "\\dfrac{\\Delta T_{slow}}{\\Delta T_{fast}} = \\dfrac{\\gamma}{\\lambda}.", "formula_001897": "C_i > 0", "formula_001891": "t = T-1", "formula_001892": "\\vec{\\chi} \\sim \\mathcal{N}\\left(\\vec{\\chi}^p, \\boldsymbol{P}\\right)", "formula_001887": "C_0^{(SO_2)}", "formula_001894": "b = \\dfrac{\\lambda + \\varepsilon \\gamma}{C_1} + \\dfrac{\\gamma}{C_2}", "formula_001898": "n^{\\mathrm{th}}", "formula_001900": "f_2^{(SO_2)}", "formula_001902": "\\vec{F}_t", "formula_001901": "^\\circ", "formula_001903": "\\xi", "formula_001899": "\\Delta^{(T)}_{t} := T_{t}^{(1)} - T_t^{obs},", "formula_001904": "t=T", "formula_001905": "\\Delta^{(Q)}_{t} := Q_{t} - Q_t^{obs}.", "formula_001906": "T^{(2)}_0", "formula_001907": "\\sim", "formula_001912": "\\phi_s", "formula_001910": "\\Delta T_{fast} \\approx TCR = \\dfrac{F_{2\\times}}{\\lambda + \\gamma}.", "formula_001908": "\\left(\\boldsymbol{L}_t\\right)^*", "formula_001921": "\\boldsymbol{\\Sigma}_T", "formula_001915": "\\boldsymbol{X}_t \\in \\mathbb{R}^N", "formula_001917": "N_{ens}", "formula_001911": "\\phi_s = \\dfrac{C_1}{2 \\gamma} \\left( b^* + \\sqrt{\\delta} \\right)", "formula_001914": "C_{0}^{(e)}", "formula_001920": "|| \\cdot ||_2", "formula_001916": "L_2", "formula_001909": "t' > t", "formula_001919": "\\log_{10}\\left(\\mathrm{Value}\\right)", "formula_001918": "f_2^{(\\mathrm{SO}_2)}", "formula_001913": "\\boldsymbol{\\Sigma}_Q", "formula_001922": "f_{3}^{(c)}", "formula_001924": "J_{guess} = \\dfrac{1}{2} \\left( \\vec{x}_0 - \\vec{\\chi} \\right)^T \\boldsymbol{P}^{-1} \\left( \\vec{x}_0 - \\vec{\\chi} \\right).", "formula_001923": "\\dfrac{d\\boldsymbol{T}_t}{dt} = \\boldsymbol{D} \\boldsymbol{T}_t + \\boldsymbol{F}_t,", "formula_001925": "\\boldsymbol{D} = \\begin{pmatrix}        -(\\gamma \\varepsilon + \\lambda)/C_1 & \\gamma \\varepsilon / C_1 \\\\        \\gamma / C_2 & - \\gamma / C_2    \\end{pmatrix},", "formula_001928": "T_{0}^{(2)}", "formula_001927": "\\dfrac{\\partial J_{guess}}{\\partial x^{(n)}_{0}} = p_{n,n} \\left( x_{0}^{(n)} - \\chi^{(n)} \\right) + \\sum_{j = 0}^{N} p_{n,j} \\left( x_{0}^{(j)} - \\chi^{(j)} \\right)", "formula_001929": "\\sigma^{(T)}_{i,j}", "formula_001926": "\\boldsymbol{\\Sigma}_T^{-1}", "formula_001930": "\\boldsymbol{L}_t := \\dfrac{\\partial \\boldsymbol{M}_t}{\\partial \\boldsymbol{X}_t}\\bigg|_{\\boldsymbol{X}_t}", "formula_001931": "\\vec{T}^{(obs)}", "formula_001932": "q_t = \\varepsilon_t", "formula_001934": "T_{0}^{(1)}", "formula_001933": "^{-1/2}", "formula_001935": "\\mathcal{N}\\left(0.46, 0.2\\right)", "formula_001938": "_2", "formula_001941": "\\varepsilon_t \\sim \\mathcal{N}\\left( 0 , \\sigma \\right)", "formula_001940": "\\vec{Q}^{(obs)}", "formula_001942": "\\varepsilon", "formula_001944": "\\Lambda_t \\approx 1", "formula_001945": "\\delta = b^2 - 4 \\dfrac{\\lambda \\gamma}{C_1 C_2}", "formula_001939": "f_{1}^{(e)}", "formula_001936": "\\boldsymbol{P}^{-1}", "formula_001937": "\\dfrac{\\partial J_{obs}}{\\partial x_T^{(n)}} = \\left( \\sigma_{T,T}^{(T)} \\Delta^{(T)}_T + \\sum_{\\substack{t' = 0 \\\\ t' \\neq T}} \\sigma_{t',T}^{(T)} \\Delta_{t'}^{(T)} \\right) \\dfrac{\\partial \\Delta_{T}^{(T)}}{\\partial x_{T}^{(n)}} + \\left( \\sigma_{T,T}^{(Q)} \\Delta^{(Q)}_T + \\sum_{\\substack{t' = 0 \\\\ t' \\neq T}} \\sigma_{t',T}^{(Q)} \\Delta_{t'}^{(Q)} \\right) \\dfrac{\\partial \\Delta^{(Q)}_{T}}{\\partial x_{T}^{(n)}}", "formula_001943": "\\begin{align}    \\mathcal{F}_t & = \\sum_{c \\in \\mathcal{C}} f_{1}^{(c)} \\ln\\left( \\dfrac{C^{(c)}_t}{C_{0}^{(c)}}\\right) + f_{2}^{(c)} \\left( C^{(c)}_t - C^{(c)}_0 \\right) + f_{3}^{(c)} \\left( \\sqrt{C^{(c)}_t} - \\sqrt{C^{(c)}_0} \\right)  \\\\    & + \\sum_{e \\in \\mathcal{E}} f_{1}^{(e)} \\ln\\left(1 + \\dfrac{E^{(e)}_t}{C_{0}^{(e)}} \\right) + f_{2}^{(e)} E^{(e)}_t \\end{align}", "formula_001946": "\\tau_f = \\dfrac{C_1 C_2}{2 \\lambda \\gamma} \\left( b - \\sqrt{\\delta} \\right)", "formula_001947": "\\mathcal{N}\\left(100, 30\\right)", "formula_001953": "\\boldsymbol{T}(t) = \\begin{pmatrix}        T^{(1)}(t) \\\\        T^{(2)}(t)    \\end{pmatrix},", "formula_001954": "f_{2}^{(e)}", "formula_001948": "3 \\ ^\\circ", "formula_001950": "\\delta \\boldsymbol{X}", "formula_001952": "\\varphi = 0.2", "formula_001949": "N \\times N", "formula_001951": "\\mathcal{N}\\left(8, 2.4\\right)", "formula_001955": "\\vec{\\chi}", "formula_001956": "\\sum_{a = 0}^{N} \\dfrac{\\partial x_{T}^{(a)}}{\\partial x_{T-1}^{(n)}} F_{T}^{(a)} \\equiv \\left(\\boldsymbol{L}_T\\right)^* \\vec{F}_T", "formula_001958": "\\tau_s", "formula_001959": "\\vec{x}_t", "formula_001957": "\\begin{align}    \\dfrac{d}{dt} \\left( \\boldsymbol{X}_t + \\delta \\boldsymbol{X}_t \\right) & = \\boldsymbol{M}_t\\left( \\boldsymbol{X}_t  \\right) + \\dfrac{\\partial \\boldsymbol{M}_t}{\\partial \\boldsymbol{X}_t}\\bigg|_{\\boldsymbol{X}_t} \\delta \\boldsymbol{X}_t + \\mathcal{O}\\left( || \\delta \\boldsymbol{X}_t ||^2 \\right) \\\\    & \\approx \\boldsymbol{M}_t\\left( \\boldsymbol{X}_t  \\right) + \\boldsymbol{L}_t \\delta \\boldsymbol{X}_t \\end{align}", "formula_001962": "\\phi_f", "formula_001964": "C_2", "formula_001960": "\\begin{align}    C_1 \\dfrac{dT_{t}^{(1)}}{dt} & = \\mathcal{F}_t - \\lambda T_{t}^{(1)} + \\varepsilon \\gamma \\left(T_{t}^{(2)} - T_{t}^{(1)}\\right) + q_t  \\\\    C_2 \\dfrac{dT_{t}^{(2)}}{dt} & = \\gamma \\left( T_{t}^{(1)} - T_{t}^{(2)} \\right)  \\\\    \\dfrac{dQ}{dt} & = \\mathcal{F}_t - \\lambda T_{t}^{(1)} + (\\varepsilon - 1) \\gamma \\left( T^{(2)}_t - T^{(1)}_t \\right) + q_t. \\end{align}", "formula_001965": "C_1", "formula_001966": "(i,j)^{\\mathrm{th}}", "formula_001969": "J_{guess}", "formula_001963": "\\lim_{\\alpha \\to 0} R_{\\alpha} \\to 1", "formula_001967": "b^* = \\dfrac{\\lambda + \\varepsilon \\gamma}{C_1} - \\dfrac{\\gamma}{C_2}", "formula_001961": "q_{t+1} = \\varphi q_t + \\varepsilon_{t+1}", "formula_001968": "d\\xi/dt = 0", "formula_001970": "\\mathcal{N}\\left(4.58, 0.519\\right)", "formula_001971": "\\alpha", "formula_001972": "Q_t", "formula_001974": "\\Lambda_t := \\dfrac{\\langle \\boldsymbol{L} \\delta \\boldsymbol{X}, \\boldsymbol{L} \\delta \\boldsymbol{X} \\rangle}{\\langle \\delta \\boldsymbol{X} , \\boldsymbol{L}^* \\left( \\boldsymbol{L} \\delta \\boldsymbol{X} \\right) \\rangle}", "formula_001976": "f_3^{(\\mathrm{CO}_2)}", "formula_001979": "\\Phi_\\alpha", "formula_001978": "\\varepsilon > 0", "formula_001975": "\\sigma", "formula_001973": "\\begin{align}    J_{obs} & = \\dfrac{1}{2}          \\left( \\vec{T}^{(1)} - \\vec{T}^{(obs)} \\right)^T \\boldsymbol{\\Sigma}_T^{-1} \\left( \\vec{T}^{(1)} - \\vec{T}^{(obs)} \\right)  \\\\        & + \\dfrac{1}{2} \\left( \\vec{Q} - \\vec{Q}^{(obs)} \\right)^T \\boldsymbol{\\Sigma}_Q^{-1} \\left( \\vec{Q} - \\vec{Q}^{(obs)} \\right) \\end{align}", "formula_001977": "\\begin{align}    \\Delta T_{slow} & \\approx ECS - TCR = \\dfrac{F_{2\\times}}{\\lambda} - \\dfrac{F_{2\\times}}{\\lambda + \\gamma}\\\\    & = \\dfrac{\\gamma F_{2\\times}}{\\lambda (\\gamma + \\lambda)}. \\end{align}", "formula_001980": "T^{(1)}_0", "formula_001981": "x_T^{(n)}", "formula_001982": "f_1^{(\\mathrm{CO}_2)}", "formula_001983": "f_1^{(\\mathrm{SO}_2)}", "formula_001984": "\\phi_f = \\dfrac{C_1}{2\\gamma} \\left( b^* - \\sqrt{\\delta} \\right)", "formula_001986": "i \\in \\{ 1 , 2 \\}", "formula_001985": "\\Phi_\\alpha := \\dfrac{J\\left(\\boldsymbol{X} + \\alpha \\boldsymbol{h}\\right) - J\\left(\\boldsymbol{X}\\right)}{\\alpha \\boldsymbol{h}^T \\boldsymbol{\\nabla}J\\left( \\boldsymbol{X} \\right)},", "formula_001991": "t", "formula_001987": "\\mathcal{E}", "formula_001990": "\\mathcal{N}(0, 0.27)", "formula_001989": "R_\\alpha := \\dfrac{||\\boldsymbol{M}_t \\left( \\boldsymbol{X}_t + \\alpha \\delta \\boldsymbol{X}_t \\right) - \\boldsymbol{M}_t \\left( \\boldsymbol{X}_t \\right)||_2}{\\alpha || \\boldsymbol{L}_t \\delta \\boldsymbol{X}_t ||_2}", "formula_001992": "f_{1}^{(c)}", "formula_001993": "\\lim_{\\alpha \\to 0} \\Phi_\\alpha \\to 1", "formula_001988": "\\mathcal{N}\\left(-0.0047, 0.0014\\right)", "formula_001994": "f_3^{(CO_2)}", "formula_001995": "\\begin{align}    C_1 \\dfrac{dT_{t}^{(1)}}{dt} & = \\mathcal{F}_t - \\lambda T_{t}^{(1)} + \\varepsilon \\gamma \\left(T_{t}^{(2)} - T_{t}^{(1)}\\right) + q_{t}  \\\\    C_2 \\dfrac{dT_{t}^{(2)}}{dt} & = \\gamma \\left( T_{t}^{(1)} - T_{t}^{(2)} \\right) \\end{align}", "formula_001996": "Q_t = C_1 T_{t}^{(1)} + C_2 T_{t}^{(2)}.", "formula_001998": "\\boldsymbol{M}_t : \\mathbb{R}^N \\to \\mathbb{R}^N", "formula_002000": "\\mathcal{N}\\left(0.06, 0.2\\right)", "formula_001997": "\\dfrac{d\\boldsymbol{X}_t}{dt} = \\boldsymbol{M}_t\\left( \\boldsymbol{X}_t \\right).", "formula_001999": "\\dfrac{\\partial J_{obs}}{\\partial x_T^{(n)}} = \\sum_{k \\in \\{T, Q\\}}\\left( \\sigma_{T,T}^{(k)} \\Delta^{(k)}_T + \\sum_{\\substack{t' = 0 \\\\ t' \\neq T}} \\sigma_{t',T}^{(k)} \\Delta_{t'}^{(k)} \\right) \\dfrac{\\partial \\Delta_{T}^{(k)}}{\\partial x_{T}^{(n)}} =: F_T^{(n)}", "formula_002002": "\\boldsymbol{h}", "formula_002025": "p_{i,j}", "formula_002008": "\\alpha > 0", "formula_002009": "T_t^{(2)}", "formula_002014": "\\delta \\boldsymbol{X}_t", "formula_002018": "f_1^{(CO_2)}", "formula_002011": "\\ln2", "formula_002012": "(i, j)^{\\mathrm{th}}", "formula_002010": "\\boldsymbol{P}", "formula_002003": "t = T - 1", "formula_002001": "\\varepsilon_t", "formula_002021": "\\dfrac{\\partial J_{obs}}{\\partial x_{T-1}^{(n)}} = F_{T-1}^{(n)} + \\sum_{a = 0}^{N} \\dfrac{\\partial x_{T}^{(a)}}{\\partial x_{T-1}^{(n)}} F_{T}^{(a)}.", "formula_002005": "\\lambda > 0", "formula_002004": "\\boldsymbol{I}", "formula_002020": "\\vec{\\varepsilon} \\sim \\mathcal{N}(\\vec{0}, \\boldsymbol{I})", "formula_002022": "\\boldsymbol{h} = \\boldsymbol{\\nabla} J(\\boldsymbol{X})", "formula_002017": "\\vec{x}_0", "formula_002015": "t = T \\to t = 0", "formula_002023": "\\gamma > 0", "formula_002013": "f_{1}^{(CO_2)}", "formula_002024": "T^{(1)}_{hist}(t) = \\dfrac{1}{\\phi_s - \\phi_f} \\left( \\left[ \\phi_s T^{(1)}_0 - T^{(2)}_0 \\right] e^{-t/\\tau_f} + \\left[ T^{(2)}_0 - \\phi_f T^{(1)}_0 \\right] e^{-t/\\tau_s} \\right),", "formula_002007": "q_t", "formula_002016": "\\boldsymbol{X}_t", "formula_002019": "\\varphi", "formula_002006": "\\Lambda_t", "formula_002026": "^{-2}", "formula_002027": "T_t^{(1)}", "formula_002028": "\\mathcal{N}\\left(0.086, 0.026\\right)", "formula_002029": "^{-1}", "formula_002031": "\\to", "formula_002030": "T^{(1)}(t) = T_{hist}^{(1)}(t) + \\underbrace{\\dfrac{\\phi_s}{C_1 (\\phi_s - \\phi_f)} \\int_{0}^{t}\\mathcal{F}(\\zeta) e^{-(t - \\zeta)/\\tau_f} d\\zeta}_{=: T_{fast}^{(1)}(t)} - \\underbrace{\\dfrac{\\phi_f}{C_1 (\\phi_s - \\phi_f)} \\int_{0}^{t}\\mathcal{F}(\\zeta) e^{-(t - \\zeta)/\\tau_s} d\\zeta}_{=: T_{slow}^{(1)}(t)},", "formula_002032": "\\mathcal{C}", "formula_002034": "\\mathcal{N}\\left(170.6, 51.2\\right)", "formula_002033": "\\mathcal{N}\\left(0.7, 0.21\\right)", "formula_002035": "\\tau_f", "formula_002036": "\\gamma", "formula_002037": "x_{0}^{(n)}", "formula_002038": "J_{obs}", "formula_002039": "\\tau_s = \\dfrac{C_1 C_2}{2 \\lambda \\gamma} \\left( b + \\sqrt{\\delta} \\right)", "formula_002040": "\\mu\\textrm{m}", "formula_002041": "_{1/2}", "formula_002042": "{\\leq}0.87", "formula_002043": "1\\sigma", "formula_002044": "{<}0.5", "formula_002046": "\\infty", "formula_002048": "^1", "formula_002045": "\\times", "formula_002047": "^{40}", "formula_002049": "\\leq0.8", "formula_002050": "\\mu\\textrm{m}^2", "formula_002051": "(^2)", "formula_002052": "\\left(-10 \\pm 1\\right)", "formula_002053": "^+", "formula_002055": "\\sqrt{2}", "formula_002054": "\\mu", "formula_002056": "\\sigma", "formula_002057": "\\textrm{S} \\ge 0.8", "formula_002060": "\\textrm{NA} = 0.30", "formula_002059": "{\\sim}540~\\mu\\textrm{m}", "formula_002058": "r_\\textrm{Airy} = 0.87~\\mu\\textrm{m}", "formula_002061": "^{+}", "formula_002062": "3.45 \\times 3.45", "formula_002063": "^2", "formula_002064": "d_\\textrm{gap}", "formula_002065": "\\mathbf{h}", "formula_002070": "\\text{Nb}_3", "formula_002069": "T", "formula_002068": "^\\text{1,2}", "formula_002067": "\\begin{aligned}P'_{\\text{eddy}} = \\sum_{k=1}^M  \\Weightk{k} \\, L'_0 \\, \\Irevkdt{k} \\, \\Ieddyk{k} = \\sum_{k=1}^M \\Weightk{k}\\, L'_0\\, \\Timeck{k}\\, \\Irevkdt{k}^2,\\end{aligned}", "formula_002073": "\\begin{aligned}P'_{\\text{irr}} = \\sum_{k=1}^M  \\Weightk{k} \\ L'_0 \\ \\Irevkdt{k} \\ \\Iirrk{k},\\end{aligned}", "formula_002074": "^\\text{2}", "formula_002075": "b\\ne b_0", "formula_002078": "\\begin{aligned}P'_{\\text{total}} = P'_{\\text{irr}} + P'_{\\text{eddy}} + P'_{\\text{joule}}\\end{aligned}", "formula_002079": "\\begin{align}\\Phi'_{\\text{int}} &= \\int_{\\Omega_{\\text{c}}} \\frac{1}{2\\pi r} \\ \\mu_0\\,\\mathbf{h} \\cdot \\hat{\\vec e}_{\\varphi} \\ \\mathrm{d}\\Omega_{\\text{c}},\\end{align}", "formula_002080": "\\begin{align}I_{\\text{rev}} &= \\left\\{\\begin{aligned}& I_{\\text{rev}}^\\text{prev} \\quad &&\\text{if } \\left| I - I_{\\text{rev}} \\right| < \\xi,\\\\& I  - \\xi \\ \\dot I /|\\dot I |\\quad &&\\text{if } \\left|I  - I_{\\text{rev}}\\right| \\geq \\xi,\\\\\\end{aligned}\\right.\\end{align}", "formula_002084": "b_0(I)", "formula_002081": "I", "formula_002083": "f\\to\\infty", "formula_002082": "(r,\\phi, z)", "formula_002085": "V'", "formula_002087": "\\beta", "formula_002088": "\\Fluxintk{k}", "formula_002089": "j_{\\text{c}}(b_0,T_0)", "formula_002091": "\\xi", "formula_002090": "k", "formula_002092": "(b,T)", "formula_002093": "I_{\\text{eddy}}", "formula_002094": "R'_{\\text{m}}", "formula_002095": "b_0", "formula_002098": "\\begin{aligned}f_\\xi(b, T) = \\frac{j_{\\text{c}}(b_0 + b,T)}{j_{\\text{c}}(b_0,T_0)},\\end{aligned}", "formula_002099": "M", "formula_002103": "\\Coerck{1} = 0", "formula_002100": "I(t)", "formula_002102": "\\mu_0 = 4\\pi \\times 10^{-7}", "formula_002104": "\\begin{aligned}V'(t) = - \\Dot{\\Phi}'_{\\text{int}}\\end{aligned}", "formula_002106": "M=7", "formula_002108": "T\\ne T_0", "formula_002123": "^\\text{1}", "formula_002119": "G", "formula_002105": "\\begin{aligned}\\xi(b,T) = f_\\xi(b, T) \\ \\xi,\\end{aligned}", "formula_002121": "\\Timeck{k}", "formula_002109": "\\text{prev}", "formula_002110": "\\Phi'_{\\text{int}}", "formula_002116": "\\begin{align}G &= \\left\\{\\begin{aligned}& G^{\\text{prev}} &&\\text{if } \\left|I - G^{\\text{prev}}\\right| < \\xi,\\\\& I - \\xi \\ \\dot I/|\\dot I|\\quad &&\\text{if } \\left|I - G^{\\text{prev}}\\right| \\geq \\xi,\\\\\\end{aligned}\\right.\\end{align}", "formula_002111": "_2", "formula_002115": "I_{\\text{irr}}", "formula_002124": "R'_{\\text{f}}(I_{\\text{f}})", "formula_002120": "-I_{\\text{c}}", "formula_002125": "G^{\\text{prev}}", "formula_002112": "1/ \\sqrt{f}", "formula_002122": "G = I_{\\text{rev}} + I_{\\text{eddy}}", "formula_002118": "j_{\\text{c}}", "formula_002113": "f_\\xi(b, T)", "formula_002117": "I_{\\text{c}}", "formula_002127": "\\eta", "formula_002129": "b", "formula_002140": "\\begin{aligned}  I = I_{\\text{rev}} + I_{\\text{irr}} + I_{\\text{eddy}},\\end{aligned}", "formula_002132": "j_{\\text{c}}(b,T)", "formula_002131": "I_{\\text{rev}}", "formula_002135": "\\begin{aligned}I = I_{\\text{rev}} + I_{\\text{irr}}.\\end{aligned}", "formula_002130": "\\Coerck{M+1} = \\max(I)", "formula_002136": "L'_0 = \\frac{\\mu_0}{4 \\pi}", "formula_002139": "I = \\Coerck{k}", "formula_002146": "1/ f", "formula_002147": "\\Coerck{k}", "formula_002149": "3M", "formula_002151": "\\Ieddyk{k}", "formula_002152": "10\\%", "formula_002153": "\\Omega_{\\text{c}}", "formula_002156": "[0, I_{\\text{c}})", "formula_002157": "\\Irevk{k}", "formula_002154": "R'_{\\text{eq}}", "formula_002161": "\\begin{aligned}\\Weightk{1} = \\frac{\\Fluxintk{2} - \\Fluxintk{1}}{L'_0(\\Coerck{2} - \\Coerck{1})}, \\quad \\Weightk{k} = \\frac{\\Fluxintk{k+1} - \\Fluxintk{k}}{L'_0(\\Coerck{k+1} - \\Coerck{k})} - \\sum_{i=1}^{k-1} \\Weightk{i},\\end{aligned}", "formula_002163": "\\begin{aligned}P'_{\\text{joule}} = R'_{\\text{eq}}\\, I^2,\\end{aligned}", "formula_002166": "\\varepsilon_\\text{avg}", "formula_002167": "\\Weightk{k}", "formula_002168": "\\Dot{\\Phi}'_{\\text{int}}", "formula_002165": "M \\gtrsim 10", "formula_002169": "I(t)V'(t)", "formula_002170": "I >> I_{\\text{c}}", "formula_002172": "\\begin{aligned}\\Phi'_{\\text{int}} = \\sum_{k=1}^M  \\Weightk{k} \\ L'_0 \\ \\Irevk{k},\\end{aligned}", "formula_002173": "\\hat{\\delta N}_{kx}=-(Nq/T)\\hat{\\delta \\Phi}_{k_x}", "formula_002174": "1/L_{N}=d({\\rm ln}N)/dx=10^{-5} \\rho_e^{-1}", "formula_002175": "k_y\\rho_e=0.4", "formula_002176": "\\begin{align}&\\hat{\\delta f}_{k_x} = -\\frac{q}{\\bar{T}} \\Bigg\\{ \\hat{\\delta \\Phi}_{k_x} - \\left( \\sum_{k_x'} \\hat{\\omega}_{d, k_x - k_x'} \\hat{\\delta \\Phi}_{k_x'} - \\omega \\hat{\\delta \\Phi}_{k_x} \\right)  \\\\&\\sum_{\\substack{m, m'\\\\n,'n} = -\\infty}^{\\infty}i^{(m'-m)}\\f{ J_m\\left(-\\frac{k_x v_{\\perp}}{\\Omega}\\right) J_{m'}\\left(\\frac{k_x v_{\\perp}}{\\Omega}\\right) J_n\\left(\\frac{k_y v_{\\perp}}{\\Omega}\\right) J_{n'}\\left(\\frac{k_y v_{\\perp}}{\\Omega}\\right) } {k_z v_z + (n - m) \\Omega - \\omega}e^{i(m' - m + n - n')\\theta}\\Bigg\\} f_0\\end{align}", "formula_002177": "\\hat{e}_x", "formula_002178": "L_z=2\\pi L_{\\rm ref}", "formula_002179": "y-x", "formula_002180": "\\vec{v}_c", "formula_002181": "\\omega_T", "formula_002182": "T", "formula_002183": "\\omega_{Te}^{\\rm ext}=\\omega_{Te}", "formula_002185": "20", "formula_002184": "L_x=1/(\\hat{s}k_y)=2.5\\rho_e", "formula_002186": "Q_e/Q_{GB}\\simeq 1200", "formula_002187": "\\begin{align}\\delta \\hat{N}_{k_x} = -\\frac{N q}{T} \\left\\{ \\hat{\\delta \\Phi}_{k_x} - \\left( \\sum_{k'_x} \\hat{\\omega}_{d,k_x - k'_x} \\hat{\\delta \\Phi}_{k'_x} - \\omega \\hat{\\delta \\Phi}_{k_x} \\right) \\frac{1}{\\omega} \\left[ W\\left( \\frac{\\omega}{|k_z| v_{th}} \\right) - 1 \\right]Y(k_x\\rho,k_y\\rho),\\right\\}\\end{align}", "formula_002188": "L_{v_\\parallel}=3\\sqrt{2}~v_{th,e}", "formula_002191": "\\omega_r/k_y", "formula_002190": "\\omega_{N}^{\\rm ext}=8, 12", "formula_002189": "R", "formula_002192": "\\epsilon(\\omega)", "formula_002193": "\\omega_r", "formula_002194": "{\\bf R}", "formula_002195": "\\omega^{\\rm ext}_{Te}/\\omega_{Te}=0.2", "formula_002197": "h_z=\\nu_z/\\Delta z^4", "formula_002196": "\\omega_{Te}", "formula_002199": "-\\infty", "formula_002198": "\\nu_e^*=0.3, 2.8", "formula_002200": "\\begin{align}\\begin{bmatrix}A_{-1,-1} & A_{-1,0} & 0\\\\A_{0,-1} & A_{0,0} & A_{0,1}\\\\0 & A_{1,0} & A_{1,1}\\end{bmatrix}\\begin{bmatrix}\\hat{\\delta \\Phi}_{-1}\\\\\\hat{\\delta \\Phi}_0\\\\\\hat{\\delta \\Phi}_1\\end{bmatrix}=0,\\end{align}", "formula_002201": "\\partial f_0(x,H)/\\partial \\vec{v}=-(\\vec{v}/v_{th}^2)f_0(x,H)", "formula_002202": "1", "formula_002203": "h_z", "formula_002204": "\\hat{\\omega}_{Te}^{\\rm ext}/\\tilde{\\omega}_{Te}=0,\\ 0.2", "formula_002206": "v_z", "formula_002207": "\\lambda_D=\\epsilon_0 T/(Nq^2)", "formula_002205": "\\hat{\\delta f}_{kx}", "formula_002208": "(x,y,z)", "formula_002209": "E\\times B", "formula_002210": "\\langle\\cdot\\rangle", "formula_002211": "|\\omega|\\ll|\\Omega|", "formula_002212": "\\nabla B", "formula_002213": "L_{\\mu}=9~T_e/B_{0,\\rm axis}", "formula_002214": "\\simeq 0", "formula_002216": "N", "formula_002215": "\\omega_{Te}=60", "formula_002218": "\\vec{B}", "formula_002219": "[\\vec{r}',\\vec{v}']", "formula_002221": "z", "formula_002220": "\\omega_{E\\times B}", "formula_002222": "r", "formula_002223": "\\vec{v}_{\\nabla B}", "formula_002224": "L_{\\rm ref}", "formula_002225": "\\omega^{\\rm ext}_{Te}/\\omega_{Te}=0", "formula_002226": "{\\bf Q}", "formula_002227": "\\omega_{Te}=2.2", "formula_002228": "\\begin{align}e^{iz\\,{\\rm sin}\\theta}&=\\sum_{n=-\\infty}^{\\infty}J_n(z)e^{in\\theta},\\\\e^{iz\\,{\\rm cos}\\theta}&=\\sum_{n=-\\infty}^{\\infty} i^n J_n(z)e^{in\\theta}.\\end{align}", "formula_002229": "27.6", "formula_002231": "\\omega_d(x)", "formula_002230": "\\omega_{Te}=5.5", "formula_002232": "N_x\\times N_z\\times N_{v_\\parallel}\\times N_\\mu=4\\times 32\\times 32\\times 8", "formula_002233": "\\omega_{Te}=20", "formula_002234": "\\omega_{Te}^{\\rm tot}", "formula_002235": "\\hat{\\omega}_d^{\\rm ext}=2\\hat{\\omega}_{d,1}", "formula_002236": "\\hat{\\delta \\Phi}_{0}=0", "formula_002237": "\\omega \\in \\mathbb{C}", "formula_002238": "\\omega_N^{\\rm eff}", "formula_002239": "\\omega_N=0", "formula_002240": "k_z=1/L_{\\rm ref}", "formula_002241": "400\\rho_e \\times 15.7 \\rho_e", "formula_002242": "z=0", "formula_002244": "A_{-1,-1}, A_{0,0}", "formula_002245": "\\beta=0", "formula_002246": "\\omega_{Te}^{\\rm ext}", "formula_002247": "\\omega_N", "formula_002248": "\\omega_r={\\rm Imag}({\\rm ln}[\\delta \\Phi(t+\\Delta t)/\\delta \\Phi(t)])/\\Delta t", "formula_002249": "L_z=2\\pi", "formula_002250": "\\vec{v}_E=-\\nabla\\delta \\Phi\\times\\vec{B_0}/B_0^2", "formula_002251": "\\hat{\\delta N}_{kx} = \\int d \\vec{v}\\ \\hat{\\delta f}_{kx}", "formula_002253": "\\hat{e}_z", "formula_002252": "\\begin{align}\\delta f &=\\sum_{k_x}\\hat{\\delta f} e^{i(k_xx+k_yy+k_zz - \\omega t)},\\\\\\delta \\Phi &=\\sum_{k_x}\\hat{\\delta \\Phi} e^{i(k_xx+k_yy+k_zz - \\omega t)}.\\end{align}", "formula_002255": "L_x=2.5\\rho_e", "formula_002254": "k_y", "formula_002256": "\\omega_{\\rm dia}=-\\nabla P\\times\\vec{B_0}/(qB_0^2)", "formula_002257": "\\mathcal{E}=mv^2/2", "formula_002258": "\\rho^\\star=k_{y,\\min}\\rho_i(r_0/a)(1/q_0)", "formula_002259": "t v_{th,e}/R=700", "formula_002261": "\\nu_z\\partial^4/\\partial z^4", "formula_002262": "k_y\\rho_e=0.1", "formula_002263": "\\hat{\\omega}_{Ti}^{\\rm ext}=0", "formula_002264": "\\omega_T^{\\rm ext}", "formula_002265": "\\tilde{\\omega}_d= \\tilde{\\omega}_N + \\tilde{\\omega_T}", "formula_002266": "-", "formula_002268": "\\omega_{Te}^{\\rm eff}=\\omega_{Te}^{\\rm tot} + (R/N_0)\\,d\\langle \\delta N\\rangle/dx", "formula_002267": "\\hat{\\omega}_{Te}^{\\rm ext}/\\tilde{\\omega}_{Te}=\\omega_{Te}^{\\rm ext}/\\omega_{Te}=0, 0.2", "formula_002270": "\\mu", "formula_002272": "D/Dt\\vert_{u.t.}", "formula_002271": "\\begin{align}\\delta \\Phi=\\hat{\\delta \\Phi}_{-1}e^{-ik_{x}^{\\rm ext}x} + \\hat{\\delta \\Phi}_{0} + \\hat{\\delta \\Phi}_{1}e^{ik_{x}^{\\rm ext}x}.\\end{align}", "formula_002273": "v_\\parallel", "formula_002274": "\\omega_{Te}=6.9", "formula_002276": "\\hat{s}", "formula_002277": "m", "formula_002278": "\\Gamma", "formula_002279": "\\delta f", "formula_002280": "x=z", "formula_002281": "L_x=20\\rho_e", "formula_002282": "k_z", "formula_002283": "Te", "formula_002285": "r/R=0.18", "formula_002284": "L_{\\rm ref}=1/k_z", "formula_002286": "k_{x}^{\\rm ext}", "formula_002287": "\\hat{\\omega}_{Te}^{\\rm ext}=0", "formula_002290": "v_y=v_\\perp{\\rm cos}\\theta", "formula_002289": "12.8", "formula_002291": "k_x^{\\rm ext}\\rho_e=0.1", "formula_002292": "\\hat{\\omega}_{Te}^{\\rm ext}/\\tilde{\\omega}_{Te}=0.2", "formula_002293": "=400/k_{x,\\rm ind}", "formula_002294": "k_{x,\\rm ind}=5", "formula_002295": "\\omega_T=L_{\\rm ref}\\,d{\\rm ln} T/dx", "formula_002296": "N_x\\times N_z\\times N_{v_\\parallel}\\times N_\\mu=32\\times 16\\times 32\\times 8", "formula_002297": "\\tau=t'-t", "formula_002299": "|k\\lambda_D|\\ll 1", "formula_002298": "L", "formula_002300": "\\omega_{Te}^{\\rm eff}=\\omega_{Te}^{\\rm tot} + (R/T_{0,e})\\,d\\langle \\delta T_e\\rangle/dx", "formula_002301": "\\propto", "formula_002302": "1-10", "formula_002303": "Y=1", "formula_002304": "\\begin{align*}{\\bf Q}(\\tau) =\\left(\\begin{matrix}{\\rm sin}(\\Omega\\tau)  &  -[{\\rm cos}(\\Omega\\tau) - 1]  &  0\\\\{\\rm cos}(\\Omega\\tau) - 1  &  {\\rm sin}(\\Omega\\tau)  &  0\\\\0  &  0  &  \\Omega\\tau\\end{matrix}\\right)\\\\{\\bf R}(\\tau) =\\frac{1}{\\Omega}\\frac{d}{d\\tau} {\\bf Q}=\\left(\\begin{matrix}{\\rm cos}(\\Omega\\tau)  &  {\\rm sin}(\\Omega\\tau)  &  0\\\\-{\\rm sin}(\\Omega\\tau)  &  {\\rm cos}(\\Omega\\tau)  &  0\\\\0  &  0  &  1\\end{matrix}\\right).\\end{align*}", "formula_002305": "\\gamma-\\omega", "formula_002306": "N_x\\times N_{y}", "formula_002307": "\\hat{\\omega}_{N}^{\\rm ext}=0", "formula_002308": "\\nabla P", "formula_002309": "\\begin{align*}\\frac{\\partial \\delta f}{\\partial t}+ \\vec{v}_E \\cdot \\left( \\nabla f_{0} - \\frac{\\mu}{m v_{\\|}} \\nabla B_0 \\frac{\\partial f_{0}}{\\partial v_{\\|}} \\right)+ (\\vec{v}_E + \\vec{v}_{\\nabla B} + \\vec{v}_c) \\cdot \\vec{\\Gamma}+ v_{\\|} \\hat{b}_0 \\cdot \\vec{\\Gamma}- \\frac{\\mu}{m} \\left( \\hat{b}_0 + \\frac{\\vec{v}_c}{v_{\\|} } \\right) \\cdot \\nabla B_0 \\frac{\\partial \\delta f}{\\partial v_{\\|}} = 0;\\end{align*}", "formula_002311": "k_{y,\\min}", "formula_002310": "\\hat{s}=1", "formula_002312": "x", "formula_002314": "L_x=62.8\\rho_e", "formula_002313": "t\\ v_{th,e}/R", "formula_002315": "L_{\\rm ref}/L_{T_e}", "formula_002316": "k_y\\rho_e>2", "formula_002318": "\\Gamma=\\nabla \\delta f - (q/m v_\\parallel)\\nabla \\delta \\Phi \\partial f_0/\\partial v_\\parallel", "formula_002317": "\\delta\\Phi", "formula_002319": "\\omega_{Te}^{\\rm ext}=20", "formula_002321": "Q_e/Q_{GB}\\simeq 820", "formula_002320": "e^{i\\omega t}", "formula_002322": "y-", "formula_002323": "\\rho", "formula_002324": "\\pi", "formula_002325": "-\\nabla^2\\delta \\Phi=(1/\\epsilon_0)\\sum_{\\rm species}q\\delta N", "formula_002326": "\\hat{\\delta N}_{k_x}", "formula_002327": "N_x=32", "formula_002328": "m_e/m_i=1/1836", "formula_002329": "v_{E,x}\\nabla_x f_0", "formula_002330": "0", "formula_002332": "\\Omega", "formula_002333": "\\begin{align}\\omega_T \\longrightarrow \\underbrace{\\omega_T + \\omega_T^{\\rm ext}{\\rm sin}[(2\\pi/L_x)k_{x,{\\rm ind}}x + \\varphi^{\\rm ext}]}_{\\omega_T^{\\rm tot}},\\end{align}", "formula_002334": "(k_1)^2=(k_x^{\\rm ext})^2+k_{y}^2+k_{z}^2", "formula_002335": "k_x\\rho\\ll 1", "formula_002336": "f_0(X,\\mathcal{E})", "formula_002337": "\\omega_n=L_{\\rm ref}\\,d{\\rm ln} N/dx", "formula_002338": "1/L_{Te}", "formula_002340": "\\epsilon", "formula_002341": "10", "formula_002343": "k_x^{\\rm ext}\\ll k_y", "formula_002344": "k_{x,\\rm ind}=1", "formula_002345": "n=m", "formula_002346": "\\rho^\\star=\\rho_i/a", "formula_002347": "\\omega_{Te}^{\\rm eff}", "formula_002348": "^3", "formula_002349": "\\hat{s}=0.1", "formula_002350": "\\omega_r={\\rm real}(\\omega)", "formula_002352": "40", "formula_002353": "y", "formula_002354": "q_0=1.4", "formula_002355": "1/(\\hat{s}k_y)", "formula_002356": "\\delta f(t=-\\infty)=0", "formula_002357": "0.5", "formula_002359": "(x,z)", "formula_002358": "q", "formula_002361": "^{1,2}", "formula_002360": "\\omega", "formula_002362": "\\bf A", "formula_002363": "\\hat{s}=(r/q_0)dq/dr=1", "formula_002364": "L_x \\times L_y", "formula_002365": "v_{E,x}\\nabla_xf_{0}", "formula_002366": "k_{x,{\\rm ind}}", "formula_002369": "^4", "formula_002367": "{\\rm det}({\\bf A})=0", "formula_002370": "A_{1,1}", "formula_002368": "\\begin{align*}f_0(X,\\mathcal{E})=\\frac{N(X)}{[2\\pi T(X)/m]^{3/2}}e^{-\\mathcal{E}/T(X)},\\end{align*}", "formula_002371": "k_y=k_{y,\\min}", "formula_002372": "^2", "formula_002373": "\\begin{align}\\omega_d(x)&=\\hat{\\omega}_{d,1}e^{-ik_{x}^{\\rm ext}x} + \\hat{\\omega}_{d,0} + \\hat{\\omega}_{d,1}e^{ik_{x}^{\\rm ext}x} \\\\&=\\hat{\\omega}_{d,0} + \\hat{\\omega}_d^{\\rm ext}~{\\rm cos}(k_{x}^{\\rm ext}x)\\end{align}", "formula_002374": "= f-f_0", "formula_002375": "\\varphi^{\\rm ext}", "formula_002376": "\\omega_{Te}^{ext}=20", "formula_002377": "1/(k\\lambda_D)^2", "formula_002378": "X=x+v_y/\\Omega", "formula_002379": "\\omega^{\\rm ext}_{Te}", "formula_002380": "+", "formula_002381": "\\nu_e^*", "formula_002382": "\\hat{\\omega}_{Te}^{\\rm ext}", "formula_002383": "\\begin{align}\\delta f(\\vec{r},\\vec{v},t)= \\frac{q}{m} \\int_{-\\infty}^{t} dt' \\frac{\\partial \\delta \\Phi}{\\partial\\vec{r}}\\cdot\\frac{\\partial f_0}{\\partial \\vec{v}}\\bigg\\vert_{\\vec{r}',\\vec{v}',t'},\\end{align}", "formula_002384": "\\nu_e^*=27.6", "formula_002385": "L_{\\rm ref}=R", "formula_002386": "820", "formula_002387": "x-y", "formula_002388": "1/L_{Te}=d({\\rm ln}T_e)/dx=10^{-1}\\rho_e^{-1}", "formula_002389": "\\infty", "formula_002390": "160\\times 12", "formula_002391": "\\omega_{\\rm dia}", "formula_002393": "\\Omega=qB/m", "formula_002392": "v_x=v_\\perp{\\rm sin}\\theta", "formula_002394": "\\epsilon=\\rho/L \\ll 1", "formula_002396": "^1", "formula_002395": "W(z)=(1/\\sqrt{2}\\pi)\\int_\\Gamma dx\\ x/(x-z)e^{-x^2/2}", "formula_002397": "L_z", "formula_002398": "\\omega^{\\rm ext}_{Te}/\\omega_{Te}=0, 0.2", "formula_002399": "tv_{th,e}/R=20-100", "formula_002400": "\\begin{align}\\vec{r}'(t')=& \\vec{r} + \\frac{1}{\\Omega}{\\bf Q}(\\tau)\\vec{v},\\\\\\vec{v}'(t')=& {\\bf R}(\\tau)\\vec{v},\\end{align}", "formula_002401": "200\\times 16", "formula_002402": "\\gamma={\\rm imag}(\\omega)", "formula_002403": "k_{x,\\rm ind}= 5", "formula_002404": "\\tilde{\\omega}_T=(Tk_y/(qB)) d{\\rm ln} T/dx", "formula_002405": "Q_e/Q_{GB}\\simeq 590, 680", "formula_002406": "\\omega_{E\\times B}=(1/B_0)d^2\\langle\\delta \\phi\\rangle/dx^2", "formula_002407": "\\hat{\\delta \\Phi}_{-1}=\\hat{\\delta \\Phi}_{1}", "formula_002408": "\\omega^{\\rm ext}_N", "formula_002410": "T_e=T_i", "formula_002411": "f_0", "formula_002413": "k_x", "formula_002412": "x,y", "formula_002414": "T_e", "formula_002415": "B=|\\vec{B}|", "formula_002416": "x=0", "formula_002417": "400\\rho_e \\times 62.8 \\rho_e", "formula_002418": "\\omega_{\\rm dia}R/v_{th,e}=k_y\\rho_e\\omega_T^{\\rm tot}", "formula_002419": "\\tilde{\\omega}_N=(Tk_y/(qB)) d{\\rm ln} N/dx", "formula_002420": "\\omega_d", "formula_002421": "k_0^2=k_{y}^2+k_{z}^2", "formula_002422": "Q_e/Q_{GB}", "formula_002423": "^5", "formula_002425": "\\gamma", "formula_002424": "\\delta \\Phi", "formula_002426": "n_{\\min}=1", "formula_002428": "kv_\\perp/\\Omega=k\\rho\\, v_\\perp/v_{th}", "formula_002427": "\\omega_T^{\\rm tot}", "formula_002429": "\\begin{align*}&A_{-1,-1}=  1 + \\sum_{s} \\frac{1}{(k_1\\lambda_D)^2}\\left\\{1 + \\frac{\\omega - \\hat{\\omega}_{d,0}}{\\omega}\\bigg[W\\bigg(\\frac{\\omega}{|k_z|v_{th}}\\bigg) - 1\\bigg]Y(-k_x^{\\rm ext}\\rho,k_y\\rho)\\right\\},\\\\&A_{-1,0} =  -\\sum_{s} \\frac{1}{(k_1\\lambda_D)^2}\\left\\{\\frac{\\hat{\\omega}_{d,1}}{\\omega}\\bigg[W\\bigg(\\frac{\\omega}{|k_z|v_{th}}\\bigg) - 1\\bigg]Y(-k_x^{\\rm ext}\\rho,k_y\\rho)\\right\\},\\\\&A_{0,-1} =  -\\sum_{s} \\frac{1}{(k_0\\lambda_D)^2}\\left\\{\\frac{\\hat{\\omega}_{d,1}}{\\omega}\\bigg[W\\bigg(\\frac{\\omega}{|k_z|v_{th}}\\bigg) - 1\\bigg]Y(0,k_y\\rho)\\right\\},\\\\&A_{0,0} =  1 + \\sum_{s} \\frac{1}{(k_0\\lambda_D)^2}\\left\\{1 + \\frac{\\omega - \\hat{\\omega}_{d,0}}{\\omega}\\bigg[W\\bigg(\\frac{\\omega}{|k_z|v_{th}}\\bigg) - 1\\bigg]Y(0,k_y\\rho)\\right\\},\\\\&A_{0,1} =  -\\sum_{s} \\frac{1}{(k_0\\lambda_D)^2}\\left\\{\\frac{\\hat{\\omega}_{d,1}}{\\omega}\\bigg[W\\bigg(\\frac{\\omega}{|k_z|v_{th}}\\bigg) - 1\\bigg]Y(0,k_y\\rho)\\right\\},\\\\&A_{1,0} =  -\\sum_{s} \\frac{1}{(k_1\\lambda_D)^2}\\left\\{\\frac{\\hat{\\omega}_{d,1}}{\\omega}\\bigg[W\\bigg(\\frac{\\omega}{|k_z|v_{th}}\\bigg) - 1\\bigg]Y(k_x^{\\rm ext}\\rho,k_y\\rho)\\right\\},\\\\&A_{1,1} =  1 + \\sum_{s} \\frac{1}{(k_1\\lambda_D)^2}\\left\\{1 + \\frac{\\omega - \\hat{\\omega}_{d,0}}{\\omega}\\bigg[W\\bigg(\\frac{\\omega}{|k_z|v_{th}}\\bigg) - 1\\bigg]Y(k_x^{\\rm ext}\\rho,k_y\\rho)\\right\\},\\end{align*}", "formula_002430": "L_{\\rm ref}/L_{Te}", "formula_002431": "\\hat{\\delta \\Phi}_{-1}=\\hat{\\delta \\Phi}_{1}=\\pm \\hat{\\delta \\Phi}_{0}/\\sqrt{2}", "formula_002432": "z_0 = 0, \\, -1.6, \\, -3.2,", "formula_002433": "\\mathbf{x}", "formula_002434": "k_1 \\pm k_2", "formula_002436": "D_{0123} = -\\frac{1}{4} |k_1| \\left( 2 |k_1| - |k_1 + k_2| - |k_1 + k_3| - |k_0-k_3| - |k_0-k_2| \\right).", "formula_002435": "(u,w)", "formula_002437": "\\omega a^2 k^2/2", "formula_002438": "T", "formula_002439": "t=-4", "formula_002440": "k_1<2", "formula_002441": "\\begin{align} \\mathcal{C}^{(3)}_{i+j+k} &= \\frac{1}{\\pi} \\sqrt{\\frac{g}{2 \\omega_{i+j+k}}} \\left[ B^{(1)}_{i+j+k,i,j,k} - B^{(4)}_{-i-j-k,i,j,k} \\right] - \\frac{1}{4 \\pi^2} \\left[ \\sqrt{\\frac{\\omega_k}{\\omega_{i+j}}} |k_i+k_j| \\left( A^{(1)}_{i+j,i,j} - A^{(3)}_{-i-j,i,j} \\right) \\right.\\\\& \\left. + \\sqrt{\\frac{\\omega_{i+j}}{\\omega_k}} |k_k| \\left( A^{(1)}_{i+j,i,j} + A^{(3)}_{-i-j,i,j} \\right) \\right] - \\frac{1}{32 \\pi^3} \\sqrt{\\frac{2 \\omega_j \\omega_k}{g \\omega_i}} \\left[ |k_i| \\left( |k_i| - |k_i+k_j| - |k_i+k_k| \\right) \\right], \\\\  \\mathcal{C}^{(3)}_{i-j-k} &= -\\frac{1}{\\pi} \\sqrt{\\frac{g}{2 \\omega_{i-j-k}}} B^{(2)}_{j+k-i,i,j,k} + \\frac{1}{4 \\pi^2} A^{(2)}_{j-i,i,j} \\left[ \\sqrt{\\frac{\\omega_{j-i}}{\\omega_k}} |k_k| + \\sqrt{\\frac{\\omega_k}{\\omega_{j-i}}}|k_i - k_j| \\right] \\\\& - \\frac{1}{32 \\pi^3} \\sqrt{\\frac{2 \\omega_j \\omega_k}{g \\omega_i}} \\left[ |k_i| \\left( |k_i| - |k_i-k_j| - |k_i-k_k| \\right) \\right], \\\\ \\mathcal{C}^{(3)}_{i+j-k} &= - \\frac{1}{\\pi} \\sqrt{\\frac{g}{2 \\omega_{i+j-k}}} B^{(3)}_{k-i-j,i,j,k} - \\frac{1}{4 \\pi^2} \\left[ \\sqrt{\\frac{\\omega_k}{\\omega_{i+j}}}|k_i+k_j| \\left( A^{(1)}_{i+j,i,j} - A^{(3)}_{-i-j,i,j} \\right) \\right. \\\\& \\left. - \\sqrt{\\frac{\\omega_{i+j}}{\\omega_k}} |k_k| \\left( A^{(1)}_{i+j,i,j} - A^{(3)}_{-i-j,i,j} \\right) \\right] + \\frac{1}{32 \\pi^3} \\sqrt{\\frac{2 \\omega_j \\omega_k}{g \\omega_i}} \\left[ |k_i| \\left( |k_i| - |k_i+k_j| - |k_i-k_k| \\right) \\right], \\\\\\mathcal{C}^{(3)}_{i-j+k} &= - \\frac{1}{4 \\pi^2} A^{(2)}_{j-i,i,j} \\left[ \\sqrt{\\frac{\\omega_{j-i}}{\\omega_k}} |k_k| - \\sqrt{\\frac{\\omega_k}{\\omega_{j-i}}} |k_i - k_j| \\right] - \\frac{1}{32 \\pi^3} \\sqrt{\\frac{2 \\omega_j \\omega_k}{g \\omega_i}} \\left[ |k_i| \\left( |k_i| - |k_i-k_j| - |k_i+k_k| \\right) \\right]. \\end{align}", "formula_002442": "\\nabla \\phi", "formula_002443": "(ii)", "formula_002444": "z_0=-5", "formula_002445": "u_S = \\underbrace{\\sum_j a_j^2 \\omega_j k_j e^{2k_j z_0}}_{\\text{(I)}} + \\underbrace{\\sum_{k_i>k_j} \\frac{\\omega_i ^2 a_i^2 a_j^2 (k_i-k_j)^3}{\\omega_i-\\omega_j} e^{2(k_i-k_j)z_0}}_{\\text{(II)}}.", "formula_002446": "\\delta_{0+1-2-3}", "formula_002447": "z=1.25", "formula_002448": "\\omega_{i\\pm j \\pm k}.", "formula_002449": "k=1", "formula_002450": "\\mathbf{x}'(t) = \\mathbf{u}.", "formula_002451": "k_1>2,", "formula_002452": "k_1>k_2", "formula_002453": "-\\infty", "formula_002454": "k_2", "formula_002455": "\\epsilon=0.075", "formula_002456": "\\sigma=0.55.", "formula_002457": "k.", "formula_002458": "g", "formula_002460": "h\\rightarrow\\infty)", "formula_002459": "\\omega = \\sqrt{gq(k)}", "formula_002461": "\\begin{align}  \\zeta_3(x,t) = & \\frac{1}{2 \\pi} \\sum_{i,j,k} A_i A_j A_k \\left\\lbrace \\sqrt{\\frac{\\omega_{i+j+k}}{2g}} \\left( B^{(1)}_{i+j+k,i,j,k} + B^{(4)}_{-i-j-k,i,j,k} \\right) \\left[ e^{i(\\xi_i + \\xi_j + \\xi_k)}  \\right. \\right. \\\\  & \\left. + e^{-i(\\xi_i + \\xi_j + \\xi_k)}  \\right] + \\sqrt{\\frac{\\omega_{i-j-k}}{2g}} B^{(2)}_{j+k-i,i,j,k} \\left[ e^{i(\\xi_j+\\xi_k-\\xi_i)}  \\right.   \\left. \\left. + e^{i(\\xi_i -\\xi_j - \\xi_k)} \\right] \\right. \\\\& \\left. + \\sqrt{\\frac{\\omega_{i+j-k}}{2g}} B^{(3)}_{k-i-j,i,j,k} \\left[ e^{i(\\xi_k-\\xi_i-\\xi_j)}  + e^{i(\\xi_i+\\xi_j-\\xi_k)}  \\right] \\right\\rbrace.\\end{align}", "formula_002462": "\\hat{\\phi}(k,z,t) = e^{|k|z} \\left[ \\hat{\\psi} - \\frac{1}{2 \\pi} \\int \\hat{\\zeta}_1 \\hat{\\psi}_2 |k_2| \\delta_{0-1-2} dk_{12} - \\int D_{0123} \\hat{\\psi}_1 \\hat{\\zeta}_2 \\hat{\\zeta}_3 \\delta_{0-1-2-3} dk_{123} + \\ldots \\right]", "formula_002464": "\\hat{\\zeta}", "formula_002465": "b_i(t)", "formula_002463": "\\psi(x,t)=\\phi(x,\\zeta(x,t),t),", "formula_002466": "\\mathcal{C}^{(2)}", "formula_002467": "\\epsilon=ak", "formula_002468": "\\phi(\\zeta) = \\phi(0) + \\epsilon \\zeta \\phi_z(0) + \\frac{\\epsilon^2 \\zeta^2}{2} \\phi_zz(0) + \\ldots.", "formula_002469": "\\frac{\\partial \\hat{\\zeta}}{\\partial t} = \\frac{\\delta H}{\\delta \\hat{\\psi}^*}, \\quad \\frac{\\partial \\hat{\\psi}}{\\partial t} = - \\frac{\\delta H}{\\delta \\hat{\\zeta}^*},", "formula_002470": "z_0=-4, \\, k_1=1.2", "formula_002472": "\\Delta>0.", "formula_002471": "\\begin{align} x'(t) &= a \\omega e^{kz_0} \\cos(\\xi_0) - a^2 k \\omega e^{2kz_0} \\cos(\\omega t) + a^2 k \\omega e^{2kz_0},\\\\ z'(t) &= a \\omega e^{kz_0} \\sin(\\xi_0) + a^2 k \\omega e^{2kz_0} \\sin(\\omega t). \\end{align}", "formula_002473": "(x_0,z_0)=(0,-3).", "formula_002475": "\\exp(i(\\xi_i \\pm \\xi_j))", "formula_002476": "\\Delta,", "formula_002474": "A^{(i)}, \\, B^{(i)}", "formula_002477": "u_1", "formula_002478": "u(x,z,t) = \\underbrace{u(x_0,z_0,t)}_{(i)} + \\underbrace{(x-x_0) \\frac{\\partial u}{\\partial x}\\vert_{(x_0,z_0)} + (z-z_0) \\frac{\\partial u}{\\partial z}\\vert_{(x_0,z_0)}}_{(ii)} + \\ldots,", "formula_002479": "\\phi", "formula_002480": "\\begin{align} & \\zeta_1 = a \\cos(\\xi), \\quad \\phi_1 = \\frac{a\\omega}{k}  e^{kz} \\sin(\\xi),\\end{align}", "formula_002481": "x_0=0", "formula_002482": "\\begin{align} x'(t) = a \\omega e^{kz} \\cos(\\xi),\\\\ z'(t) = a \\omega e^{kz} \\sin(\\xi).\\end{align}", "formula_002484": "\\exp(k z_0) a \\omega/\\Omega.", "formula_002483": "z=-1.5", "formula_002485": "\\Delta X/T", "formula_002486": "w.", "formula_002487": "\\zeta,", "formula_002488": "\\mathbf{x}'(t) = \\nabla \\phi", "formula_002489": "\\frac{1}{\\pi} \\sqrt{\\frac{\\omega_i}{2g}} A_i = a_i.", "formula_002490": "z", "formula_002492": "(x_0,t_0)", "formula_002491": "\\epsilon_1 = \\epsilon_2 = 0.075", "formula_002493": "q(k) = |k| \\tanh(|k|h),", "formula_002494": "\\epsilon_1=\\epsilon_2=0.025, \\, 0.05, \\, 0.075", "formula_002496": "k_p=2.5", "formula_002495": "(x_0,z_0)", "formula_002498": "\\omega_2 = 1.25", "formula_002500": "z=0", "formula_002497": "a(k)", "formula_002499": "x \\in \\mathbb{R}.", "formula_002501": "t\\approx[-5,5]", "formula_002504": "\\xi_0 = kx_0 - \\omega t,", "formula_002502": "B_i = A_i e^{-i\\varphi_i t}", "formula_002503": "u_S =    \\underbrace{a_1^2 k_1 \\omega_1 e^{2 k_1 z_0} + a_2^2 k_2 \\omega_2 e^{2 k_2 z_0}}_{(\\text{I})} + \\underbrace{a_1^2 a_2^2 \\omega_1^2 \\frac{(k_1-k_2)^3}{\\omega_1-\\omega_2} e^{2(k_1-k_2)z_0}}_{(\\text{II})} ,", "formula_002505": "\\begin{align} \\phi &= \\frac{ag}{\\omega} \\left(1-\\frac{a^2 k^2}{4}\\right) e^{kz} \\sin(\\xi) \\\\ \\zeta &= a \\left( 1- \\frac{a^2 k^2}{8} \\right) \\cos (\\xi) + \\frac{a^2 k}{2} \\cos(2\\xi) + \\frac{3}{8} a^3 k^2 \\cos(3\\xi) \\end{align}", "formula_002506": "a:", "formula_002508": "z_0=-3", "formula_002507": "105", "formula_002509": "\\begin{align}\\phi(x,z,t) = \\frac{a_1 g}{\\omega_1} e^{k_1 z} \\sin(\\xi_1) + \\frac{a_2 g}{\\omega_2} e^{k_2 z} \\sin(\\xi_2). \\end{align}", "formula_002510": "\\omega({k})^2 = g | {k}|.", "formula_002511": "\\mathcal{C}^{(3)}", "formula_002512": "k_0 + k_1 = k_0 + k_1", "formula_002513": "z\\leq 0", "formula_002514": "-5", "formula_002515": "\\zeta", "formula_002516": "{u_S} = \\frac{a^2 \\omega^2 k e^{2kz_0}}{\\Omega} = \\frac{2 a^2 \\omega k e^{2kz_0}}{2+a^2 k^2}.", "formula_002517": "\\begin{align}\\phi(x,z,t) = \\frac{a_1 g}{\\omega_1} e^{k_1 z} \\sin(\\xi_1) + \\frac{a_2 g}{\\omega_2} e^{k_2 z} \\sin(\\xi_2) - \\omega_1 a_1 a_2 e^{(k_1-k_2)z} \\sin(\\xi_1 - \\xi_2),\\end{align}", "formula_002518": "(x,z)=(0,-5)", "formula_002520": "\\omega(k_i \\pm k_j) = \\sqrt{g |k_i \\pm k_j|}.", "formula_002519": "A_i \\in \\mathbb{R}", "formula_002522": "A=0.03", "formula_002523": "k_1=1.2", "formula_002525": "\\epsilon.", "formula_002521": "\\epsilon^2,", "formula_002524": "(x(t),z(t))", "formula_002526": "(0,0)", "formula_002528": "(x_0,t_0)=(0,0)", "formula_002527": "H  =  \\int \\int_{-h}^\\zeta \\frac{1}{2} |\\nabla \\phi|^2 dz + \\frac{1}{2}g \\zeta^2 dx,", "formula_002529": "a", "formula_002530": "u_S T_E", "formula_002532": "\\psi", "formula_002531": "\\begin{align}  \\zeta_2(x,t) = & \\frac{1}{2\\pi} \\sum_{i,j} A_i A_j \\left\\lbrace \\sqrt{\\frac{\\omega_{i+j}}{2g}} \\left( A^{(1)}_{i+j,i,j} + A^{(3)}_{-i-j,i,j} \\right) \\left[ e^{i(\\xi_i + \\xi_j)}  + e^{-i(\\xi_i + \\xi_j)}  \\right]  \\right. \\\\ & \\left. + \\sqrt{\\frac{\\omega_{i-j}}{2g}} A^{(2)}_{j-i,i,j} \\left[  e^{i(\\xi_j-\\xi_i)} + e^{i(\\xi_i-\\xi_j)} \\right] \\right\\rbrace. \\end{align}", "formula_002533": "a=0.15", "formula_002535": "z_0=0", "formula_002534": "T_E = 2 \\pi/\\omega", "formula_002536": "\\exp(k_2 z)", "formula_002537": "b_i(t) = |b_i(0)| \\exp\\left( -it \\left[ \\tilde{V}^{(2)}_{iiii} |b_i(0)|^2 + 2 \\sum_{j\\neq i} \\tilde{V}^{(2)}_{ijij} |b_j(0)|^2 \\right] \\right),", "formula_002538": "\\varphi_i(t),", "formula_002540": "(x_0,z_0)=(0,0)", "formula_002539": "B_i=B(k_i,t)", "formula_002541": "T_E=2\\pi/\\omega.", "formula_002542": "k_0 + k_0 = k_0 + k_0", "formula_002543": "k_1>k_2,", "formula_002546": "g=9.81", "formula_002548": "x_0=0, \\, 2\\pi/3", "formula_002545": "\\omega_{i\\pm j}", "formula_002549": "k_1=6", "formula_002547": "u_S=  a_1^2 k_1 \\omega_1 e^{2 k_1 z_0} + a_2^2 k_2 \\omega_2 e^{2 k_2 z_0} ,", "formula_002552": "z\\rightarrow-\\infty", "formula_002551": "\\Omega_i = \\omega_i + \\tilde{V}^{(2)}_{iiii} |A_i|^2 + 2 \\sum_{j\\neq i} \\tilde{V}^{(2)}_{ijij} |A_j|^2,", "formula_002550": "\\epsilon,", "formula_002553": "\\omega(k_i \\pm k_j) = \\sqrt{g |k_i \\pm k_j|}", "formula_002554": "\\begin{align} \\Omega_1 &= \\omega_1 \\left[ 1+ \\frac{1}{2} \\epsilon_1^2 + \\epsilon_2^2 \\left( \\frac{k_1}{k_2} \\right)^{1/2} \\right],\\\\ \\Omega_2 &= \\omega_2 \\left[ 1+ \\frac{1}{2} \\epsilon_2^2 + \\epsilon_1^2 \\left( \\frac{k_2}{k_1} \\right)^{3/2} \\right],\\end{align}", "formula_002555": "\\begin{align}  \\phi &= \\frac{ag}{\\omega} e^{kz} \\sin(\\xi) \\\\ \\zeta &= a \\left( 1+ \\frac{a^2 k^2}{8} \\right) \\cos (\\xi) + \\frac{a^2 k}{2} \\cos(2\\xi) + \\frac{3}{8} a^3 k^2 \\cos(3\\xi) \\end{align}", "formula_002557": "z=0.", "formula_002556": "ia^*(k)", "formula_002558": "k_i", "formula_002559": "\\begin{align*} \\phi_2(x,z,t) & =  \\sum_{i,j} A_i A_j  \\left[ \\mathcal{C}^{(2)}_{i+j} \\sin(\\xi_i + \\xi_j) e^{|k_i+k_j|z} + \\mathcal{C}^{(2)}_{i-j} \\sin(\\xi_i - \\xi_j) e^{|k_i-k_j|z} \\right] \\end{align*}", "formula_002560": "x", "formula_002561": "\\nabla_x", "formula_002562": "\\Omega_i \\pm \\Omega_j,", "formula_002564": "\\hat{\\phi}(k),", "formula_002563": "k_1", "formula_002565": "k_1=k_2+\\Delta", "formula_002566": "b", "formula_002567": "k_2=1,", "formula_002568": "\\epsilon \\approx 0.1", "formula_002569": "\\zeta(x,t)", "formula_002572": "\\tilde{V}^{(2)}_{iiii} = \\frac{1}{4 \\pi^2} k_i^3 \\text{ and } \\tilde{V}^{(2)}_{ijij} = \\frac{1}{4 \\pi^2} k_i k_j \\min(k_i,k_j).", "formula_002570": "k_i \\pm k_j", "formula_002571": "T\\approx 10", "formula_002575": "\\epsilon", "formula_002576": "\\zeta \\rightarrow \\epsilon \\zeta", "formula_002573": "\\begin{align}\\mathcal{C}^{(2)}_{i+j} &= \\frac{1}{\\pi} \\sqrt{\\frac{g}{2 \\omega_{i+j}}} \\left( A^{(1)}_{i+j,i,j} - A^{(3)}_{-i-j,i,j} \\right) - \\frac{1}{4 \\pi^2} \\sqrt{\\frac{\\omega_i}{\\omega_j}}|k_j|,\\\\\\mathcal{C}^{(2)}_{i-j} &= -\\frac{1}{\\pi} \\sqrt{\\frac{g}{2 \\omega_{i-j}}} A^{(2)}_{j-i,i,j} + \\frac{1}{4 \\pi^2} \\sqrt{\\frac{\\omega_i}{\\omega_j}}|k_j|.\\end{align}", "formula_002574": "\\epsilon=0.15", "formula_002577": "10", "formula_002578": "\\frac{\\partial \\zeta}{\\partial t} = \\frac{\\delta H}{\\delta \\psi}, \\quad \\frac{\\partial \\psi}{\\partial t}=-\\frac{\\delta H}{\\delta \\zeta}.", "formula_002579": "a_i = A \\exp\\left( -\\frac{(k_i-k_p)^2}{2 \\sigma^2}\\right).", "formula_002580": "\\begin{align}&\\Delta \\phi = 0, \\\\&\\phi = \\psi \\text{ on } z = \\zeta, \\\\&\\phi_z \\rightarrow 0 \\text{ as } z \\rightarrow -\\infty,\\end{align}", "formula_002581": "\\exp((k_1-k_2)z)", "formula_002582": "k_{10}=4", "formula_002583": "4\\pi/3.", "formula_002584": "z_0<0", "formula_002585": "\\exp(k_1 z)", "formula_002587": "\\hat{\\zeta}(k)", "formula_002586": "\\Omega > \\omega", "formula_002588": "(x,z)", "formula_002590": "\\xi=kx-\\omega t", "formula_002589": "\\phi = \\sum_j \\frac{a_j g}{\\omega_j} e^{|k_j|z} \\sin ( k_j x - \\omega_j t) - \\sum_{i>j} \\omega_{i} a_i a_j \\sin(\\xi_{i} - \\xi_{j} ) e^{|k_i - k_j| z}.", "formula_002591": "t", "formula_002592": "b(k) = \\sum_i B_i \\delta(k-k_i).", "formula_002593": "z_0=0, \\, k_1 = 6", "formula_002596": "\\begin{align} \\zeta(x) = \\frac{1}{2\\pi} \\int \\left( \\frac{q(k)}{2 \\omega(k)}\\right)^{1/2} \\left[ a(k) + a^*(-k)\\right] e^{ikx} dk, \\\\ \\psi(x) = \\frac{-i}{2\\pi} \\int \\left( \\frac{\\omega(k)}{2 q(k)}\\right)^{1/2} \\left[ a(k) - a^*(-k)\\right] e^{ikx} dk, \\end{align}", "formula_002594": "\\Omega = \\omega(k) \\left(1 + \\frac{a^2 k^2}{2} \\right).", "formula_002595": "(x,z)=(0,0).", "formula_002598": "*", "formula_002597": "\\xi = kx - \\Omega t", "formula_002600": "T_E", "formula_002599": "^2", "formula_002601": "z_0", "formula_002602": "\\epsilon = 0.2", "formula_002604": "x_0=0, \\, 55", "formula_002603": "k_1.", "formula_002606": "\\mathbf{x}'=\\mathbf{u}", "formula_002605": "\\begin{align} &\\Delta \\phi = 0 \\text{ on } -h < z < \\zeta, \\\\ &\\zeta_t + \\nabla_x \\phi \\cdot \\nabla_x \\zeta = \\phi_z \\text{ on } z = \\zeta, \\\\ &\\phi_t + \\frac{1}{2}(\\nabla \\phi)^2 + g\\zeta = 0 \\text{ on } z= \\zeta, \\\\ &\\phi_z = 0 \\text{ on } z= -h.\\end{align}", "formula_002607": "\\phi_3  = -\\frac{a^3 \\omega k}{4} e^{kz} \\sin \\xi", "formula_002608": "\\sin(\\xi)", "formula_002609": "\\begin{align}x'(t) = a_1 \\omega_1 e^{k_1z_0} \\cos(k_1x_0-\\omega_1 t) + a_2 \\omega_2 e^{k_2 z_0} \\cos(k_2 x_0-\\omega_2 t) ,\\\\z'(t) = a_1 \\omega_1 e^{k_1 z_0} \\sin(k_1 x_0 - \\omega_1 t) +  a_2 \\omega_2 e^{k_2 z_0} \\sin(k_2 x_0 - \\omega_2 t),\\end{align}", "formula_002610": "t=-8", "formula_002611": "k_p", "formula_002612": "\\mathbf{u}", "formula_002613": "\\begin{align}x'(t) = a \\omega e^{kz_0} \\cos(\\xi_0),\\\\z'(t) = a \\omega e^{kz_0} \\sin(\\xi_0),\\end{align}", "formula_002614": "x=1.5", "formula_002615": "\\delta(k_0 + k_1 - k_2 - k_3).", "formula_002617": "\\epsilon_1=\\epsilon_2=0.05", "formula_002620": "u_2", "formula_002618": "(t,x)", "formula_002619": "i \\frac{\\partial b_0}{\\partial t} = \\omega_0 b_0 + \\int \\tilde{V}^{(2)}_{0123} b_1^* b_2 b_3 \\delta_{0+1-2-3} dk_{123},", "formula_002616": "\\phi_1(x,z,t) = \\frac{1}{2\\pi} \\int e^{|k|z} e^{ikx} \\hat{\\psi}_1 dk =  \\frac{1}{\\pi} \\sum_i A_i e^{|k_i|z} \\sqrt{\\frac{g}{2\\omega_i}} \\sin(\\xi_i).", "formula_002623": "k_1=1", "formula_002622": "\\omega_2=1.25", "formula_002621": "z_0=-0.5", "formula_002625": "b_0=b(k_0,t),", "formula_002626": "\\phi(x,z) = \\frac{1}{2\\pi} \\int \\phi(k) e^{kz} e^{ikx} dk, \\quad \\phi(k) = \\phi^*(-k),", "formula_002627": "\\tilde{V}^{(2)}", "formula_002628": "x=\\pi", "formula_002631": "k_2=1", "formula_002629": "a^*(k)", "formula_002632": "k_1 \\sim k_2,", "formula_002630": "\\begin{align}  a_0 = b_0 &+ \\int A^{(1)}_{012} b_1 b_2 \\delta_{0-1-2} dk_{12} + \\int A^{(2)}_{012} b_1^* b_2 \\delta_{0+1-2} dk_{12} + \\int A^{(3)}_{012} b_1^* b_2^* \\delta_{0+1+2} dk_{12} \\\\  &+ \\int B^{(1)}_{0123} b_1 b_2 b_3 \\delta_{0-1-2-3} dk_{123} + \\int B^{(2)}_{0123} b_1^* b_2 b_3 \\delta_{0+1-2-3} dk_{123} \\\\  &+ \\int B^{(3)}_{0123} b_1^* b_2^* b_3 \\delta_{0+1+2-3} dk_{123} + \\int B^{(4)}_{0123} b_1^* b_2^* b_3^* \\delta_{0+1+2+3} dk_{123} + \\ldots\\end{align}", "formula_002635": "h", "formula_002633": "(x_0,z_0)=(0,0),", "formula_002634": "x,y", "formula_002636": "x=0", "formula_002637": "k_2 = 1", "formula_002638": "u_S = a^2 k \\omega e^{2kz_0}.", "formula_002639": "z_0=-4", "formula_002641": "T_L", "formula_002644": "\\hat{\\psi}", "formula_002643": "z_0=-3.5", "formula_002640": "\\omega_1=1", "formula_002642": "8 \\pi", "formula_002646": "k_1-k_2", "formula_002647": "\\xi_i = k_i x - \\Omega_i t + \\theta_i.", "formula_002645": "\\zeta_1 = \\sum_i a_i \\cos(k_i(x-x_0) - \\omega_i(t-t_0))", "formula_002648": "z_0=-5, \\, k_1 = 1.2", "formula_002649": "\\epsilon_1=\\epsilon_2=0.15,", "formula_002651": "a_i.", "formula_002650": "\\zeta_1 = \\frac{1}{2 \\pi} \\sum_i \\sqrt{\\frac{\\omega_i}{2g}} A_i \\left( e^{i \\xi_i} +  e^{-i \\xi_i} \\right).", "formula_002653": "k_i.", "formula_002654": "k_1 > k_2,", "formula_002658": "P = Q", "formula_002656": "\\epsilon^{-2} T", "formula_002652": "\\omega^2 = g|k|", "formula_002657": "P(M - N) + Q(N - R) \\neq PQ", "formula_002655": "\\begin{align} \\mathbf{x}'(t) = \\nabla \\phi(x,z,t).\\end{align}", "formula_002659": "\\dfrac{R_1}{(1+r)^2}-C_1+F_1", "formula_002661": "[x_0(1-x_0)(Q-P)](N-R-x_0P)", "formula_002662": "N=R", "formula_002660": "N=M", "formula_002664": "N<R", "formula_002665": "M - R < \\min\\{Q,P\\} \\cap R > N", "formula_002663": "N = 14", "formula_002667": "\\lambda_1 > 0, \\lambda_2 < 0", "formula_002666": "R", "formula_002670": "None", "formula_002668": "P< M-R < Q, M > N>R", "formula_002669": "x_0(1-x_0)(Q-P)+N-R-x_0P", "formula_002672": "M>N>R", "formula_002671": "Q>P", "formula_002673": "(0, 1, 0)", "formula_002675": "R_1>R_2>R", "formula_002676": "tr(J)=0", "formula_002678": "P < M - R < Q \\land M > N > R \\land P(M - N) + Q(N - R) < PQ", "formula_002677": "N<M", "formula_002680": "\\dfrac{R_2}{1+r}-C_2", "formula_002682": "F_2 = 8", "formula_002679": "\\lambda_1 < 0, \\lambda_2=N-R-x_1P>0", "formula_002681": "1", "formula_002683": "W_1 = 0", "formula_002684": "r\\in(0.333, 1)", "formula_002686": "M = \\dfrac{R_1}{(1+r)^2}-C_1", "formula_002685": "P<M-R<Q", "formula_002687": "f", "formula_002691": "x, y, z", "formula_002690": "0<Q(N-R)+P(M-N)<PQ", "formula_002688": "g", "formula_002689": "U", "formula_002692": "J<0", "formula_002693": "\\lambda_1>0", "formula_002694": "P< M-R < Q, M > N", "formula_002695": "\\begin{align*}f_{x}=&x\\cdot (M+F_1) +y\\cdot M + z\\cdot (M-F_2-Q)\\\\=&M+xF_1-zF_2-zQ.\\end{align*}", "formula_002697": "\\dfrac{R_1}{(1+r)^2}-C_1", "formula_002698": "N > R", "formula_002699": "M - R > Q, M - R > P", "formula_002700": "R > N", "formula_002701": "N", "formula_002704": "(N)", "formula_002703": "e", "formula_002705": "4", "formula_002702": "A=B=0", "formula_002706": "(x, y, z) = (1, 0, 0)", "formula_002707": "km^2", "formula_002708": "\\lambda_1=x_1(1-x_1)(Q-P)>0", "formula_002709": "Q = 10", "formula_002713": "r", "formula_002711": "Q < M-R < P, M > N > R", "formula_002712": "P< M-R < Q, M < N", "formula_002714": "(P < M - R < Q \\land M < N \\land N > R)", "formula_002715": "(0,0,1)", "formula_002716": "W_1=A\\alpha-B\\beta", "formula_002717": "M-R-P>0", "formula_002718": "O", "formula_002719": "P = 10", "formula_002720": "(x, y, z) = (x^*, y^*, z^*)", "formula_002721": "PC", "formula_002722": "\\Delta=P^2x^*y^*\\sqrt{1-x^*-y^*}", "formula_002723": "M-R < Q, M-R < P", "formula_002724": "y^*=1-x^*-z^*", "formula_002728": "\\begin{cases}        \\dfrac{d\\xi}{d t}=\\sqrt{\\Delta}\\eta+\\dfrac{\\xi^2}{1-y^*}+\\dfrac{P(1-2x^*-y^*)}{(1-y^*)\\sqrt{\\Delta}}\\xi\\eta -\\dfrac{\\eta^2}{1-y^*}\\\\        \\dfrac{d\\eta}{d t}=-\\sqrt{\\Delta}\\xi-\\dfrac{\\xi\\eta}{y^*}.    \\end{cases}", "formula_002726": "|J|>0", "formula_002725": "r = 0.05", "formula_002727": "J", "formula_002729": "Q", "formula_002730": "P = 8", "formula_002731": "z=0", "formula_002732": "\\psi(x)=N-R-xP", "formula_002733": "r\\in (0.162, 0.333),", "formula_002736": "R = 10, C_2 = 5, F_1 = 5, F_2 = 8, Q = 8", "formula_002735": "F_2", "formula_002734": "C", "formula_002737": "N>R", "formula_002738": "R=N", "formula_002739": "(x^*, y^*, z^*)", "formula_002740": "\\varphi(y)=M-R-zP-xP=M-R-(1-y)P", "formula_002741": "M-2R-Q+N", "formula_002742": "R=10, R_2=2R=20, R_1=3R=30, C_2=5, C_1=2C_2=10", "formula_002743": "z_0=1-\\frac{P-M+R}{P-Q}", "formula_002744": "(0,1,0)", "formula_002745": "x^*=\\frac{N-R}{P},z^*=\\frac{M-N}{Q}", "formula_002746": "C_1 > C_2", "formula_002747": "R = 10, C_2 = 5, F_1 = 5, F_2 = 8, Q = 6", "formula_002749": "M", "formula_002750": "|J|>0, tr(J)=0", "formula_002748": "x^*=\\frac{N-R}{P}", "formula_002751": "y=1-x-z,", "formula_002752": "9.6", "formula_002753": "R\\neq N", "formula_002755": "\\lambda_2> 0", "formula_002754": "\\phi=x f_x+y f_y+z f_z", "formula_002756": "M-R > Q, M-R > P", "formula_002758": "r\\in(0, 0.162)", "formula_002757": "x_0=\\frac{M-R-Q}{P-Q}", "formula_002759": "R+F_1+P", "formula_002760": "y=0", "formula_002762": "\\lambda_2 > 0", "formula_002761": "J(0,1,0)=\\begin{pmatrix}    M-N&0\\\\    *&R-N\\end{pmatrix}.", "formula_002763": "a", "formula_002765": "\\lambda_1= -(M-R-P)<0, \\lambda_2=N-M<0", "formula_002764": "(1,0,0)", "formula_002768": "\\dfrac{1}{1+r}", "formula_002767": "J=0", "formula_002769": "P < Q", "formula_002770": "P-", "formula_002771": "C, P, N", "formula_002772": "N = 13", "formula_002773": "-(M-R-P)(N-M)", "formula_002775": "(M-R-Q)(N-R)", "formula_002774": "3", "formula_002776": "j=x,y,z.", "formula_002777": "d", "formula_002779": "N>M>R", "formula_002778": "Q = 5, P=8, <PERSON>=17", "formula_002782": "R = 10, C_2 = 5, F_1 = 5,", "formula_002781": "Q = 10, P=4, <PERSON>=15", "formula_002783": "W_1=0", "formula_002784": "Q < M-R < P", "formula_002785": "S_3 = \\{(x, y, z): x, y, z \\geq 0, x + y + z = 1\\}", "formula_002786": "P < M-R < Q", "formula_002787": "R_1", "formula_002789": "Q<M-R<P", "formula_002788": "M > N", "formula_002791": "M<N", "formula_002790": "A=\\frac{1}{1-y^*}-\\frac{1}{1-y^*}=0", "formula_002792": "R-F_2", "formula_002793": "r = 0.5", "formula_002794": "Q < M - R < P \\land M > N > R \\land P(M - N) + Q(N - R) < PQ", "formula_002795": "Q < M - R < P \\land M > R > N", "formula_002796": "(x_0,0,z_0)", "formula_002797": "(C)", "formula_002798": "N>M> R", "formula_002799": "\\lambda_1= M-R-Q > 0", "formula_002800": "\\lambda_1< 0,\\lambda_2=R-N<0", "formula_002802": "r = 0.3", "formula_002801": "P(M-N)+Q(N-R)<PQ", "formula_002805": "(x, y, z) = (x_0, 0, 1-x_0)", "formula_002804": "J(1, 0, 0) = \\begin{pmatrix}-(M-R-P) & * \\\\0 & N-M\\end{pmatrix}", "formula_002807": "f_x = f_y = f_z", "formula_002803": "f_y = f_z", "formula_002806": "N\\neq M", "formula_002808": "PN", "formula_002809": "(x, y, z) = (0, 1,0)", "formula_002810": "(x_0, 0, 1-x_0)", "formula_002811": "C_2", "formula_002812": "C_1", "formula_002813": "0", "formula_002814": "R_1 > R_2 > R, C_1 > C_2, R_1>C_1, R_2> C_2", "formula_002815": "Q=P", "formula_002816": "B=0", "formula_002817": "b", "formula_002820": "M < N", "formula_002818": "Q< M-R < P, M>N>R", "formula_002821": "(1, 0, 0)", "formula_002819": "\\begin{aligned}J_{11}=\\frac{\\partial \\dot{x}}{\\partial x}=&[(1-x)\\varphi(x, y)-y\\psi(x)]+ x\\left[-\\varphi(x, y)+(1-x)\\varphi_x'(x, y)-y\\psi'(x))\\right], \\\\J_{12}=\\frac{\\partial \\dot{x}}{\\partial y}=& x\\left[-\\psi(x)+(1-x)\\varphi_y'(x, y)\\right],\\\\J_{21}=\\frac{\\partial \\dot{y}}{\\partial x}=& y\\left[-\\varphi(x, y)+(1-y)\\psi'(x)-x\\varphi_x'(x, y)\\right],\\\\J_{22}=\\frac{\\partial \\dot{y}}{\\partial y}=&[(1-y)\\psi(x)-x\\varphi(x, y)]+ y\\left[-\\psi(x)-x\\varphi_y'(x, y))\\right]. \\\\\\end{aligned}", "formula_002822": "A", "formula_002823": "X=x-x^*,Y=x-x^*", "formula_002824": "x^*y^*(1-x^*-y^*)PQ", "formula_002825": "x + y + z = 1", "formula_002826": "N = 18", "formula_002828": "-(M-R-P)+ N-M", "formula_002827": "P(M-N) + Q(N-R) > PQ", "formula_002829": "\\dfrac{R_1}{(1+r)^2}-C_1-F_2-Q", "formula_002831": "P=Q", "formula_002832": "\\lambda_1 < 0, \\lambda_2=N-R<0", "formula_002833": "\\lambda_1 < 0, \\lambda_2<0", "formula_002835": "\\lambda_1 < 0, \\lambda_2>0", "formula_002834": "\\lambda_1 > 0", "formula_002836": "N>M", "formula_002837": "(x^*, y^*,z^*)", "formula_002838": "M = 17", "formula_002839": "M-2N+R", "formula_002840": "(P)", "formula_002841": "N = 8", "formula_002843": "14.3", "formula_002844": "\\mathrm{tr}(J) > 0", "formula_002842": "Q < M-R < P, M > R > N", "formula_002845": "Q<P", "formula_002846": "M - R < \\min\\{Q,P\\} \\land M > N > R", "formula_002847": "\\lambda_1 <0, \\lambda_2 <0", "formula_002848": "M-R<Q", "formula_002849": "\\begin{align*}f_{z}=&x\\cdot (R+F_1+P) +y\\cdot R + z\\cdot (R-F_2)\\\\=&R+xF_1-zF_2+xP.\\end{align*}", "formula_002850": "\\lambda_1 < 0, \\lambda_2 < 0", "formula_002851": "M - R", "formula_002855": "P<Q", "formula_002854": "Q(N-R)+P(M-N)>PQ", "formula_002852": "\\dfrac{R_2}{1+r}-C_2-F_2", "formula_002853": "P < M - R < Q \\land M > N \\land N < R", "formula_002856": "f_x-f_z=M-R-z Q-x P=M-R-(1-x-y) Q-x P\\triangleq \\varphi(x,y)", "formula_002857": "A=a_{20}+a_{02}, B=b_{20}+b_{02}", "formula_002858": "x^*(1-x^*-y^*)(Q-P)", "formula_002859": "P(M-N) + Q(N-R) < PQ", "formula_002860": "\\dfrac{1}{(1+r)^2}", "formula_002861": "J=\\begin{pmatrix}    J_{11}&J_{12}\\\\    J_{21}&J_{22}\\end{pmatrix}.", "formula_002863": "|J| > 0", "formula_002862": "f_x = f_z", "formula_002864": "5", "formula_002867": "Q = 8", "formula_002866": "C-", "formula_002865": "N = \\dfrac{R_2}{1+r}-C_2", "formula_002868": "J(0,0,1)=\\begin{pmatrix}    M-R-Q&0\\\\    0&N-R\\end{pmatrix}.", "formula_002869": "(x^*,y^*,z^*)", "formula_002870": "P>Q", "formula_002871": "R = 10, C_2 = 5, F_1 = 5, F_2 = 8", "formula_002872": "2", "formula_002873": "(Q < M - R < P \\land N > M > R)", "formula_002876": "F_1", "formula_002874": "P", "formula_002877": "M > N > R", "formula_002878": "z^*=\\frac{M-N}{Q}", "formula_002875": "Q< M-R < P", "formula_002879": "P(M-N)+Q(N-R)>PQ", "formula_002880": "\\phi=x f_x+y f_y+(1-x-y) f_z", "formula_002882": "R < N", "formula_002883": "R_1, R_2, R, r, C_1, C_2, F_1, F_2, P, Q>0", "formula_002884": "M - R < Q, M - R < P", "formula_002885": "S_3", "formula_002886": "f_x = f_y", "formula_002887": "(x, y, z) = (0,0,1)", "formula_002888": "\\dfrac{R_2}{1+r}-C_2+F_1", "formula_002890": "6.4\\%", "formula_002889": "M=\\dfrac{3R}{(1+r)^2}-2C_2=\\dfrac{30}{(1+r)^2}-10, N=\\dfrac{2R}{1+r}-C_2=\\dfrac{20}{1+r}-5, R=10", "formula_002891": "\\lambda_1< 0, \\lambda_2<0", "formula_002892": "M>R>N", "formula_002893": "(0, 0, 1)", "formula_002894": "\\begin{align*}f_{y}=&x\\cdot (N+F_1) +y\\cdot N + z\\cdot (N-F_2)\\\\=&N+xF_1-zF_2.\\end{align*}", "formula_002895": "N-", "formula_002896": "CN", "formula_002897": "\\lambda_1> 0", "formula_002898": "h", "formula_002899": "J(x_0, 0, 1-x_0)=\\begin{pmatrix}    x_0(1-x_0)(Q-P)&0\\\\    *&N-R-x_0P\\end{pmatrix}.", "formula_002901": "\\lambda_1= M-N > 0", "formula_002900": "Q<M-R<P, M>N>R,", "formula_002902": "x=0", "formula_002903": "P< M-R < Q, M > N > R", "formula_002904": "\\lambda_1 < 0, \\lambda_2 > 0", "formula_002905": "M - R > \\max\\{Q,P\\} \\land M > N", "formula_002906": "Q = 4, P=10, <PERSON>=15", "formula_002908": "P > Q", "formula_002907": "J>0", "formula_002909": "N < R", "formula_002910": "\\mathrm{tr}(J) < 0", "formula_002911": "J(x^*, y^*, z^*)=\\begin{pmatrix}    x^*(1-x^*)(Q-P)+x^*y^*P & x^*(1-x^*)Q\\\\    -y^*(1-y^*)P-x^*y^*(Q-P)&-x^*y^*Q\\end{pmatrix}.", "formula_002912": "B", "formula_002913": "M = \\dfrac{R_1}{(1+r)^2}- C_1, N =\\dfrac{R_2}{1+r} - C_2", "formula_002915": "c", "formula_002914": "(M-N)(R-N)", "formula_002916": "P < M - R < Q \\land M > N > R \\land P(M - N) + Q(N - R) > PQ", "formula_002917": "0 < r < 1", "formula_002918": "f_j", "formula_002919": "f_y-f_z=N-R-x P\\triangleq \\psi(x).", "formula_002920": "R_2", "formula_002921": "P = 5", "formula_002922": "\\sigma_{\\rm \\hspace{1pt} E} = \\frac{\\Gamma}{R(t)}.", "formula_002925": "f \\sim 1", "formula_002923": "\\mathrm {I\\hspace{-0.6pt}I\\hspace{-0.6pt}I}", "formula_002924": "\\lambda_\\mathrm{E}", "formula_002926": "R", "formula_002927": "c_\\mathrm{S}", "formula_002928": "C=-3.51 \\times 10^{-7}", "formula_002929": "l", "formula_002930": "t - t_{\\mathrm{\\hspace{1pt} EC}} < 0", "formula_002931": "\\times 10^{7}", "formula_002932": "C=-3.49 \\times 10^{-7}", "formula_002934": "c^*_\\mathrm{S}", "formula_002933": "R/R_{0}", "formula_002935": "\\sigma_{\\rm \\hspace{1pt} E,EC}", "formula_002936": "c_\\mathrm{D}=0.0100", "formula_002937": "0.15<c_\\mathrm{S}<0.4", "formula_002939": "0.01<c_\\mathrm{D}<0.1", "formula_002938": "n_{\\parallel}", "formula_002940": "{\\delta}", "formula_002941": "\\lambda", "formula_002942": "\\mathrm {V\\hspace{-0.6pt}I\\hspace{-0.6pt}I\\hspace{-0.6pt}I}", "formula_002943": "\\mathrm {V\\hspace{-0.6pt}I\\hspace{-0.6pt}I}", "formula_002944": "I_4", "formula_002945": "\\sigma_{\\rm \\hspace{1pt} E}/\\sigma_{\\rm \\hspace{1pt} E,EC} = \\delta n/\\delta n_\\mathrm{\\hspace{1pt} EC}", "formula_002947": "x, y, z", "formula_002949": "-1.93 \\pm 0.11", "formula_002946": "c_\\mathrm{D}=0.0300", "formula_002948": "c_\\mathrm{S}/c_\\mathrm{D} = 7.7", "formula_002950": "c_\\mathrm{S}-c_\\mathrm{D}", "formula_002951": "c_\\mathrm{S}=0.23", "formula_002952": "c^*_\\mathrm{S} \\sim 0.0670", "formula_002953": "\\sigma_{\\rm \\hspace{1pt} E}/\\sigma_{\\rm \\hspace{1pt} E,EC}", "formula_002954": "I_3", "formula_002956": "c_\\mathrm{S}/c_\\mathrm{D}=7.7", "formula_002955": "\\begin{align}{\\delta} &= \\frac{\\lambda}{2 \\pi} \\sin ^{ -1 }{ \\frac { \\sqrt { { ( { I }_{ 3 }-{ I }_{ 1 } ) }^{ 2 }+{ ( { I }_{ 2 }{-I }_{ 4 } ) }^{ 2 } } }{ ({ I }_{ 1 }+{ I }_{ 2 }+{ I }_{ 3 }+{ I }_{ 4 } )/2} },\\\\[6pt]\\varphi &=\\frac{1}{2}\\tan ^{ -1 }{ \\frac { { I }_{ 3 }-{ I }_{ 1 } }{{ I }_{ 2 }-{ I }_{ 4 }}},\\end{align}", "formula_002958": "\\sigma_{\\rm \\hspace{1pt} E}", "formula_002957": "z", "formula_002959": "1.5 \\times 10^6", "formula_002960": "^\\circ", "formula_002962": "t_{\\mathrm{FE}}", "formula_002963": "t - t_\\mathrm{\\hspace{1pt} EC} = 0", "formula_002961": "\\delta n/\\delta n_\\mathrm{\\hspace{1pt} EC}", "formula_002964": "t-t_\\mathrm{\\hspace{1pt} EC}", "formula_002966": "\\bm{\\sigma}", "formula_002965": "t_{\\mathrm{\\hspace{1pt} EC}}", "formula_002967": "C=\\delta n / \\sigma_{\\rm \\hspace{1pt} E}.", "formula_002968": "c_\\mathrm{S} / c_\\mathrm{D} = 7.7", "formula_002969": "\\mathrm {I\\hspace{-0.6pt}I}", "formula_002970": "C", "formula_002972": "\\sigma_{\\parallel} = 0", "formula_002973": "-3.51 \\pm 0.44", "formula_002971": "c_\\mathrm{S}/c_\\mathrm{D}", "formula_002974": "\\delta_\\mathrm{\\hspace{1pt} EC}", "formula_002975": "R_0", "formula_002976": "C=-3.1 \\times 10^{-7}", "formula_002977": "c_\\mathrm{D}=0.0050", "formula_002978": "0.0050 \\leq c_\\mathrm{D} \\leq 0.0300", "formula_002979": "\\bm{n} \\neq C \\bm{\\sigma}", "formula_002980": "l=2R", "formula_002981": "t - t_{\\mathrm{\\hspace{1pt} EC}} \\geq t_{\\mathrm{\\hspace{1pt} FE}} - t_{\\mathrm{\\hspace{1pt} EC}}", "formula_002982": "-1.41 \\pm 0.05", "formula_002983": "n_{y}", "formula_002984": "\\Gamma", "formula_002986": "\\dot{\\varepsilon}", "formula_002988": "\\delta n", "formula_002987": "\\mathrm I", "formula_002985": "c_\\mathrm{D}=0.03", "formula_002989": "\\sigma_{\\parallel}", "formula_002990": "n_{x}", "formula_002991": "\\delta _\\mathrm{\\hspace{1pt} EC}=3.9", "formula_002992": "\\delta n^{0}", "formula_002993": "\\delta n = \\delta/2R", "formula_002994": "\\Gamma = 36", "formula_002996": "\\bm{n}", "formula_002995": "c^*_\\mathrm{S} \\sim c_\\mathrm{S} - c_\\mathrm{D}", "formula_002997": "c_\\mathrm{D} = 0.0100", "formula_002998": "t - t_\\mathrm{\\hspace{1pt} EC}", "formula_002999": "G", "formula_003000": "x", "formula_003001": "c_\\mathrm{S}-c_\\mathrm{D}=0.2", "formula_003002": "0", "formula_003004": "t - t_\\mathrm{\\hspace{1pt} EC} < 0", "formula_003003": "x', y', z'", "formula_003005": "-1.44 \\pm 0.06", "formula_003006": "c_\\mathrm{S}=0.2300", "formula_003007": "\\sigma_{\\perp} = \\sigma_{\\rm \\hspace{1pt} E}", "formula_003008": "\\pi/2", "formula_003011": "t - t_{\\mathrm{\\hspace{1pt} EC}}", "formula_003009": "-2.71 \\pm 0.21", "formula_003010": "\\dot{\\gamma} >", "formula_003012": "-0.90 \\pm 0.05", "formula_003014": "|C|", "formula_003015": "\\delta n / \\sigma_{\\rm \\hspace{1pt} E}", "formula_003013": "y", "formula_003016": "t_{\\mathrm{FE}}-t_{\\mathrm{EC}}", "formula_003018": "-1.78 \\pm 0.14", "formula_003019": "0<\\dot{\\varepsilon}<30", "formula_003020": "\\delta = \\int \\delta n\\,dz = Cl(\\sigma_{\\parallel}-\\sigma_{\\perp}),", "formula_003021": "\\sigma_{\\parallel}-\\sigma_{\\perp}", "formula_003017": "\\mathrm I - \\mathrm {V\\hspace{-0.6pt}I\\hspace{-0.6pt}I \\hspace{-0.6pt} I}", "formula_003022": "I_2", "formula_003024": "R/R_0", "formula_003023": "\\mathrm V", "formula_003026": "\\bm{n} = C \\bm{\\sigma}", "formula_003025": "t - t_{\\mathrm{EC}} = 0", "formula_003027": "\\mathrm {V\\hspace{-0.6pt}I\\hspace{-0.6pt}I \\hspace{-0.6pt} I}", "formula_003028": "\\mathrm {V\\hspace{-0.6pt}I}", "formula_003029": "t - t_\\mathrm{\\hspace{1pt} EC} \\geq t_\\mathrm{\\hspace{1pt} FE} - t_\\mathrm{\\hspace{1pt} EC}", "formula_003030": "c_\\mathrm{S}=0.0385", "formula_003031": "-0.58 \\pm 0.03", "formula_003032": "\\delta n = f \\delta n^{0} = \\frac{3\\cos^{2}(\\pi/2-\\varphi)-1}{2} \\delta n^{0},", "formula_003033": "c_\\mathrm{S} - c_\\mathrm{D} = 0.2", "formula_003035": "|\\delta n / \\sigma_{\\rm \\hspace{1pt} E}|", "formula_003034": "\\delta n = -n_{\\perp} < 0", "formula_003036": "t_\\mathrm{\\hspace{1pt} FE}-t_\\mathrm{\\hspace{1pt} EC}", "formula_003037": "\\delta = 2 C R \\sigma_{\\rm \\hspace{1pt} E} .", "formula_003039": "\\sim c^*_\\mathrm{S}", "formula_003038": "C=-2.77 \\times 10^{-7}", "formula_003040": "2\\times2", "formula_003041": "\\varphi", "formula_003042": "\\sigma_{\\perp}", "formula_003043": "\\Delta \\hspace{0.4pt} t", "formula_003044": "c_\\mathrm{D}", "formula_003045": "^{-1}", "formula_003046": "9 \\times 10^{-4}", "formula_003047": "c_\\mathrm{S}-c_\\mathrm{D}>0", "formula_003048": "c^*_\\mathrm{S} \\sim 0.2000", "formula_003049": "R_{0}", "formula_003050": "\\delta n = n_{\\parallel}-n_{\\perp} = C (\\sigma_{\\parallel}-\\sigma_{\\perp}),", "formula_003051": "n_{\\perp}", "formula_003052": "\\mathrm{I\\hspace{-0.6pt}I\\hspace{-0.6pt}I}", "formula_003054": "\\delta", "formula_003053": "\\mathrm {I\\hspace{-0.6pt}V}", "formula_003055": "\\frac{R(t)}{R_{0}} = \\left(\\frac{GR_{0}}{2\\Gamma}\\right)^{1/3}\\exp\\!\\left(-\\frac{t - t_{\\mathrm{\\hspace{1pt} EC}}}{3\\lambda_\\mathrm{E}}\\right),", "formula_003056": "I_1", "formula_003057": "t-t_{\\mathrm{\\hspace{1pt} EC}}", "formula_003060": "-7.67", "formula_003058": "0 \\leq t - t_\\mathrm{\\hspace{1pt} EC} < t_\\mathrm{\\hspace{1pt} FE} - t_\\mathrm{\\hspace{1pt} EC}", "formula_003059": "320", "formula_003061": "10^{-4}", "formula_003063": "T", "formula_003062": "\\omega_{an}", "formula_003065": "\\mu_{\\rm B}=|e|/2m_e", "formula_003064": "270", "formula_003068": "ns", "formula_003069": "n=a'", "formula_003067": "1.0799\\times 10^{-5}", "formula_003066": "\\begin{eqnarray}    \\Delta E_a = \\frac{e^2}{\\pi} \\sum\\limits_{L} (2L + 1)\\int\\limits_{0}^{\\infty}d\\omega\\,\\omega\\,n_{\\beta}(\\omega)\\\\\\times\\sum\\limits_{n}\\sum\\limits_{\\pm}\\frac{\\langle a |\\alpha_{\\mu}j_{L}(\\omega r)\\bm{C}_{L}|n\\rangle \\langle n| \\alpha^{\\mu}j_{L}(\\omega r)\\bm{C}_{L}|a \\rangle}{\\varepsilon_a-\\varepsilon_n \\pm \\omega + \\mathrm{i}\\,0}\\\\=\\frac{e^2}{\\pi} \\sum\\limits_{L} (2L + 1)\\int\\limits_{0}^{\\infty}d\\omega\\,\\omega\\,n_{\\beta}(\\omega)\\alpha_{L}(\\omega).\\end{eqnarray}", "formula_003070": "\\begin{eqnarray}\\langle \\mathcal{E}^2(t)\\rangle = \\frac{1}{2}\\int\\limits_0^\\infty \\mathcal{E}^2(\\omega)d\\omega = (831.9 \\text{ V/m})^2\\left[\\frac{\\text{T}(\\text{K})}{300}\\right]^4,\\\\\\langle \\mathcal{B}^2(t)\\rangle = (2.775 \\times 10^{-6} \\text{ Tesla})^2\\left[\\frac{\\text{T}(\\text{K})}{300}\\right]^4.\\end{eqnarray}", "formula_003072": "l", "formula_003071": "\\begin{eqnarray} \\Delta E_a^{(-)} = \\frac{e^2}{\\pi} \\sum_{n^{(-)}} \\left[  \\frac{1-\\bm{\\alpha}_1\\bm{\\alpha}_2}{r_{12}} I^{\\beta}_{n^{(-)} a}(r_{12})\\right]_{an^{(-)} n^{(-)} a},\\,\\,\\,\\, \\\\ I^{\\beta}_{n^{(-)} a}(r_{12}) = \\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega) \\sum\\limits_{\\pm}\\frac{\\sin\\omega r_{12}}{\\varepsilon_a-\\varepsilon_{n^{(-)}} \\pm \\omega + \\mathrm{i}\\,0},\\end{eqnarray}", "formula_003074": "(a n| r_1^4+r_2^4| na) = 2\\delta_{an}(a|r^4|n)", "formula_003073": "D_{\\mu_1 \\mu_2}(x_1,x_2)", "formula_003076": "1.1\\times 10^{-5}", "formula_003075": "-2.75 \\times 10^{-7}", "formula_003077": "-6.0\\times 10^{-14}", "formula_003078": "(\\Delta E_a^{\\rm wf}+\\Delta E_a^{\\rm rem})/\\Delta E_a^{\\rm BBRS}", "formula_003079": "\\int\\,dr\\, r^2(f_{n_a\\kappa_{a}}g_{n\\kappa_{n}}+f_{n\\kappa_{n}}g_{n_a\\kappa_{a}})", "formula_003080": "\\Delta E_a^{\\rm dia}", "formula_003082": "-6.528 \\times 10^{-8}", "formula_003081": "1.3549\\times 10^{-8}", "formula_003083": "g", "formula_003084": "1.2066\\times 10^{-5}", "formula_003085": "\\omega_{na}=E_n -E_a", "formula_003087": "5.8097\\times 10^2", "formula_003086": "\\begin{eqnarray}\\varphi_{a}= \\left[  1 - \\frac{(\\bm{\\sigma} \\bm{p})^2}{8 m_e^2c^2}  \\right] \\phi_{a},\\end{eqnarray}", "formula_003088": "[\\bm{r}\\times \\bm{\\alpha}]_i = \\epsilon_{ijk} r_j\\alpha_k", "formula_003089": "1075", "formula_003091": "\\begin{eqnarray}    \\Delta E_{a}  = \\sum \\limits_{\\substack{n \\ne a}} \\frac{|( a|\\hat{V}| n )|^2}{E_a-E_n}     = e^2 \\sum_{\\substack{i,\\,n \\ne a}} \\frac{\\mathcal{E}^2_{i} |( a|r_{i}| n  )  |^2}{E_a-E_n},\\end{eqnarray}", "formula_003092": "-1.7\\times 10^{-14}", "formula_003090": "-50.80", "formula_003093": "4.02 \\times 10^{-6}", "formula_003094": "T^4", "formula_003095": "e", "formula_003096": "\\omega\\sim m_{e}(\\alpha Z)^2", "formula_003098": "k", "formula_003097": "6.09\\times 10^{-5}", "formula_003100": "-\\mathrm{i}m_e(n|[r_i,H]|a) = (n|p_i|a)", "formula_003099": "[\\hat{A}(12)]_{abcd}\\equiv \\langle a(1) b(2)|\\hat{A} | c(1) d(2)\\rangle", "formula_003101": "\\psi_{a}(x)= \\psi_{a} (\\bm{r}) e^{-i \\varepsilon_{a} t}", "formula_003102": "\\delta E_a", "formula_003103": "-1.1 \\times 10^{-3}", "formula_003104": "-1.27\\times 10^{-5}", "formula_003105": "\\hat{V}", "formula_003107": "3r_i r_j - r^2\\delta_{ij}", "formula_003108": "\\bm{p}", "formula_003106": "\\langle a n| (\\bm{r}_1 \\bm{\\alpha}_2) (\\bm{r}_2 \\bm{\\alpha}_1) + (\\bm{\\alpha}_1 \\bm{\\alpha}_2) (\\bm{r}_1 \\bm{r}_2) | n a \\rangle", "formula_003109": "-1.73 \\times 10^{-4}", "formula_003110": "\\bm{\\sigma}", "formula_003111": "-1.65 \\times 10^{-5}", "formula_003113": "\\langle a | r^2| a\\rangle = \\frac{n_a^2}{2}(5n_a^2+1-3l_a(l_a+1))", "formula_003114": "9.9573\\times 10^7", "formula_003115": "-58.68", "formula_003112": "61", "formula_003116": "2s_{1/2}^{F=0}", "formula_003117": "6.3981\\times 10^7", "formula_003118": "\\omega^5", "formula_003119": "2s-1s", "formula_003120": "10^{-17}", "formula_003121": "10^{-3}", "formula_003122": "4.99\\times 10^{-6}", "formula_003124": "15041", "formula_003123": "\\begin{eqnarray}J_{5} = -\\frac{e^2}{15\\pi} \\sum\\limits_n \\int\\limits_0^\\infty d\\omega\\, n_\\beta(\\omega)\\omega^5\\frac{\\omega_{an} }{\\omega_{an}^2 - \\omega^2}\\\\\\times (a n | (\\bm{r}_1 \\bm{r}_2)(r_1^2 + r_2^2) | n a).\\end{eqnarray}", "formula_003125": "1.1092\\times 10^{-6}", "formula_003126": "2.3 \\times 10^{-16}", "formula_003127": "\\begin{eqnarray} \\langle  a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2)r^2_{12}  | n a \\rangle =  \\\\  \\langle  a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2) (r^2_1+r^2_2-2(\\bm{r}_1\\bm{r}_2)) | n a \\rangle. \\,\\,\\,\\,\\,\\end{eqnarray}", "formula_003128": "V", "formula_003129": "77", "formula_003130": "-3.54 \\times 10^{-4}", "formula_003131": "1.6\\times 10^{-2}", "formula_003132": "2.1870\\times 10^6", "formula_003133": "5.8098\\times 10^2", "formula_003134": "-43.79", "formula_003135": "2.4958\\times 10^{-6}", "formula_003136": "^{2+}", "formula_003138": "3", "formula_003139": "\\varepsilon", "formula_003137": "7.0794\\times 10^{-163}", "formula_003141": "\\omega\\ll \\omega_{an}", "formula_003140": "2.87 \\times 10^{-5}", "formula_003142": "^{87}", "formula_003143": "-3.80\\times10^{-2}", "formula_003144": "1.4083\\times 10^{-5}", "formula_003145": "T^6", "formula_003146": "n_\\beta\\rightarrow 0", "formula_003147": "-10.05", "formula_003148": "9.7324\\times 10^5", "formula_003149": "3s_{1/2}^{F=0}", "formula_003150": "4.7005\\times 10^4", "formula_003151": "4.4228\\times 10^{-9}", "formula_003152": "r\\sim (m_{e}\\alpha Z)^{-1}", "formula_003153": "2.8762\\times 10^6", "formula_003154": "3.0611\\times 10^5", "formula_003156": "1s_{1/2}^{F=1}-1s_{1/2}^{F=0}", "formula_003155": "\\begin{eqnarray}    \\frac{\\sin \\omega r_{12}}{\\omega r_{12}} = \\sum\\limits_{L=0}^{\\infty}(2L+1)j_{L}(\\omega r_{1})j_{L}(\\omega r_{2})    \\\\\\times    \\bm{C}_{L}(\\bm{n}_1)\\bm{C}_{L}(\\bm{n}_2),\\end{eqnarray}", "formula_003159": "n=a", "formula_003158": "3s_{1/2} - 1s_{1/2}", "formula_003157": "2p_{1/2}^{F=0}", "formula_003160": "3d_{3/2}^{F=1}", "formula_003162": "1s", "formula_003161": "0.6", "formula_003163": "\\begin{eqnarray}\\Gamma_a^{\\mathrm{ind}+\\mathrm{FL}} = \\frac{2e^2}{3\\pi}\\sum\\limits_n |(a|\\bm{r}|n)|^2 \\int\\limits_0^\\infty d\\omega\\,\\omega^3\\,n_\\beta(\\omega)\\times\\\\\\left[\\frac{\\Gamma_{na}}{(E_{na}-\\omega)^2+\\frac{1}{4}\\Gamma_{na}^2}+\\frac{\\Gamma_{na}}{(E_{na}+\\omega)^2+\\frac{1}{4}\\Gamma_{na}^2}\\right],\\end{eqnarray}", "formula_003165": "4.8336\\times 10^{-7}", "formula_003164": "\\text{P.V.}", "formula_003166": "-9.80 \\times 10^{-4}", "formula_003167": "\\Gamma_{i}^{\\rm nat}", "formula_003168": "\\Delta E_a^{\\rm rel}", "formula_003169": "Z = 1", "formula_003170": "a'\\rightarrow a", "formula_003171": "0.0", "formula_003172": "4.9583", "formula_003174": "\\bm{C}_{L}", "formula_003173": "4.3415\\times 10^7", "formula_003175": "-288.38", "formula_003176": "-5.7\\times 10^{-15}", "formula_003177": "(\\tilde{a}|\\bm{r}|\\tilde{n})", "formula_003178": "1.1094\\times 10^{-4}", "formula_003180": "g_{\\mu\\nu}", "formula_003181": "-11.43", "formula_003179": "2.293\\times 10^{-7}", "formula_003183": "\\,\\,\\,5.6\\times 10^{-14}", "formula_003182": "\\begin{eqnarray}|(\\tilde{a}|\\bm{r}|\\tilde{n})|^2\\approx |(a|\\bm{r}|n)|^2 - \\frac{E_a+E_n}{2m_e c^2}|(a|\\bm{r}|n)|^2.\\end{eqnarray}", "formula_003184": "-\\mathrm{i}m_e(n|[r_j,H]|a) = (n|p_j|a)", "formula_003185": "-0.19", "formula_003187": "\\Delta E_a^{\\mathrm{BBRS}}", "formula_003186": "3.1377\\times 10^7", "formula_003189": "\\,\\,\\,8.0\\times 10^{-14}", "formula_003188": "1.2624\\times 10^{-11}", "formula_003190": "4.19\\times 10^{-6}", "formula_003191": "Z", "formula_003192": "2^{L}", "formula_003193": "^ {87}", "formula_003194": "1s_{1/2}^{F=0}", "formula_003196": "\\begin{eqnarray}\\Delta E_a^{\\rm dia} = \\frac{\\pi^3 e^2}{45\\beta^4}\\langle a | r^2| a\\rangle,\\end{eqnarray}", "formula_003195": "-12.95", "formula_003197": "\\begin{eqnarray}\\Delta E_a^{\\rm rel} = \\frac{e^2}{6\\pi}\\sum\\limits_n \\int\\limits^{\\infty}_0 d\\omega\\, \\omega^3\\,n_{\\beta}(\\omega) \\langle  a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2)(r_1^2+r_2^2) \\\\- 2([\\bm{r}_1\\times\\bm{\\alpha}_1]\\cdot[\\bm{r}_2\\times\\bm{\\alpha}_2]) - 2(\\bm{r}_1\\bm{\\alpha}_2)(\\bm{r}_2\\bm{\\alpha}_1)| na \\rangle \\qquad\\\\\\times\\left( \\frac{1}{\\varepsilon_a - \\varepsilon_n + \\omega + \\mathrm{i}\\,0} +  \\frac{1}{\\varepsilon_a - \\varepsilon_n - \\omega + \\mathrm{i}\\, 0}   \\right).\\qquad\\end{eqnarray}", "formula_003198": "\\begin{eqnarray}W_{aa'}^{\\mathrm{ind}}(\\mathrm{M1}) = \\frac{4}{3}n_\\beta(|\\omega_{aa'}|)\\omega_{aa'}^3 |(  a| \\bm{\\mu} |a' ) |^2.\\end{eqnarray}", "formula_003199": "1.99 \\times 10^{-8}", "formula_003200": "\\frac{(k_{B}T)^4}{\\alpha m_{e}^3Z^2}", "formula_003201": "\\mu_{B} = |e|/2m_e", "formula_003202": "6.5759\\times 10^{-4}", "formula_003203": "r_{12}^2=r_1^2+r_2^2-2\\bm{r}_1\\bm{r}_2", "formula_003207": "^{+}", "formula_003204": "3000", "formula_003206": "\\omega_{na}\\sim m_{e}(\\alpha Z)^2", "formula_003205": "\\alpha^2", "formula_003208": "-274.70", "formula_003211": "S", "formula_003209": "\\omega_{an}=0", "formula_003212": "6.318\\times 10^{10}", "formula_003210": "4.0331\\times 10^{-1}", "formula_003213": "\\begin{eqnarray}  \\Delta E_a^{\\mathrm{}} = -\\frac{2}{3\\pi}\\sum\\limits_n ( a n|   (\\bm{\\mu}_1\\bm{\\mu}_2) |n a ) \\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega)\\,\\omega^3   \\\\    \\times  \\sum\\limits_{\\pm} \\frac{1}{E_a-E_n \\pm \\omega + i 0} .\\end{eqnarray}", "formula_003214": "7.74 \\times 10^{-5}", "formula_003215": "\\bm{A}", "formula_003216": "\\varepsilon_{n^{(-)}}", "formula_003217": "-77.36", "formula_003218": "\\begin{eqnarray}\\Delta E_a^{\\rm rel} \\approx   \\Delta E_a^{\\mathcal{B}} + \\Delta E_a^{\\mathcal{Q}} + \\frac{e^2}{6\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega) \\frac{2\\omega_{an}}{\\omega_{an}^2-\\omega^2}( a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2)(r_1^2+r_2^2) | n a) - \\qquad\\\\\\frac{e^2}{6\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega)\\omega_{an} (a n|(\\bm{r}_1 \\bm{r}_2)^2|n a) + \\frac{e^2}{60\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\,  \\omega^5 n_\\beta(\\omega)\\frac{\\omega_{an}}{\\omega_{an}^2-\\omega^2}(a n|r_1^4+r_2^4-4(\\bm{r}_1 \\bm{r}_2)(r_1^2+r_2^2)|n a)\\\\=\\Delta E_a^{\\mathcal{B}} + \\Delta E_a^{\\mathcal{Q}} +\\Delta E_a^{\\rm rem}.\\end{eqnarray}", "formula_003219": "\\sum_{k}|k\\rangle \\langle k|", "formula_003220": "\\varepsilon_a-\\varepsilon_{n^{(-)}}\\approx 2mc^2", "formula_003222": "-1.11 \\times 10^{-5}", "formula_003221": "n", "formula_003223": "-1.27 \\times 10^{-3}", "formula_003224": "\\bm{r}V", "formula_003225": "-1.1\\times 10^{-3}", "formula_003226": "\\omega \\sim \\omega_{an}", "formula_003227": "\\Gamma^{\\beta}_a", "formula_003230": ">1", "formula_003229": "\\begin{eqnarray}\\langle a| \\alpha_{1i}r_{1j} + \\alpha_{1j}r_{1i}|n\\rangle \\approx \\\\\\frac{1}{m_e}(a| r_{1i}p_{1j}+r_{1j}p_{1i}-\\mathrm{i}\\delta_{ij}|n),\\\\\\langle n| \\alpha_{1i}r_{1j} + \\alpha_{1j}r_{1i}|a\\rangle \\approx \\\\\\frac{1}{m_e}(n| r_{2i}p_{2j}+r_{2j}p_{2i}-\\mathrm{i}\\delta_{ij}|a).\\end{eqnarray}", "formula_003231": "-1.35", "formula_003232": "1.6578\\times 10^6", "formula_003228": "\\Gamma_a^{\\mathrm{ind}}", "formula_003233": "\\gamma^{\\mu}", "formula_003234": "-8.79", "formula_003235": "F=4\\rightarrow F=3", "formula_003236": "3.0613\\times 10^5", "formula_003237": "9.7311\\times 10^5", "formula_003238": "-4.4990\\times 10^{-7}", "formula_003239": "\\begin{eqnarray} I^{\\beta}_{na}(r_{12}) = \\mathrm{P.V.}\\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega) \\sum\\limits_{\\pm}\\frac{\\sin\\omega r_{12}}{\\varepsilon_a-\\varepsilon_n \\pm \\omega} +  \\\\   + \\mathrm{i} \\pi \\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega) \\sin(\\omega r_{12}) \\sum\\limits_{\\pm} \\delta(\\varepsilon_n -\\varepsilon_a \\pm \\omega).\\end{eqnarray}", "formula_003240": "\\begin{eqnarray}   \\mathcal{E}^2(\\omega) = \\mathcal{B}^2(\\omega) =  \\frac{8}{\\pi}\\frac{\\omega^3}{e^{\\beta \\omega}-1},\\end{eqnarray}", "formula_003242": "4", "formula_003241": "6.3169\\times 10^6", "formula_003243": "^{9+}", "formula_003244": "2s^2(^1S_{0})-2s2p(^3P_{0})", "formula_003246": "4.8148\\times 10^{-7}", "formula_003245": "-2.90 \\times 10^{-4}", "formula_003247": "\\bm{\\mu}^{\\mathrm{nr}} = \\mu_{\\rm B}(\\bm{l}+2\\bm{s})", "formula_003248": "10^{-5}", "formula_003249": "5s^2(^1S_{0})-5s5p(^3P_{0})", "formula_003250": "J_3+<PERSON>_4+J_5", "formula_003251": "\\begin{eqnarray}\\langle a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2) (\\bm{r}_1 \\bm{r}_2) + (\\bm{r}_1 \\bm{\\alpha}_2) (\\bm{r}_2 \\bm{\\alpha}_1) | n a \\rangle =\\langle a n| \\alpha_{1i}\\alpha_{2i} r_{1j}r_{2j} + \\alpha_{1i}\\alpha_{2j} r_{1j}r_{2i} | n a \\rangle\\end{eqnarray}", "formula_003254": "2.4\\times 10^{-4}", "formula_003252": "6.7414\\times 10^{-9}", "formula_003256": "2.6\\times 10^{-3}", "formula_003255": "\\beta^{-2}", "formula_003253": "4s", "formula_003258": "W^{\\mathrm{spon}}_{if}(\\mathrm{M1})", "formula_003257": "\\bm{\\mathcal{E}}(t) = \\bm{\\mathcal{E}}_0 \\cos(\\omega t)", "formula_003259": "\\Delta E_a^\\Sigma=\\Delta E_a^{\\rm wf}+\\Delta E_a^{\\rm rem}", "formula_003260": "2.8751\\times 10^6", "formula_003261": "\\begin{eqnarray}\\Delta E_a^{\\rm rem} =  \\frac{e^2}{6\\pi}\\sum\\limits_{n}\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega) \\omega_{an} \\\\\\times( a n | 4 (\\bm{r}_1 \\bm{r}_2) r_1^2 - (\\bm{r}_1 \\bm{r}_2)^2| n a).\\end{eqnarray}", "formula_003262": "r_{12}\\equiv|\\bm{r}_1-\\bm{r}_2|", "formula_003264": "-4.94\\times10^{-3}", "formula_003263": "\\Delta E_{a}", "formula_003266": "2.41 \\times 10^{-7}", "formula_003265": "n=2, 3, 4, 6", "formula_003267": "\\Delta E_{a'a}", "formula_003268": "T=300", "formula_003270": "330", "formula_003269": "\\gamma_T", "formula_003271": "\\bm{\\mu}", "formula_003273": "-51.19", "formula_003272": "\\begin{eqnarray}D_{\\mu_1 \\mu_2}(x_1,x_2) = \\frac{g_{\\mu_1 \\mu_2}}{2\\pi \\mathrm{i} r_{12}}\\int\\limits_{-\\infty}^{+\\infty}d\\omega e^{\\mathrm{i}|\\omega|r_{12} - \\mathrm{i}\\omega(t_1-t_2)} \\\\- \\frac{g_{\\mu_1 \\mu_2}}{\\pi r_{12}}\\int\\limits_{-\\infty}^{+\\infty}d\\omega\\, n_{\\beta}(|\\omega|) \\sin{|\\omega|r_{12}} \\, e^{-\\mathrm{i}\\omega(t_1-t_2)}.\\end{eqnarray}", "formula_003274": "\\begin{eqnarray}\\Delta E_a^{\\rm rel-} = \\text{first} -\\frac{e^2}{6\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega)\\omega_{an} (a n|(\\bm{r}_1 \\bm{r}_2)^2|n a) + \\\\-\\frac{e^2}{6\\pi}\\frac{3}{5}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, n_\\beta(\\omega)\\frac{\\omega_{an}\\omega^5}{\\omega_{an}^2-\\omega^2}( a n| (\\bm{r}_1\\bm{r}_2)^2 |n a) +\\frac{e^2}{6\\pi}\\frac{1}{5}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, n_\\beta(\\omega)\\frac{\\omega_{an}\\omega^5}{\\omega_{an}^2-\\omega^2}(a n| r_1^2r_2^2 |n a)\\end{eqnarray}", "formula_003275": "1.7335\\times 10^{-30}", "formula_003277": "3.6136\\times 10^{-6}", "formula_003278": "\\bm{l}", "formula_003280": "\\langle  a| \\mu_i  | n \\rangle", "formula_003279": "5.5\\times 10^{-6}", "formula_003276": "2s_{1/2}^{F=1}-2s_{1/2}^{F=0}", "formula_003281": "1.2674\\times 10^{-5}", "formula_003284": "\\beta=1/k_{\\rm B}T", "formula_003282": "\\,\\,\\,1.0\\times 10^{-12}", "formula_003283": "-1.43 \\times 10^{-3}", "formula_003285": "5.96 \\times 10^{-6}", "formula_003286": "-1.5\\times 10^{-13}", "formula_003288": "\\begin{eqnarray}  \\Delta E^{\\rm BBRZ}_a = -\\frac{4}{3\\pi}  |(  a| \\bm{\\mu} |a' ) |^2  \\int\\limits^{\\infty}_0 d\\omega  \\frac{n_{\\beta}(\\omega)\\omega^3 \\omega_{aa'}}{\\omega_{aa'}^2-\\omega^2}.\\end{eqnarray}", "formula_003287": "\\bm{r}", "formula_003290": "\\hbar = c = m_{e}=1", "formula_003289": "\\Delta E_a^{\\rm wf}+\\Delta E_a^{\\rm rem}", "formula_003292": "\\begin{eqnarray}\\Gamma_a =  -2{\\rm Im} \\Delta E_a\\end{eqnarray}", "formula_003291": "-0.91", "formula_003294": "-1.08", "formula_003293": "1.2627\\times 10^{-11}", "formula_003295": "+\\omega", "formula_003296": "2.70\\times 10^{-6}", "formula_003297": "290", "formula_003298": "1.1227\\times 10^{-1}", "formula_003299": "T = 300", "formula_003301": "m_e", "formula_003300": "-2.1\\times 10^{-5}", "formula_003302": "-67.51", "formula_003304": "D(x_1,x_2)", "formula_003303": "2mc^2", "formula_003306": "\\omega", "formula_003305": "1/c", "formula_003307": "6.3993\\times 10^7", "formula_003308": "1.8778\\times 10^{-9}", "formula_003309": "p^2|n) = 2m_e (E_n-V)|n)", "formula_003310": "\\begin{eqnarray}    \\Delta E_{a}  = \\sum_{\\substack{i,\\,n \\ne a}}\\sum\\limits_{\\pm} \\frac{e^2 \\mathcal{E}_{0i}^2}{4} \\frac{|( a|r_{i}| n )|^2}{E_a-E_n \\pm \\omega},\\end{eqnarray}", "formula_003311": "7.4684\\times 10^{-3}", "formula_003312": "4.5062\\times 10^7", "formula_003313": "\\alpha_{L}(\\omega)", "formula_003314": "\\int_{0}^{\\infty}\\omega^3n_{\\beta}(\\omega) \\sim (k_{B}T)^4", "formula_003315": "-9.26 \\times 10^{-5}", "formula_003316": "\\begin{eqnarray}\\Delta E_a^{\\rm wf} = -\\frac{2e^2}{3 \\pi} \\sum_{n} | ( a |\\bm{r} | n )|^2 \\\\\\times\\int\\limits^{\\infty}_0 d\\omega\\,\\omega^3 n_{\\beta}(\\omega) \\frac{E_{n}^2-E_a^2}{\\omega^2_{na} - \\omega^2}.\\qquad\\end{eqnarray}", "formula_003317": "1.011\\times 10^8", "formula_003318": "4.7017\\times 10^4", "formula_003319": "1.3996\\times 10^{-9}", "formula_003321": "30000", "formula_003320": "6.89 \\times 10^{-5}", "formula_003322": "\\bm{s}=\\bm{\\sigma}/2", "formula_003323": "\\sin\\omega r_{12}", "formula_003324": "2p_{3/2}^{F=1}", "formula_003326": "6.3512\\times 10^{-182}", "formula_003328": "6.9937\\times 10^{-5}", "formula_003327": "\\bm{n}=\\bm{r}/|\\bm{r}|", "formula_003325": "\\begin{eqnarray} \\Delta E_a^{(-)}\\approx \\frac{e^2}{\\pi m_e c^2}\\sum_{n^{(-)}}\\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega)\\omega\\, \\delta_{a n^{(-)}} - \\qquad\\\\\\frac{e^2}{\\pi m_e c^2}\\sum_{n^{(-)}}\\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega)\\langle an^{(-)}|\\omega \\bm{\\alpha}_1\\bm{\\alpha}_2+ \\frac{\\omega^3}{3}\\bm{r}_1\\bm{r}_2|n^{(-)} a\\rangle \\\\-\\frac{e^2}{3\\pi m_e c^2}\\sum_{n^{(-)}}\\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega)\\omega^3\\langle a|\\bm{r}^2|n^{(-)} \\rangle\\, \\delta_{a n^{(-)}}+\\dots\\end{eqnarray}", "formula_003330": "\\Delta \\nu^{\\mathrm{BBRZ}}/\\nu", "formula_003329": "-4.4 \\times 10^{-5}", "formula_003331": "\\Gamma_a^{\\mathrm{ind+FL}}", "formula_003333": "0.5\\,\\%", "formula_003332": "\\Gamma^{\\beta+\\mathrm{FL}}_a(\\mathrm{M1})", "formula_003334": "9.2339", "formula_003335": "5.25 \\times 10^{-6}", "formula_003336": "\\mathcal{E}_{i}", "formula_003337": "^{-1}", "formula_003338": "j_{L}(\\omega r)", "formula_003339": "\\bm{j}=\\bm{l}+\\bm{s}", "formula_003340": "-9.10", "formula_003341": "-282.11", "formula_003342": "\\omega_{an}\\omega^2", "formula_003343": "\\alpha Z", "formula_003344": "9.0973\\times 10^{-2}", "formula_003345": "-\\omega", "formula_003346": "-262.68", "formula_003347": "\\omega r \\sim (\\alpha Z) {\\rm\\, r.u.} \\ll 1", "formula_003350": "1.1964\\times 10^3", "formula_003351": "\\begin{eqnarray} \\Delta E_a^{(-)}\\approx - \\frac{e^2}{3\\pi}\\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega)\\omega^3\\langle a|r^2| a\\rangle\\end{eqnarray}", "formula_003348": "\\begin{eqnarray}J_{3} = \\frac{2e^2}{3\\pi} \\sum\\limits_{nk} \\int\\limits_0^\\infty d\\omega\\,     \\omega^3 n_\\beta(\\omega) \\omega_{ak}     ( a | r_i | k) ( k | r^2 | n) ( n | r_i | a) \\\\     + \\frac{2e^2}{3\\pi} \\sum\\limits_{nk} \\int\\limits_0^\\infty d\\omega\\,     \\frac{\\omega_{ak} \\omega^5 n_\\beta(\\omega) }{\\omega_{an}^2 - \\omega^2} ( a | r_i | k) ( k | r^2 | n) ( n | r_i | a).\\,\\,\\,\\,\\,\\,\\,    \\end{eqnarray}", "formula_003349": "20", "formula_003352": "\\bm{s} = \\bm{\\sigma}/2", "formula_003353": "E", "formula_003354": "\\Delta E_a^\\Sigma", "formula_003356": "r^2", "formula_003355": "\\bm{r}V\\sim \\bm{n}", "formula_003357": "-7.21 \\times 10^{-5}", "formula_003358": "n_\\beta(\\omega_0)>1", "formula_003359": "9\\%", "formula_003360": "S(x_1,x_2)", "formula_003361": "f", "formula_003362": "-6.4\\times 10^{-5}", "formula_003363": "\\begin{eqnarray}   \\bm{\\mu}= - \\mu_{B} (\\bm{l}+2\\bm{s}),\\end{eqnarray}", "formula_003364": "3s_{1/2} - 2s_{1/2}", "formula_003365": "-8.60 \\times 10^{-7}", "formula_003366": "\\begin{eqnarray}\\Delta E_a^{\\mathcal{Q}} = - \\frac{e^2}{90\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\frac{\\omega_{an} \\omega^5 n_\\beta}{\\omega_{an}^2-\\omega^2}(an| \\mathcal{Q}^{(1)}_{ij} \\mathcal{Q}^{(2)}_{ij} |na)\\,\\,\\,\\end{eqnarray}", "formula_003367": "3.8985\\times 10^{-6}", "formula_003368": "m_l", "formula_003369": "1.1978\\times 10^{-7}", "formula_003370": "\\Delta E_a^{\\mathcal{B}}", "formula_003371": "\\Delta E_a^{\\rm wf}", "formula_003373": "\\Gamma_{1s}^\\beta = 1.35\\times 10^{-8}", "formula_003372": "\\begin{eqnarray}([\\bm{r}_1\\times\\bm{\\alpha}_1]\\cdot[\\bm{r}_2\\times\\bm{\\alpha}_2]) =  \\\\(\\bm{\\alpha}_1 \\bm{\\alpha}_2) (\\bm{r}_1\\bm{r}_2) - (\\bm{r}_1\\bm{\\alpha}_2)(\\bm{r}_2\\bm{\\alpha}_1),\\end{eqnarray}", "formula_003375": "5.33 \\times 10^{-5}", "formula_003374": "\\begin{eqnarray} \\Delta E_a^{(-)}\\approx - \\frac{e^2}{\\pi m_e c^2}\\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega)\\omega\\,\\langle a| \\bm{\\alpha}^2+ \\frac{\\omega^2}{3}\\bm{r}^2| a\\rangle\\end{eqnarray}", "formula_003376": "n= n_a", "formula_003377": "\\begin{eqnarray}\\Delta E_a^{\\rm dia} = \\frac{\\pi^3 e^2}{45\\beta^4}\\langle a | r^2| a\\rangle.\\end{eqnarray}", "formula_003378": "1.9729\\times 10^{-13}", "formula_003379": "4.5082\\times 10^{-5}", "formula_003381": "3.5261\\times 10^{-5}", "formula_003380": "n_{\\beta}(\\omega)", "formula_003382": "-2.21 \\times 10^{-4}", "formula_003383": "2.22 \\times 10^{-5}", "formula_003384": "\\bm{\\pi}= \\bm{p}+\\frac{e}{c}\\bm{A}", "formula_003386": "-4.206 \\times 10^{-8}", "formula_003385": "8.22\\times 10^{-8}", "formula_003387": "\\begin{eqnarray}J_{3}=\\frac{2e^2}{3\\pi}\\sum\\limits_{nk}\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega) \\frac{\\omega_{an}^2\\omega_{ak}}{\\omega_{an}^2-\\omega^2}\\\\\\times( a | r_i| k) ( k | r^2 | n) ( n | r_i | a),\\end{eqnarray}", "formula_003388": "n_\\beta", "formula_003389": "2.9247\\times 10^{-11}", "formula_003390": "4.3973\\times 10^{-5}", "formula_003391": "1.5945\\times 10^1", "formula_003392": "3p_{1/2}^{F=0}", "formula_003393": "\\Delta E_a = \\Delta E^{\\rm BBRZ}_a - \\mathrm{i} \\Gamma_a^\\beta(\\mathrm{M}1)/2", "formula_003394": "5d^{10}6s(^2S_{1/2})-5d^96s^2(^2D_{5/2})", "formula_003395": "5.6040\\times 10^{-18}", "formula_003397": "n=100,l=0", "formula_003398": "\\kappa_{n}=(j_{n}+\\frac{1}{2})(-1)^{j_{n}+l_{n}+s}", "formula_003396": "2.7167\\times 10^6", "formula_003400": "-4.25 \\times 10^{-6}", "formula_003399": "\\mathcal{Q}^{(1)}_{ij} = 3r_{1i} r_{1j}-r^2_1\\delta_{ij}", "formula_003401": "a", "formula_003402": "\\mathcal{E}_{0i}^2(\\omega) = \\frac{1}{3}\\mathcal{E}^2(\\omega)", "formula_003405": "3s", "formula_003404": "\\varepsilon_a", "formula_003403": "6s", "formula_003406": "\\mathrm{i}m_e(n|[H, r_ir_j]|a) =(n|r_ip_j+r_jp_i|a)", "formula_003407": "-1.53", "formula_003408": "3.2221\\times 10^{-3}", "formula_003409": "\\mathcal{B}^2 \\bm{r}^2", "formula_003410": "-1.98\\times 10^{-4}", "formula_003411": "7.8665\\times 10^{-5}", "formula_003412": "-6.33\\times 10^{-5}", "formula_003413": "\\begin{eqnarray}  \\Delta E^{\\rm BBRZr}_a = -\\frac{2}{3\\pi}  |( a| \\bm{\\mu} |a' ) |^2  \\int\\limits^{\\infty}_0 d\\omega\\,\\omega^3\\,n_{\\beta}(\\omega) \\times\\\\\\left( \\frac{\\omega_{aa'}+\\omega}{(\\omega_{aa'}+\\omega)^2+\\frac{1}{4}\\Gamma^2} + \\frac{\\omega_{aa'}-\\omega}{(\\omega_{aa'}-\\omega)^2+\\frac{1}{4}\\Gamma^2}\\right),\\end{eqnarray}", "formula_003414": "e\\bm{r} \\bm{\\mathcal{E}}", "formula_003415": "n<a", "formula_003416": "58.5474", "formula_003417": "-8.912 \\times 10^{-8}", "formula_003418": "F=5\\rightarrow F=4", "formula_003419": "\\begin{eqnarray}\\langle  a| \\mu_i  | n \\rangle \\approx \\frac{|e|}{2m_e}( a|[\\bm{r}\\times \\bm{p}]_i + \\sigma_i|n),\\end{eqnarray}", "formula_003420": "\\bm{\\alpha}", "formula_003421": "2.8689\\times 10^{-15}", "formula_003423": "-1.4 \\times 10^{-4}", "formula_003422": "2.11", "formula_003424": "(3r_{1i} r_{1j}-r^2_1\\delta_{ij})(3r_{2i} r_{2j}-r^2_1\\delta_{ij}) \\equiv \\mathcal{Q}^{(1)}_{ij} \\mathcal{Q}^{(2)}_{ij} = 9(\\bm{r}_1\\bm{r}_2)^2-3r_1^2r_2^2", "formula_003426": "a'", "formula_003425": "-1.67 \\times 10^{-4}", "formula_003427": "4000", "formula_003429": "-1.4\\times 10^{-14}", "formula_003428": "n_\\beta(\\omega)", "formula_003432": "\\delta_a^\\Sigma", "formula_003430": "1/c^2", "formula_003431": "r^2_{12}=r^2_1+r^2_2-2\\bm{r}_1\\bm{r}_2", "formula_003433": "[\\bm{r}\\times\\bm{\\alpha}]_i", "formula_003434": "\\overline{\\psi}", "formula_003436": "i,j", "formula_003435": "-8.19 \\times 10^{-5}", "formula_003438": "--", "formula_003437": "\\begin{eqnarray}\\begin{pmatrix}\\varphi_a^* & \\chi_a^*\\end{pmatrix} \\begin{pmatrix}0 & r_{j}\\sigma_{k}\\\\r_{j}\\sigma_{k} & 0\\end{pmatrix} \\begin{pmatrix}\\varphi_n \\\\ \\chi_n\\end{pmatrix} \\approx \\qquad\\\\\\frac{1}{2m_e}  \\varphi_a^* \\left( r_jp_k + ir_j[\\bm{p} \\times \\bm{\\sigma}]_k + p_k r_j + i[\\bm{\\sigma} \\times \\bm{p}]_k r_j     \\right)\\varphi_n.\\end{eqnarray}", "formula_003439": "\\phi_{a}", "formula_003440": "\\Delta E_a^{\\rm wf} \\sim\\frac{(k_{B}T)^4}{\\alpha m_{e}^2Z^2}", "formula_003441": "\\begin{eqnarray}\\Delta E_a^{\\rm rem} =   \\frac{e^2}{3\\pi}\\frac{\\pi^4(k_{\\rm B} T)^4}{15}( a |  r^2(\\bm{r}\\cdot \\bm{\\nabla})| a),\\end{eqnarray}", "formula_003443": "\\Gamma=\\Gamma_a+\\Gamma_{a'}", "formula_003442": "\\begin{eqnarray}\\Delta E_a^{\\mathrm{BBRZ}} = -\\frac{2}{3\\pi}\\sum\\limits_n\\langle a n|   (\\bm{\\mu}_1\\bm{\\mu}_2) |n a \\rangle \\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega)\\,\\omega^3 \\,\\,\\,\\,\\  \\\\    \\times  \\left( \\frac{1}{\\varepsilon_a-\\varepsilon_n + \\omega + i 0} +  \\frac{1}{\\varepsilon_a-\\varepsilon_n - \\omega + i 0}   \\right),\\end{eqnarray}", "formula_003444": "3p_{3/2}^{F=1}", "formula_003446": "4.7\\times 10^{-2}", "formula_003447": "\\omega\\rightarrow m_ec^2", "formula_003445": "-2.6\\times 10^{-5}", "formula_003448": "\\sim 1/c^4", "formula_003450": "n_\\beta(|\\omega_{aa'}|)", "formula_003449": "\\bm{\\alpha}_i\\bm{\\alpha}_j = \\delta_{ij}+\\mathrm{i}\\varepsilon_{ijk}\\bm{\\Sigma}_k", "formula_003453": "\\sim(k_{\\rm B}T)^4/(m\\alpha Z^2)", "formula_003451": "1.2607", "formula_003452": "\\pm", "formula_003454": "l_a", "formula_003456": "1.4437\\times 10^{-6}", "formula_003455": "\\,\\,\\,8.3\\times 10^{-15}", "formula_003458": "t", "formula_003457": "^{43}", "formula_003459": "2s_{1/2} - 1s_{1/2}", "formula_003460": "1.1201\\times 10^{-5}", "formula_003461": "-1.7\\times 10^{-13}", "formula_003462": "n_{\\beta}(\\omega)= \\left(\\mathrm{exp}\\left (\\omega/k_{\\rm B} T\\right)-1\\right)^{-1}", "formula_003463": "9.8\\times 10^{-3}", "formula_003464": "k_{\\rm B} T\\ll \\omega_{aa'}", "formula_003465": "-273.48", "formula_003467": "-8.1 \\times 10^{-6}", "formula_003466": "5.117\\times 10^8", "formula_003468": "\\sin", "formula_003469": "n\\neq a", "formula_003470": "\\begin{eqnarray}\\Delta E_a = \\frac{4e^2}{3 \\pi} \\sum_{n} | ( a |\\bm{r} | n )|^2 \\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega) \\frac{\\omega_{na}\\omega^3}{\\omega^2_{na} - \\omega^2}  \\\\- \\frac{2  e^2}{3} \\mathrm{i} \\sum_{n } n_{\\beta}(|\\omega_{an}|)\\,|\\omega_{an}|^3\\, | ( a |\\bm{r} | n )|^2,\\end{eqnarray}", "formula_003471": "-5.0\\times 10^{-12}", "formula_003472": "3s^2(^1S_{0})-3s3p(^3P_{0})", "formula_003473": "2", "formula_003474": "( nlm_l|\\hat{V}|nlm_l ) = 0", "formula_003475": "-1.0\\times 10^{-6}", "formula_003477": "x=(t,\\bm{r})", "formula_003476": "4.60\\times 10^{-6}", "formula_003478": "1.1749\\times 10^{-4}", "formula_003480": "3p", "formula_003481": "2.5\\times 10^{-3}", "formula_003479": "\\chi\\approx \\frac{(\\bm{\\sigma} \\bm{p})}{2m_e}\\varphi", "formula_003482": "1.1296\\times 10^{-6}", "formula_003484": "3.1376\\times 10^7", "formula_003483": "\\begin{eqnarray}\\Delta E_a^{\\rm rel} = -\\frac{2}{3\\pi}\\sum\\limits_{n} \\int\\limits^{\\infty}_0 d\\omega\\, \\omega^3\\,n_{\\beta}(\\omega) \\Big[ \\langle  a n| (\\bm{\\mu}_1 \\bm{\\mu}_2)| na \\rangle \\\\-\\frac{e^2}{4}\\langle  a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2)(r_1^2+r_2^2) - (\\bm{\\alpha}_1\\bm{\\alpha}_2)(\\bm{r}_1\\bm{r}_1) | na \\rangle + \\qquad\\\\ \\frac{e^2}{4}\\langle  a n| (\\bm{r}_1\\bm{\\alpha}_2)(\\bm{r}_2\\bm{\\alpha}_1)| na \\rangle  \\Big]\\sum\\limits_{\\pm}\\frac{1}{\\varepsilon_a-\\varepsilon_n \\pm \\omega + \\mathrm{i}\\,0},\\qquad\\end{eqnarray}", "formula_003485": "-2.83 \\times 10^{-4}", "formula_003486": "m_{e}", "formula_003487": "\\begin{eqnarray}\\Delta E_a^{\\rm rel}= \\frac{e^2 }{6 \\pi} \\sum\\limits_n \\int\\limits^{\\infty}_0 d\\omega\\, \\omega^3\\,n_{\\beta}(\\omega)  \\langle  a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2) r^2_{12}   | n a \\rangle \\,\\,\\,  \\\\  \\times\\left( \\frac{1}{\\varepsilon_a-\\varepsilon_n + \\omega + \\mathrm{i}\\, 0} +  \\frac{1}{\\varepsilon_a-\\varepsilon_n - \\omega + \\mathrm{i}\\,0}   \\right).\\end{eqnarray}", "formula_003489": "r_i r_j", "formula_003488": "W^{\\mathrm{ind}}_{if}(\\mathrm{M1})", "formula_003491": "1/2", "formula_003490": "I^{\\beta}_{na}(r_{12})", "formula_003492": "\\begin{eqnarray}I^{\\beta}_{na}(r_{12}) = \\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega) \\sum\\limits_{\\pm}\\frac{\\sin\\omega r_{12}}{\\varepsilon_a-\\varepsilon_n \\pm \\omega + \\mathrm{i}\\,0}.\\end{eqnarray}", "formula_003493": "\\bm{a}", "formula_003495": "-1.04", "formula_003496": "n^{(-)}", "formula_003494": "1.0212", "formula_003497": "-4.9\\times 10^{-5}", "formula_003498": "1.7 \\times 10^{-12}", "formula_003500": "F=1\\rightarrow F=0", "formula_003499": "4.5063\\times 10^7", "formula_003501": "1.1999\\times 10^{-4}", "formula_003503": "-50.8", "formula_003506": "c", "formula_003502": "\\begin{eqnarray}[r_i,p_j] &=& \\mathrm{i} \\delta_{ij},\\\\(\\bm{\\sigma}\\bm{a})\\bm{\\sigma}  &=& \\bm{a} + \\mathrm{i} [\\bm{\\sigma} \\times \\bm{a}],\\\\\\bm{\\sigma}(\\bm{\\sigma}\\bm{a})  &=& \\bm{a} + \\mathrm{i} [\\bm{a} \\times  \\bm{\\sigma}].\\end{eqnarray}", "formula_003505": "-5.52\\times 10^{-5}", "formula_003504": "-2.3\\times 10^{-15}", "formula_003507": "-1.45 \\times 10^{-5}", "formula_003508": "\\Gamma_{2s}^{\\rm nat} = 8.229", "formula_003510": "\\Delta \\nu^{\\mathrm{BBRZ}} \\equiv \\Delta E_a^{\\rm BBRZ} - \\Delta E_{a'}^{\\rm BBRZ}", "formula_003509": "-2.56 \\times 10^{-4}", "formula_003512": "2. 1 \\times 10^{-18}", "formula_003511": "1.7\\times 10^{-7}", "formula_003513": "1.9730\\times 10^{-13}", "formula_003514": "\\begin{eqnarray}\\Delta E_a^{\\rm rel-} = \\frac{e^2}{6\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega) \\frac{2\\varepsilon_{an}}{\\varepsilon_{an}^2-\\omega^2}\\langle a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2)(r_1^2+r_2^2) + \\frac{\\omega^2}{20}(r_1^4+r_2^4-4(\\bm{r}_1 \\bm{r}_2)(r_1^2+r_2^2)) | n a\\rangle  -\\qquad\\\\\\frac{e^2}{6\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega)\\frac{\\omega_{an}^3}{\\omega_{an}^2-\\omega^2}(a|r_{1i}r_{1j}|n)(n|r_{2i}r_{2j}|a) + \\frac{e^2}{6\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega)\\frac{2\\varepsilon_{an}}{\\varepsilon_{an}^2-\\omega^2}\\langle a n| \\frac{\\omega^2}{5}(\\bm{r}_1\\bm{r}_2)^2+\\frac{\\omega^2}{10}r_1^2r_2^2 |n a\\rangle\\end{eqnarray}", "formula_003515": "\\begin{eqnarray}    \\hat{H}= \\hat{H}_0 + \\hat{V},\\end{eqnarray}", "formula_003516": "18.9075", "formula_003517": "2.3920\\times 10^{-14}", "formula_003518": "\\omega^5r^4_{12}/120", "formula_003520": "n_a", "formula_003519": "8.6\\times 10^{-4}", "formula_003522": "2s", "formula_003521": "\\begin{eqnarray}\\langle  a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2)r^2_{12}  | n a \\rangle = \\\\-\\langle  a n| ([\\bm{r}_1\\times\\bm{\\alpha}_1]\\cdot[\\bm{r}_2\\times\\bm{\\alpha}_2]) | n a \\rangle +\\,\\,\\,\\,\\,\\\\+\\langle  a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2) (r^2_1+r^2_2) | n a \\rangle\\\\- \\langle  a n| (\\bm{r}_1 \\bm{\\alpha}_2) (\\bm{r}_2 \\bm{\\alpha}_1) - (\\bm{\\alpha}_1 \\bm{\\alpha}_2) (\\bm{r}_1 \\bm{r}_2) | n a \\rangle.\\end{eqnarray}", "formula_003525": "\\epsilon_{ijk}", "formula_003524": "\\begin{eqnarray}I^{\\beta}_{n^{(-)} a}(r_{12}) \\approx \\frac{1}{m_e c^2}\\int\\limits^{\\infty}_0 d\\omega\\, n_{\\beta}(\\omega)\\left[\\omega r_{12}-\\frac{\\omega^3}{6}r_{12}^3+\\dots\\right],\\end{eqnarray}", "formula_003526": "6.74 \\times 10^{-6}", "formula_003523": "\\begin{eqnarray}\\frac{\\pi^3 e^2}{45\\beta^4}\\rightarrow 7.6425\\times 10^{-8} \\left[\\frac{T}{300\\, \\text{K}}\\right]^4 \\text{ Hz.}\\end{eqnarray}", "formula_003529": "\\,\\,\\,9.9\\times 10^{-13}", "formula_003528": "1.6572\\times 10^6", "formula_003533": "\\omega_{aa'}", "formula_003527": "r^2_{12}=r^2_1+r^2_2-2(\\bm{r}_1\\bm{ r}_2)", "formula_003534": "-2.26 \\times 10^{-4}", "formula_003532": "4.3354\\times 10^7", "formula_003530": "\\begin{eqnarray}    \\Delta E_{a}^{\\mathrm{BBRS}}= \\frac{2e^2}{3\\pi} \\text{P.V.}\\int\\limits_{0}^{\\infty} \\frac{d\\omega\\,\\omega ^3}{ e^{\\beta \\omega}-1 }    \\sum\\limits_{n \\neq a}   \\sum\\limits_{\\pm}    \\frac{\\left |  ( a|  \\bm{r} |n )  \\right|^2}{E_a-E_n \\pm \\omega}.\\end{eqnarray}", "formula_003531": "\\omega_{an} = E_a-E_n", "formula_003536": "ns(p)", "formula_003535": "39.6399", "formula_003537": "1.3018\\times 10^{-4}", "formula_003538": "\\Delta E_{n_a=100,l_a=0}^{\\rm dia} = 19.1", "formula_003539": "\\begin{eqnarray}J_{3}=\\frac{e^2}{6\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega) \\frac{2\\varepsilon_{an}}{\\varepsilon_{an}^2-\\omega^2}\\\\\\times\\langle a n| (\\bm{\\alpha}_1 \\bm{\\alpha}_2)(r_1^2+r_2^2) | n a\\rangle \\end{eqnarray}", "formula_003540": "-1.852 \\times 10^{-8}", "formula_003541": "-292.09", "formula_003542": "\\mathcal{E}_{0i}=\\mathcal{E}_{0i}(\\omega)", "formula_003543": "W^{\\mathrm{spon}}_{if}(\\mathrm{M1})+W^{\\mathrm{ind}}_{if}(\\mathrm{M1})", "formula_003544": "(\\bm{\\alpha}_1 \\bm{\\alpha}_2) (\\bm{r}_1\\bm{r}_2) = ([\\bm{r}_1\\times\\bm{\\alpha}_1]\\cdot[\\bm{r}_2\\times\\bm{\\alpha}_2]) + (\\bm{r}_1\\bm{\\alpha}_2)(\\bm{r}_2\\bm{\\alpha}_1)", "formula_003545": "\\sim T^6", "formula_003546": "\\begin{eqnarray}\\bm{\\mu} = \\frac{e}{2}[\\bm{r}\\times\\bm{\\alpha}].\\end{eqnarray}", "formula_003547": "\\begin{eqnarray}\\frac{1}{2}\\langle a n| \\alpha_{1i}\\alpha_{2i} r_{1j}r_{2j} + \\alpha_{1i}\\alpha_{2j} r_{1j}r_{2i} | n a \\rangle + \\frac{1}{2}\\langle a n| \\alpha_{1j}\\alpha_{2j} r_{1i}r_{2i} + \\alpha_{1j}\\alpha_{2i} r_{1i}r_{2j} | n a \\rangle = \\\\\\frac{1}{2}\\langle an | (\\alpha_{1i}r_{1j}+\\alpha_{1j}r_{1i})(\\alpha_{2i}r_{2j}+\\alpha_{2j}r_{2i}) | n a\\rangle\\equiv\\frac{1}{2}\\langle a| \\alpha_{1i}r_{1j}+\\alpha_{1j}r_{1i}| n\\rangle\\langle n|\\alpha_{2i}r_{2j}+\\alpha_{2j}r_{2i}| a\\rangle.\\end{eqnarray}", "formula_003550": "-1.5\\times 10^{-6}", "formula_003549": "k_{\\rm B}", "formula_003548": "\\bm{\\mathcal{E}}", "formula_003552": "-8.56 \\times 10^{-4}", "formula_003554": "\\begin{eqnarray}\\Delta E_a^{\\rm rem} =  \\frac{e^2}{6\\pi}\\sum\\limits_{n}\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega) \\omega_{an} ( a n | 2 (\\bm{r}_1 \\bm{r}_2) (r_1^2+r_2^2) | n a) - \\frac{e^2}{6\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\omega^3 n_\\beta(\\omega)\\omega_{an} (a n|(\\bm{r}_1 \\bm{r}_2)^2|n a) +\\qquad\\\\\\frac{2e^2}{3\\pi}\\sum\\limits_{nk}\\int\\limits_0^\\infty d\\omega\\, \\omega^5 n_\\beta(\\omega) \\frac{\\omega_{ak}}{\\omega_{an}^2-\\omega^2} ( a | r_i| k) ( k | r^2 | n) ( n | r_i | a) - \\frac{e^2}{15\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\omega^5n_\\beta(\\omega)\\frac{\\omega_{an} }{\\omega_{an}^2-\\omega^2}(a n|(\\bm{r}_1 \\bm{r}_2)(r_1^2+r_2^2)|n a).\\end{eqnarray}", "formula_003553": "-1.199 \\times 10^{-7}", "formula_003556": "\\,\\,\\,2.8\\times 10^{-12}", "formula_003551": "n>a", "formula_003555": "m_e=c=1", "formula_003557": "\\,\\,\\,2.2\\times 10^{-14}", "formula_003558": "7.79\\times 10^{-6}", "formula_003559": "2.53 \\times 10^{-5}", "formula_003560": "\\hbar", "formula_003561": "\\Delta \\nu^{\\rm BBRZ}\\equiv\\Delta E^{\\rm BBRZ}_{a}-\\Delta E^{\\rm BBRZ}_{a'}", "formula_003562": "2.2067\\times 10^1", "formula_003563": "\\,\\,\\,3.0\\times 10^{-12}", "formula_003564": "1.2448\\times 10^{-4}", "formula_003565": "\\Delta E_{a}^{\\mathrm{BBRS}} \\sim \\frac{(k_{B}T)^4}{\\alpha^3 m_{e}^3 Z^4}", "formula_003566": "310", "formula_003567": "\\Gamma_{na} = \\Gamma_n+\\Gamma_a", "formula_003568": "6.1801\\times 10^{-4}", "formula_003569": "\\Delta E_a^{\\mathcal{Q}}", "formula_003570": "\\bm{\\mu} = e[\\bm{r}\\times\\bm{\\alpha}]/2", "formula_003571": "\\alpha^5(k_{\\rm B}T)^4/(m Z^2)", "formula_003572": "\\Delta E_a^{\\rm BBRS}", "formula_003573": "\\Gamma^\\beta_{a}(\\mathrm{M1})", "formula_003574": "2.6180\\times 10^{-4}", "formula_003575": "1.9233\\times 10^{-6}", "formula_003579": "1.8952\\times 10^{-6}", "formula_003578": "-2.2\\times 10^{-4}", "formula_003580": "5s", "formula_003581": "<1", "formula_003576": "300", "formula_003577": "8.62 \\times 10^{-5}", "formula_003582": "\\begin{eqnarray}   \\langle f |\\hat{S}^{(2)}| i \\rangle= (-ie)^2 \\int d^4 x_1 d^4 x_2 \\overline{\\psi}_f(x_1) \\gamma^{\\mu} S(x_1,x_2)\\times\\\\       D_{\\mu \\nu}(x_1,x_2)\\gamma^{\\nu} \\psi_i(x_2),\\end{eqnarray}", "formula_003584": "2.1872\\times 10^6", "formula_003583": "8.3118\\times 10^{-2}", "formula_003585": "^+", "formula_003586": "-2.9\\times 10^{-4}", "formula_003587": "-1.21 \\times 10^{-4}", "formula_003588": "1.80\\times 10^{-6}", "formula_003589": "8.229", "formula_003590": "\\langle a| (\\bm{l} + 2 \\bm{s}) |n \\rangle", "formula_003591": "\\Delta E_a^{\\rm rem}", "formula_003592": "3s-2s", "formula_003593": "i=1,\\,2,\\,3", "formula_003594": "J_{3}", "formula_003595": "\\begin{eqnarray}\\langle  a| \\mu_i  | n \\rangle = \\frac{|e|}{2}\\epsilon_{ijk}\\langle  a|r_j\\alpha_k  | n \\rangle \\rightarrow\\\\\\begin{pmatrix}\\varphi_a^* & \\chi_a^*\\end{pmatrix} \\begin{pmatrix}0 & r_{j}\\sigma_{k}\\\\r_{j}\\sigma_{k} & 0\\end{pmatrix} \\begin{pmatrix}\\varphi_n \\\\ \\chi_n\\end{pmatrix} \\approx \\\\\\varphi_a^* \\left( r_{j}\\sigma_{k} \\frac{(\\bm{\\sigma} \\bm{p})}{2m_e} + \\frac{(\\bm{\\sigma} \\bm{p})}{2m_e} r_{j}\\sigma_{k}    \\right) \\varphi_n,\\end{eqnarray}", "formula_003596": "\\delta E_{a}\\rightarrow \\sum\\limits_{n\\neq a'}", "formula_003598": "-5.52 \\times 10^{-8}", "formula_003597": "\\varepsilon_a-\\varepsilon_{n^{(-)}}\\approx 2m_e c^2", "formula_003599": "\\begin{eqnarray} \\Delta E_a = \\frac{e^2}{\\pi} \\sum_n \\left[  \\frac{1-\\bm{\\alpha}_1\\bm{\\alpha}_2}{r_{12}} I^{\\beta}_{na}(r_{12})\\right]_{anna}.\\end{eqnarray}", "formula_003601": "\\Delta \\nu^{\\rm BBRZ}", "formula_003600": "\\omega_{aa'}\\leq k_{\\rm B} T", "formula_003605": "\\Delta \\nu^{\\mathrm{BBRZ}}", "formula_003604": "1.2256\\times 10^{-4}", "formula_003602": "3.1112\\times 10^{-3}", "formula_003603": "-1.304 \\times 10^{-17}", "formula_003607": "1.94\\times 10^{-5}", "formula_003606": "\\sum_\\pm", "formula_003609": "1.7466\\times 10^6", "formula_003608": "9.5352\\times 10^{-6}", "formula_003610": "[\\dots]_{anna}", "formula_003611": "\\begin{eqnarray}\\Delta E_a^{\\mathcal{Q}} = - \\frac{e^2}{90\\pi}\\sum\\limits_n\\int\\limits_0^\\infty d\\omega\\, \\frac{\\omega_{an} \\omega^5 n_\\beta}{\\omega_{an}^2-\\omega^2}(an| \\mathcal{Q}^{(1)}_{ij} \\mathcal{Q}^{(2)}_{ij} |na).\\,\\,\\,\\,\\,\\,\\,\\end{eqnarray}", "formula_003613": "\\omega_{ak} \\omega^2 / (\\omega_{an}^2 - \\omega^2)", "formula_003612": "-1.12 \\times 10^{-3}", "formula_003614": "2p", "formula_003616": "^1", "formula_003615": "J_{5}", "formula_003617": "^{133}", "formula_003618": "\\hat{H}_0", "formula_003619": "\\delta_a^\\Sigma=(\\Delta E_a^{\\rm wf}+\\Delta E_a^{\\rm rem})/\\Delta E_a^{\\rm BBRS}", "formula_003620": "\\mu=0,\\,1,\\,2,\\,3", "formula_003621": "\\psi_{a}(x)", "formula_003622": "1s_{1/2}", "formula_003623": "-1.19", "formula_003624": "(n| r_i p_j + r_j p_i |a) = \\mathrm{i}m_e(n| [H,r_i r_j] |a) = \\mathrm{i}m_e(E_n-E_a)(n| r_i r_j |a)", "formula_003626": "\\omega^3", "formula_003625": "n\\neq n_a", "formula_003627": "1.69 \\times 10^{-5}", "formula_003629": "-1.86\\times 10^{-5}", "formula_003628": "10^{-6}", "formula_003631": "5.8\\times 10^{-4}", "formula_003633": "\\gamma", "formula_003632": "F=2\\rightarrow F=1", "formula_003630": "-9.1\\times 10^{-6}", "formula_003634": "\\begin{eqnarray}|(\\tilde{a}|\\bm{r}|\\tilde{n})|^2\\approx \\left|(a|\\bm{r}|n) - \\frac{1}{8m_e^2c^2}(a|p^2\\bm{r}+\\bm{r}p^2|n)\\right|^2,\\end{eqnarray}", "formula_003635": "\\begin{eqnarray}        S(x_1,x_2)=\\frac{i}{2\\pi}\\int\\limits_{-\\infty}^{+\\infty}d\\Omega\\, e^{-i\\Omega(t_1-t_2)}            \\sum\\limits_{n}\\frac{\\psi_{n}(\\bm{r}_1)\\overline{\\psi}_{n}(\\bm{r}_2)}{\\Omega - \\varepsilon_{n} (1-i0)}\\,,\\end{eqnarray}", "formula_003637": "\\mathrm {I\\hspace{-0.6pt}I\\hspace{-0.6pt}I}", "formula_003636": "40 \\times 40\\ \\mu\\mathrm{m}^2", "formula_003638": "\\lambda_\\mathrm{E}", "formula_003639": "f \\sim 1", "formula_003640": "36", "formula_003641": "R", "formula_003642": "c_\\mathrm{S} / c_\\mathrm{D}", "formula_003643": "c_\\mathrm{S}", "formula_003644": "\\Gamma_0", "formula_003647": "\\mathrm{II}", "formula_003648": "\\times 10^{7}", "formula_003649": "C=-3.49 \\times 10^{-7}", "formula_003645": "\\Gamma(\\tau) = \\Gamma_{\\mathrm{st}} + \\frac{(\\Gamma_0 - \\Gamma_{\\mathrm{st}})}{1 + \\left(\\tau/\\tau^* \\right)^m},", "formula_003666": "\\delta n^{0} = 9.38 \\times 10^{-5}", "formula_003646": "t - t_{\\mathrm{\\hspace{1pt} EC}} < 0", "formula_003663": "c_\\mathrm{S}/c_\\mathrm{D} = 7.7", "formula_003658": "\\lambda", "formula_003665": "-3.53 \\pm 0.32", "formula_003653": "m \\sim 18", "formula_003652": "\\sigma_{\\rm \\hspace{1pt} E,EC}", "formula_003650": "\\mathrm {X\\hspace{-0.6pt}I}", "formula_003662": "x, y, z", "formula_003664": "c_\\mathrm{D} = 0.10~\\mathrm{M},\\ c_\\mathrm{S} = 0~\\mathrm{M}", "formula_003657": "{\\delta}", "formula_003656": "f", "formula_003655": "\\tau_\\mathrm{D}", "formula_003661": "\\sigma_{\\rm \\hspace{1pt} E}/\\sigma_{\\rm \\hspace{1pt} E,EC} = \\delta n/\\delta n_\\mathrm{\\hspace{1pt} EC}", "formula_003654": "n_{\\parallel}", "formula_003651": "R/R_{0}", "formula_003659": "\\mathrm {V\\hspace{-0.6pt}I\\hspace{-0.6pt}I}", "formula_003660": "I_4", "formula_003667": "\\dot{\\gamma} > 4~\\mathrm{s^{-1}}", "formula_003669": "I_3", "formula_003668": "\\sigma_{\\rm \\hspace{1pt} E}/\\sigma_{\\rm \\hspace{1pt} E,EC}", "formula_003671": "-3.89 \\pm 0.21", "formula_003670": "\\begin{align}{\\delta} &= \\frac{\\lambda}{2 \\pi} \\sin ^{ -1 }{ \\frac { \\sqrt { { ( { I }_{ 3 }-{ I }_{ 1 } ) }^{ 2 }+{ ( { I }_{ 2 }{-I }_{ 4 } ) }^{ 2 } } }{ ({ I }_{ 1 }+{ I }_{ 2 }+{ I }_{ 3 }+{ I }_{ 4 } )/2} },\\\\[6pt]\\varphi &=\\frac{1}{2}\\tan ^{ -1 }{ \\frac { { I }_{ 3 }-{ I }_{ 1 } }{{ I }_{ 2 }-{ I }_{ 4 }}},\\end{align}", "formula_003672": "t_\\mathrm{\\hspace{1pt} EC}", "formula_003674": "\\mathrm{Pe} = \\frac{\\tau_\\mathrm{D}}{\\tau_\\mathrm{\\Gamma}} = \\frac{R_{0} \\, \\Gamma}{4 D \\, \\eta_\\mathrm{E}},", "formula_003673": "-3.60 \\pm 0.35", "formula_003675": "\\mathrm{I}", "formula_003678": "^\\circ", "formula_003683": "\\delta n/\\delta n_\\mathrm{\\hspace{1pt} EC}", "formula_003689": "t_{\\mathrm{\\hspace{1pt} EC}}", "formula_003684": "0.5 \\leq c_\\mathrm{S}/c_\\mathrm{D} \\leq 2", "formula_003690": "8.9 \\times 10^{-4}", "formula_003680": "\\sigma_{\\rm \\hspace{1pt} E}", "formula_003685": "t_{\\mathrm{FE}}", "formula_003679": "1.5 \\times 10^6", "formula_003687": "N_\\mathrm{A} = 6.02 \\times 10^{23}", "formula_003677": "\\delta n = |n_{\\parallel}-n_{\\perp}| > 0", "formula_003682": "-2.04 \\pm 0.13", "formula_003686": "\\mathrm {X\\hspace{-0.6pt}V\\hspace{-0.6pt}I \\hspace{-0.6pt} I}", "formula_003676": "z", "formula_003688": "t - t_\\mathrm{\\hspace{1pt} EC} = 0", "formula_003681": "c_\\mathrm{S}/c_\\mathrm{D}=7.7", "formula_003691": "c_\\mathrm{V}=0.030", "formula_003692": "\\bm{\\sigma}", "formula_003695": "\\overline{n} = 1.342", "formula_003693": "c_\\mathrm{S} / c_\\mathrm{D} = 7.7", "formula_003696": "\\mathrm {I\\hspace{-0.6pt}I}", "formula_003694": "\\rm Pa", "formula_003697": "C", "formula_003698": "c_\\mathrm{S}/c_\\mathrm{D}", "formula_003699": "\\Gamma(\\tau)", "formula_003700": "\\sigma_{\\parallel} = 0", "formula_003703": "2R_0", "formula_003702": "\\Delta \\alpha^0", "formula_003701": "\\delta = \\int \\delta n\\,dz= C \\int (\\sigma_{\\parallel}-\\sigma_{\\perp})\\,dz,", "formula_003705": "\\delta_\\mathrm{\\hspace{1pt} EC}", "formula_003706": "20 \\times 20", "formula_003707": "C=-3.1 \\times 10^{-7}", "formula_003709": "2R", "formula_003711": "D", "formula_003714": "72~\\mathrm{mN/m}", "formula_003704": "C = f \\frac{2 \\pi(\\overline{n}^2+2)^2 N_\\mathrm{A} c_\\mathrm{V} \\Delta \\alpha^0}{9\\overline{n} m \\sigma_{\\rm E}}.", "formula_003708": "10^{-11}", "formula_003712": "\\delta n = |n_{\\parallel}-n_{\\perp}| = 0", "formula_003710": "\\delta n = n_{\\parallel} - n_{\\perp} < 0", "formula_003713": "c_\\mathrm{V} = 0.030", "formula_003715": "10^{-12}", "formula_003718": "\\bm{n} \\neq C \\bm{\\sigma}", "formula_003720": "\\tau", "formula_003717": "\\mu", "formula_003719": "10^{4}", "formula_003716": "\\mathrm {X\\hspace{-0.6pt}I\\hspace{-0.6pt}V}", "formula_003721": "\\mathrm{Pe} \\gg 1", "formula_003722": "t - t_{\\mathrm{\\hspace{1pt} EC}} \\geq t_{\\mathrm{\\hspace{1pt} FE}} - t_{\\mathrm{\\hspace{1pt} EC}}", "formula_003723": "-1.41 \\pm 0.05", "formula_003726": "m", "formula_003725": "n_{y}", "formula_003724": "\\sim c_\\mathrm{D}", "formula_003729": "\\Gamma", "formula_003727": "\\delta n_\\mathrm{\\hspace{1pt} EC}", "formula_003732": "\\mathrm I", "formula_003733": "\\Gamma_{\\mathrm{st}} = 36~\\mathrm{mN/m}", "formula_003731": "\\dot{\\varepsilon}", "formula_003736": "\\delta n", "formula_003735": "c_\\mathrm{V}/\\sigma_{\\rm E}", "formula_003738": "\\text{\\AA}^3", "formula_003728": "C=-3.67 \\times 10^{-7}", "formula_003730": "\\tau_\\mathrm{\\Gamma}", "formula_003734": "-3.63 \\pm 0.06", "formula_003737": "\\sigma_{\\parallel}", "formula_003739": "-3.04 \\pm 0.30", "formula_003741": "n_{x}", "formula_003742": "\\delta _\\mathrm{\\hspace{1pt} EC}=3.9", "formula_003743": "\\mathrm{Pa} \\cdot \\mathrm{s}", "formula_003744": "\\delta n^{0}", "formula_003747": "36~\\mathrm{mN/m}", "formula_003740": "R(t)", "formula_003745": "\\delta n = \\delta/2R", "formula_003746": "\\mathrm {X\\hspace{-0.6pt}I\\hspace{-0.6pt}I}", "formula_003748": "c_\\mathrm{D} = 0~\\mathrm{M},\\ c_\\mathrm{S} = 0.360~\\mathrm{M}", "formula_003750": "1 \\leq c_\\mathrm{S}/c_\\mathrm{D} \\leq 6", "formula_003751": "\\bm{n}", "formula_003749": "\\mathrm {X\\hspace{-0.6pt}V\\hspace{-0.6pt}I\\hspace{-0.6pt}I}", "formula_003752": "G", "formula_003753": "\\mathrm{V}", "formula_003754": "\\rm Pa^{-1}", "formula_003756": "x", "formula_003758": "c_\\mathrm{S} = 0.230", "formula_003759": "\\mathrm{V\\hspace{-0.6pt}I}", "formula_003760": "\\sigma_{\\rm E}/\\sigma_{\\rm E,EC}", "formula_003761": "0", "formula_003762": "x', y', z'", "formula_003755": "N_\\mathrm{A} c_\\mathrm{V} / m", "formula_003757": "-3.79 \\pm 0.38", "formula_003763": "t - t_\\mathrm{\\hspace{1pt} EC} < 0", "formula_003764": "C = \\frac{\\delta n}{\\sigma_{\\rm E}}.", "formula_003765": "t = 0", "formula_003771": "y", "formula_003766": "^+", "formula_003767": "t_{\\mathrm{FE}} - t_{\\mathrm{EC}}", "formula_003769": "t - t_{\\mathrm{\\hspace{1pt} EC}}", "formula_003770": "-2.98 \\pm 0.17", "formula_003768": "\\pi/2", "formula_003772": "\\mathrm {X\\hspace{-0.6pt}I\\hspace{-0.6pt}I\\hspace{-0.6pt}I}", "formula_003773": "\\tau^* =", "formula_003775": "|C|", "formula_003774": "\\eta_\\mathrm{E}", "formula_003776": "t", "formula_003777": "\\mathrm {X\\hspace{-0.6pt}V\\hspace{-0.6pt}I}", "formula_003780": "N_\\mathrm{A}", "formula_003778": "t_{\\mathrm{FE}}-t_{\\mathrm{EC}}", "formula_003779": "0.36 < \\dot{\\gamma} < 3.6~\\mathrm{s^{-1}}", "formula_003781": "^-", "formula_003782": "-3.78 \\pm 0.37", "formula_003783": "^2", "formula_003785": "\\sigma_{\\parallel}-\\sigma_{\\perp}", "formula_003784": "t_{\\mathrm{\\hspace{1pt} FE}}", "formula_003786": "I_2", "formula_003787": "\\mathrm V", "formula_003788": "\\sigma_{\\rm \\hspace{1pt} E} = \\eta_{\\rm \\hspace{1pt} E} \\dot{\\varepsilon} = \\frac{\\Gamma}{R(t)},", "formula_003789": "R/R_0", "formula_003791": "|\\Delta \\alpha^0| \\sim 14~\\text{\\AA}^3", "formula_003790": "t - t_{\\mathrm{EC}} = 0", "formula_003792": "dI(x,t)/dx \\sim 0", "formula_003793": "\\delta =\\delta n \\cdot 2R=C \\sigma_{\\rm E}\\cdot 2R.", "formula_003795": "\\mathrm {V\\hspace{-0.6pt}I\\hspace{-0.6pt}I \\hspace{-0.6pt} I}", "formula_003794": "\\bm{n} = C \\bm{\\sigma}", "formula_003797": "t - t_\\mathrm{\\hspace{1pt} EC} \\geq t_\\mathrm{\\hspace{1pt} FE} - t_\\mathrm{\\hspace{1pt} EC}", "formula_003796": "\\mathrm {V\\hspace{-0.6pt}I}", "formula_003798": "\\mathrm{Pe}", "formula_003799": "(t - t_{\\mathrm{EC}})/(t_{\\mathrm{FE}} - t_{\\mathrm{EC}})", "formula_003800": "0.1 < \\dot{\\varepsilon} < 30~\\mathrm{s^{-1}}", "formula_003801": "\\mathrm{IV}", "formula_003802": "\\Gamma_0 = 72~\\mathrm{mN/m}", "formula_003806": "\\mathrm{V\\hspace{-0.6pt}I \\hspace{-0.6pt} I}", "formula_003805": "\\lambda_{\\mathrm{E}}", "formula_003809": "\\mathrm {I}", "formula_003808": "0.5 < \\dot{\\varepsilon} < 4.5~\\mathrm{s^{-1}}", "formula_003810": "\\times", "formula_003804": "-0.58 \\pm 0.03", "formula_003803": "10^{0}", "formula_003807": "\\delta n = f \\delta n^{0} = \\frac{3\\cos^{2}(\\pi/2-\\varphi)-1}{2} \\delta n^{0},", "formula_003811": "\\mathrm {X\\hspace{-0.6pt}V}", "formula_003812": "c_\\mathrm{D}=0.005", "formula_003813": "|\\delta n / \\sigma_{\\rm \\hspace{1pt} E}|", "formula_003814": "t_\\mathrm{\\hspace{1pt} FE}-t_\\mathrm{\\hspace{1pt} EC}", "formula_003816": "\\overline{n}", "formula_003815": "C=-2.77 \\times 10^{-7}", "formula_003817": "\\mathrm{VI}", "formula_003818": "2\\times2", "formula_003819": "\\Gamma(\\tau^*) = (\\Gamma_0 + \\Gamma_{\\mathrm{st}})/2 = 54", "formula_003820": "-3.85 \\pm 0.36", "formula_003821": "\\varphi", "formula_003822": "|\\sigma_{\\parallel}-\\sigma_{\\perp}|", "formula_003824": "\\sigma_{\\perp}", "formula_003823": "\\tau^*", "formula_003825": "\\Delta \\hspace{0.4pt} t", "formula_003826": "\\mathrm {I\\hspace{-0.6pt}X}", "formula_003833": "c_\\mathrm{D}", "formula_003830": "10^7", "formula_003832": "^{-1}", "formula_003831": "|\\Delta \\alpha^0| \\sim 12~\\text{\\AA}^3", "formula_003827": "0.16 < \\dot{\\gamma} < 2.5~\\mathrm{s^{-1}}", "formula_003828": "\\mathrm {V}", "formula_003834": "c_\\mathrm{D}=0.030", "formula_003829": "0.001\\%", "formula_003835": "0.15 < \\dot{\\gamma} < 3.0~\\mathrm{s^{-1}}", "formula_003836": "c_\\mathrm{V}", "formula_003837": "\\cdot", "formula_003838": "10^2", "formula_003842": "c_\\mathrm{S}-c_\\mathrm{D}>0", "formula_003839": "-3.80 \\pm 0.42", "formula_003841": "\\mathrm {X}", "formula_003843": "R_{0}", "formula_003840": "\\ln{R(t)/R_{0}}", "formula_003845": "\\dot{\\varepsilon}(t) = -\\frac{2}{R(t)} \\frac{dR(t)}{dt}.", "formula_003848": "n_{\\perp}", "formula_003849": "\\sigma_{\\perp} = \\sigma_{\\rm E}", "formula_003844": "c_\\mathrm{S}=0.230", "formula_003846": "-3.67 \\pm 0.35", "formula_003847": "\\delta n = n_{\\parallel}-n_{\\perp} = C (\\sigma_{\\parallel}-\\sigma_{\\perp}),", "formula_003850": "-3.99 \\pm 0.16", "formula_003851": "-3.73 \\pm 0.27", "formula_003852": "c_\\mathrm{S}=0.038", "formula_003855": "\\mathrm {I\\hspace{-0.6pt}V}", "formula_003856": "\\delta", "formula_003857": "-3.53 \\pm 0.11", "formula_003853": "\\delta n^{0} = \\frac{2 \\pi(\\overline{n}^2+2)^2 N_\\mathrm{A} c_\\mathrm{V} \\Delta \\alpha^0}{9\\overline{n} m},", "formula_003854": "c_\\mathrm{D} = 0.030", "formula_003858": "\\eta_{\\rm \\hspace{1pt} E}", "formula_003859": "I_1", "formula_003861": "t-t_{\\mathrm{\\hspace{1pt} EC}}", "formula_003860": "\\frac{R(t)}{R_{0}} = \\left(\\frac{GR_{0}}{2\\Gamma}\\right)^{1/3}\\exp\\!\\left(-\\frac{t - t_{\\mathrm{\\hspace{1pt} EC}}}{3\\lambda_\\mathrm{E}}\\right),", "formula_003862": "0 \\leq t - t_\\mathrm{\\hspace{1pt} EC} < t_\\mathrm{\\hspace{1pt} FE} - t_\\mathrm{\\hspace{1pt} EC}", "formula_003865": "C_0\\approx -(2/3)(t/\\Delta \\epsilon_0)", "formula_003864": "n", "formula_003868": "|\\mathrm{SC}\\mu\\rangle", "formula_003870": "k=3.6~\\mathrm{meV}", "formula_003867": "|\\Psi\\rangle=|\\mathrm{SC}2\\rangle+C_0|\\mathrm{SC}0\\rangle+C_1|\\mathrm{SC1}\\rangle", "formula_003873": "j", "formula_003866": "; so", "formula_003869": "\\alpha'\\alpha\\beta\\alpha\\beta", "formula_003876": "\\beta\\alpha\\alpha\\beta", "formula_003879": "a_1=15/4", "formula_003877": "\\alpha'\\beta\\beta\\alpha\\alpha", "formula_003882": "\\hat{H}_{0}", "formula_003881": "k/j", "formula_003883": "J=21~\\mathrm{meV}", "formula_003885": "\\hat{n}_i", "formula_003884": "|\\mathrm{SC0}\\rangle", "formula_003886": "|\\mathrm{SC}2\\rangle", "formula_003887": "\\beta", "formula_003889": "|\\mathrm{SC1}\\rangle", "formula_003894": "z", "formula_003893": "k", "formula_003892": "|S,M_S\\rangle", "formula_003891": "|\\Psi\\rangle=\\sum_{\\mu}C_{\\mu}|\\mathrm{SC}\\mu\\rangle", "formula_003895": "7-8", "formula_003896": "S_{1,2}", "formula_003898": "|\\mathrm{SC}1\\rangle", "formula_003901": "C_1\\approx -(3/4)(t/\\Delta \\epsilon_0)", "formula_003902": "b_3=1/2", "formula_003900": "J", "formula_003903": "\\alpha'", "formula_003906": "C_0=\\frac{-a_0t}{a_1J - a_2 j + \\Delta \\epsilon_{0}};~~C_1=\\frac{-b_0 t}{b_1J - b_2j - b_3 k+\\Delta \\epsilon_{0}}", "formula_003907": "U_{ij}", "formula_003908": "\\displaystyle\\mathbf{H} = \\begin{bmatrix}10J - \\frac{99}{4}j +\\Delta \\epsilon_0 & -5/2k & 2/3 t\\\\-5/2k & 10J-\\frac{95}{4}j-\\frac{1}{2}k+\\Delta\\epsilon_0 & 3/4 t\\\\2/3t & 3/4 t & \\frac{25}{4}J - 22j\\end{bmatrix}", "formula_003905": "{}^{57}", "formula_003912": "\\begin{split}\\Delta\\epsilon_0 &= \\langle\\mathrm{SC}0|\\hat{H}_0|\\mathrm{SC0}\\rangle - \\langle\\mathrm{SC}2|\\hat{H}_0|\\mathrm{SC2}\\rangle\\\\                 &= \\langle\\mathrm{SC}1|\\hat{H}_0|\\mathrm{SC1}\\rangle - \\langle\\mathrm{SC}2|\\hat{H}_0|\\mathrm{SC2}\\rangle\\end{split}", "formula_003911": "\\mu", "formula_003916": "|\\mathrm{SC2}\\rangle", "formula_003918": "d", "formula_003919": "\\beta\\alpha\\beta\\alpha", "formula_003923": "3\\times 3", "formula_003921": "\\alpha\\alpha\\beta\\beta", "formula_003925": "\\beta\\beta\\alpha\\alpha", "formula_003929": "i", "formula_003930": "b_0=3/4", "formula_003931": "j=55~\\mathrm{meV}", "formula_003933": "C_1", "formula_003935": "C_0", "formula_003938": "\\alpha", "formula_003939": "\\Omega", "formula_003936": "|\\mathrm{SC}0\\rangle", "formula_003940": "\\{J_{ij}\\}", "formula_003941": "\\alpha\\beta\\alpha\\beta", "formula_003946": "J_{ij}", "formula_003947": "\\mathbf{I}", "formula_003948": "\\Delta \\epsilon_0", "formula_003950": "c(M_{12},M_{34})", "formula_003951": "t", "formula_003952": "\\mathbf{H}", "formula_003953": "C_{\\mu}", "formula_003957": "b_2=7/4", "formula_003958": "|\\Psi^{(0)}\\rangle=|\\mathrm{SC}2\\rangle", "formula_003960": "\\alpha'\\alpha\\alpha\\beta\\beta", "formula_003961": "b_1=15/4", "formula_003962": "|\\Psi^{(1)}\\rangle=C_1|\\mathrm{SC}1\\rangle", "formula_003964": "\\Delta\\epsilon_0>0", "formula_003965": "\\epsilon_{ij,\\sigma}", "formula_003966": "H_{\\mu\\nu}=\\langle \\mathrm{SC}\\mu|\\hat{H}|\\mathrm{SC}\\nu\\rangle", "formula_003967": "|\\Psi^{(0)}\\rangle", "formula_003968": "k\\ll j", "formula_003969": "\\begin{split}C_1&=-\\frac{H_{10}}{H_{11}-H_{00}}\\\\   &=\\frac{5}{2}\\times \\frac{k}{j}\\end{split}", "formula_003972": "\\alpha=\\beta\\approx 1/2", "formula_003973": "|\\Psi^{(0)}\\rangle=|\\mathrm{SC}0\\rangle", "formula_003975": "{}^{13}", "formula_003976": "a_0=2/3", "formula_003977": "|\\Psi^{(1)}\\rangle=\\sum_{\\mu}C_{\\mu}^{(1)}|\\mathrm{SC}\\mu\\rangle", "formula_003979": "|\\Psi\\rangle", "formula_003978": "\\alpha\\beta\\beta\\alpha", "formula_003980": "S_{3,4}", "formula_003984": "\\alpha'\\beta\\alpha\\beta\\alpha", "formula_003987": "\\hat{a}_i", "formula_003988": "{\\partial\\mathbf{X}}/{\\partial s}\\rvert_{s=0}=(0,1,0)", "formula_003989": "\\mathbf{x}", "formula_003990": "[ 0, 0.3 ]", "formula_003991": "\\eta\\approx0.2", "formula_003993": "T", "formula_003992": "\\mathbf{f}_{b}", "formula_003994": "f_{nat}", "formula_003995": "\\eta(x,y)", "formula_003996": "\\boldsymbol{Ca}", "formula_003997": "\\langle w' w' \\rangle", "formula_003998": "N_s", "formula_003999": "y = H", "formula_004000": "y=0.25H", "formula_004004": "U", "formula_004002": "\\mathbf{f}_b", "formula_004013": "\\beta", "formula_004025": "k", "formula_004001": "50\\%", "formula_004007": "y/H =1", "formula_004005": "\\tau_w", "formula_004014": "W", "formula_004009": "\\tau_4", "formula_004020": "w'", "formula_004003": "s", "formula_004018": "\\overline{\\tau_1}(0)", "formula_004010": "\\rho_f", "formula_004006": "\\lambda", "formula_004016": "\\overline{\\tau_w} = \\overline{\\tau_{tot}}(0) = \\overline{D}(0) + \\overline{\\tau_1}(0)", "formula_004012": "\\boldsymbol{\\Delta S/h}", "formula_004019": "\\Omega_x", "formula_004011": "h=0.65H", "formula_004017": "Ca =10", "formula_004008": "y=0.01H", "formula_004021": "\\tilde{\\omega_z}", "formula_004015": "\\tau_2", "formula_004023": "{\\partial^3\\mathbf{X}}/{\\partial s^3}\\rvert_{s=h}={\\partial^2\\mathbf{X}}/{\\partial s^2}\\rvert_{s=h}=\\mathbf{0}", "formula_004024": "f_{turb}\\approx0.5U_b/H", "formula_004026": "k = 0.5 (\\langle u' u' \\rangle + \\langle v' v' \\rangle + \\langle w' w' \\rangle)", "formula_004027": "\\tilde{\\omega_x}", "formula_004029": "z", "formula_004030": "\\mathbf{F}_{\\mathbf{\\rm{IBM}}}", "formula_004031": "\\tau_{tot}(y) =  - \\left \\langle {\\partial \\tilde{p}} /{\\partial x} \\right \\rangle (H - y)", "formula_004032": "\\pm 0.5 U_b", "formula_004028": "\\pm 0.2 U_b", "formula_004033": "u'>0", "formula_004035": "z=1.15H", "formula_004034": "v'>0", "formula_004036": "\\Delta S", "formula_004037": "\\Delta y/H = 0.002", "formula_004038": "\\overline{\\tau_2}=\\overline{\\tau_3}=\\overline{D}=0", "formula_004039": "61\\times15\\times15", "formula_004040": "z=0", "formula_004041": "5000", "formula_004043": "Ca=100", "formula_004042": "\\mathbf{u} (\\mathbf{x},t) = \\langle \\mathbf{u} \\rangle(y,z) = \\mathbf{U} (y,z) + \\mathbf{u'} (\\mathbf{x},t).", "formula_004044": "\\overline{\\tau_1}", "formula_004045": "U_{bl}", "formula_004046": "r \\approx 0.01 H", "formula_004047": "\\overline{\\tau_4}", "formula_004048": "\\mathbf{X}(s,t)", "formula_004049": "v'", "formula_004050": "\\langle u' u' \\rangle", "formula_004051": "D", "formula_004052": "\\mu", "formula_004053": "\\mathbf{U}", "formula_004055": "\\begin{eqnarray}   &\\nabla \\cdot \\mathbf{u} = 0,   \\\\   &\\displaystyle \\frac{\\partial \\mathbf{u}}{\\partial t} + \\nabla \\cdot (\\mathbf{u} \\mathbf{u}) = - \\frac{1}{\\rho_f} \\nabla p + \\frac{\\mu}{\\rho_f} \\nabla^2 \\mathbf{u} + \\mathbf{f}_s + \\mathbf{f}_b,   \\end{eqnarray}", "formula_004054": "{\\mathbf{F_{IBM}}= \\beta \\left(\\mathbf{u}_{\\mathbf{\\rm{IBM}}} - \\frac{\\partial \\mathbf{X}}{\\partial t}\\right),}", "formula_004056": "\\mathbf{u}(\\mathbf{x},t)", "formula_004068": "L_z/2", "formula_004057": "V", "formula_004059": "h = 0.25 H", "formula_004058": "y=0", "formula_004060": "a", "formula_004062": "U(y;z)", "formula_004061": "\\Gamma", "formula_004063": "\\mathbf{u'} (\\mathbf{x},t)", "formula_004065": "\\partial \\tilde{p} / \\partial x", "formula_004066": "d", "formula_004064": "N_x\\times N_y\\times N_z=1152\\times 384\\times 864", "formula_004067": "\\Delta \\tilde{\\rho}/(\\pi r^2 \\rho_f) = 0.27", "formula_004069": "\\overline{\\tau_2}=\\overline{\\tau_3}=0", "formula_004070": "-0.3 U_b", "formula_004071": "y = 0", "formula_004072": "\\mathbf{U}(y,z)=\\{U,V,W\\}", "formula_004073": "\\boldsymbol{h/(2r)}", "formula_004074": "|u'w'_{out}|/U_b^2", "formula_004075": "\\Delta \\tilde{\\rho}", "formula_004076": "v'<0", "formula_004077": "Ca = 10", "formula_004078": "\\tau_1", "formula_004079": "u'w'", "formula_004080": "Ca\\in\\{ 10, 100\\}", "formula_004082": "z/H\\gtrsim1", "formula_004084": "x", "formula_004083": "y/H \\in [0.0,0.3]", "formula_004081": "u'<0", "formula_004086": "L_z/4", "formula_004085": "\\frac{\\partial u}{\\partial t} + u \\frac{\\partial u}{\\partial x} + v \\frac{\\partial u}{\\partial y} + w \\frac{\\partial u}{\\partial z} = - \\frac{1}{\\rho_f}\\frac{\\partial p}{\\partial x} + \\frac{\\mu}{\\rho_f} \\left( \\frac{\\partial^2 u}{\\partial x^2} + \\frac{\\partial^2 u}{\\partial y^2} + \\frac{\\partial^2 u}{\\partial z^2}\\right) + f_{s,x} + f_{b,x}.", "formula_004087": "3.516", "formula_004088": "z=1.07H", "formula_004089": "\\Phi_v = \\frac{N_s V_s}{L_x L_y L_z} \\approx 2\\%", "formula_004090": "w'<0", "formula_004091": "f_{b,x}", "formula_004092": "10, 100", "formula_004093": "b", "formula_004094": "\\mathcal{F}(w/U_b)^2", "formula_004095": "\\overline{D}(0)", "formula_004096": "\\frac{\\mu}{\\rho_f} \\int_0^y \\frac{\\partial^2 \\langle u \\rangle}{\\partial y^2} dy = \\frac{\\mu}{\\rho_f} \\frac{\\partial \\langle u \\rangle}{\\partial y} \\bigg|_y - \\frac{\\mu}{\\rho_f} \\frac{\\partial \\langle u \\rangle}{\\partial y} \\bigg|_0 = \\frac{\\mu}{\\rho_f} \\frac{\\partial \\langle u \\rangle}{\\partial y} \\bigg|_y - \\frac{\\tau_w}{\\rho_f}.", "formula_004097": "u'w'_{out}", "formula_004098": "f_{nat}=\\left(\\frac{\\beta_1}{2\\pi h^2}\\right)\\sqrt{\\frac{\\gamma}{\\tilde{\\rho}_s}}.", "formula_004099": "\\mathbf{u'}", "formula_004100": "10", "formula_004101": "\\langle \\mathbf{u'}  \\mathbf{u'} \\rangle", "formula_004102": "\\mathbf{X}\\rvert_{s=0}=\\mathbf{X_0}", "formula_004103": "|u'v'|/U_b^2", "formula_004104": "p(\\mathbf{x},t)", "formula_004105": "f_{nat}\\approx f_{turb}", "formula_004106": "U_b", "formula_004107": "\\Phi_v", "formula_004109": "Ca", "formula_004108": "y", "formula_004110": "u'v'", "formula_004112": "{\\mathbf{F_{IBM}}}", "formula_004111": "\\Delta S / H \\approx 0.044", "formula_004114": "\\boldsymbol{Re_b}", "formula_004113": "U_{bl}(z) = \\int_0^H U(y,z) dy,", "formula_004115": "u'", "formula_004116": "Ca = 100", "formula_004117": "\\boldsymbol{\\Delta \\tilde{\\rho}/(\\pi r^2 \\rho_f)}", "formula_004118": "t", "formula_004120": "\\langle v' v' \\rangle", "formula_004121": "\\partial_x \\eta", "formula_004123": "\\omega'", "formula_004122": "15\\times61\\times15", "formula_004128": "1 U_b/H", "formula_004132": "0\\%", "formula_004124": "\\overline{D}", "formula_004126": "\\langle v'w' \\rangle", "formula_004134": "Re_b=7070", "formula_004127": "\\langle \\cdot \\rangle", "formula_004136": "\\tau_3", "formula_004125": "Ca =100", "formula_004139": "Ca=10", "formula_004129": "0.3 U_b", "formula_004142": "16\\%", "formula_004133": "\\mathbf{f}_s", "formula_004130": "s=h", "formula_004141": "\\langle \\eta \\rangle(z)", "formula_004140": "- f_{s,x}", "formula_004138": "\\partial\\mathbf{X}/\\partial t=\\mathbf{u}[\\mathbf{X}(s,t),t]", "formula_004131": "\\langle u'w' \\rangle", "formula_004135": "\\mathbf{u}", "formula_004143": "Ca = \\rho_f r h^3 U_b^2/\\gamma \\in \\{10, 100\\}", "formula_004137": "\\langle u'v' \\rangle", "formula_004146": "h=0", "formula_004144": "\\overline{\\tau_3}", "formula_004145": "\\partial_z \\eta", "formula_004147": "\\lambda = 2 r h / \\Delta S^2 \\gg 0.1", "formula_004149": "\\mathbf{f}_s=\\int_\\Gamma \\mathbf{F}_{\\mathbf{\\rm{IBM}}}(s,t)\\delta(\\mathbf{x}-\\mathbf{X}(s,t))ds,", "formula_004148": "\\mathbf{u}_{\\mathbf{\\rm{IBM}}}", "formula_004159": "100\\%", "formula_004151": "w'_{out}", "formula_004158": "\\overline{\\tau_{tot}}", "formula_004156": "L_x \\times L_y \\times L_z = 2\\pi H \\times H \\times1.5\\pi H", "formula_004153": "\\beta_1", "formula_004150": "T\\rvert_{s=h}=0", "formula_004160": "32", "formula_004163": "V_s = \\pi r^2 h", "formula_004161": "\\tilde{\\boldsymbol{\\omega'}}", "formula_004164": "\\eta=0.25", "formula_004152": "50 H/U_b", "formula_004157": "\\tilde{\\rho}_s = \\rho_f / (\\pi r^2) + \\Delta \\tilde{\\rho}", "formula_004162": "\\overline{\\tau_2}", "formula_004155": "[ 0, 0.2 ]", "formula_004165": "4\\%", "formula_004167": "\\gamma", "formula_004166": "\\Delta y/H = 0.004", "formula_004154": "\\begin{eqnarray}    &\\Delta \\Tilde{\\rho} \\displaystyle \\frac{\\partial^2 \\mathbf{X}}{\\partial t^2} =     \\displaystyle \\frac{\\partial}{\\partial s}\\left(T\\displaystyle \\frac{\\partial \\mathbf{X}}{\\partial s}\\right) - \\gamma \\displaystyle \\frac{\\partial^4 \\mathbf{X}}{\\partial s^4} - {\\mathbf{F_{IBM}}},     \\\\    &\\displaystyle \\frac{\\partial \\mathbf{X}}{\\partial s} \\cdot \\displaystyle \\frac{\\partial \\mathbf{X}}{\\partial s} = 1,    \\end{eqnarray}", "formula_004168": "f_{turb}", "formula_004170": "aX_m-b=0", "formula_004169": "w'>0", "formula_004172": "\\kappa_i = 5E/6", "formula_004173": "\\sigma_1 = \\sigma_3 + C_0\\sqrt{m_i\\frac{\\sigma_3}C_0+1},", "formula_004174": "\\sigma_m", "formula_004175": "\\kappa_i", "formula_004176": "h_c/h_0=2^2", "formula_004178": "\\VEC{\\tau}\\otimes\\VEC{\\eta}", "formula_004180": "\\kappa(\\VEC{\\varepsilon})>0", "formula_004179": "\\VEC{\\sigma} = -p_0\\mathbf{I}", "formula_004182": "E", "formula_004183": "h_c/h_0=2^1", "formula_004185": "{\\VEC{X}}", "formula_004186": "1/9", "formula_004188": "\\dot{\\lambda}", "formula_004181": "\\begin{align}\\begin{split}\\psi(\\VEC{\\varepsilon},\\VEC{p}) \\coloneqq&\\ \\frac{1}{2} (\\VEC{\\varepsilon}-\\VEC{p}):\\MAT{E}(\\VEC{\\varepsilon}):(\\VEC{\\varepsilon}-\\VEC{p}) \\\\ =&\\ \\frac{\\kappa(\\VEC{\\varepsilon})}{2}[\\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon}-\\VEC{p})]^2 + \\mu(\\VEC{\\varepsilon})\\, (\\VEC{\\varepsilon}^D-\\VEC{p}^D):(\\VEC{\\varepsilon}^D-\\VEC{p}^D).\\end{split}\\end{align}", "formula_004187": "\\VEC{\\varepsilon} = (\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}/3)\\mathbf{I}", "formula_004190": "(\\kappa_i,\\beta_m)", "formula_004189": "\\VEC{\\sigma} \\in \\MAT{R}^{3\\times 3}_{\\text{sym}}", "formula_004192": "\\beta^D \\geq 0", "formula_004193": "b(a- \\beta_m b)/a^2", "formula_004196": "\\sigma_3", "formula_004195": "\\| \\VEC{\\varepsilon}^D \\|", "formula_004197": "\\Delta\\lambda", "formula_004199": "\\frac{\\partial f(\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}, \\| \\VEC{\\varepsilon}^D \\|)}{\\partial \\VEC{\\varepsilon}}=\\frac{\\partial f}{\\partial \\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}}\\frac{\\partial \\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}}{\\partial \\VEC{\\varepsilon}} +  \\frac{\\partial f}{\\partial \\| \\VEC{\\varepsilon}^D \\|}\\frac{\\partial \\| \\VEC{\\varepsilon}^D \\|}{\\partial \\VEC{\\varepsilon}} = \\frac{\\partial f}{\\partial \\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}}\\mathbf{I} +  \\frac{\\partial f}{\\partial \\| \\VEC{\\varepsilon}^D \\|}\\frac{\\VEC{\\varepsilon}^D}{\\| \\VEC{\\varepsilon}^D \\|}.", "formula_004200": "\\MAT{K}_{\\VEC{X}}", "formula_004201": "{\\cal D(\\VEC{p}=\\text{const})}=0", "formula_004202": "a=1/9", "formula_004203": "\\kappa", "formula_004204": "\\VEC{\\varepsilon}^D=\\VEC{0}", "formula_004205": "\\psi(\\VEC{\\varepsilon},\\VEC{p}) = \\frac{1}{2} (\\VEC{\\varepsilon}-\\VEC{p}) :\\MAT{E}:(\\VEC{\\varepsilon}-\\VEC{p}).", "formula_004208": "b=E/3000", "formula_004207": "\\bar\\sigma \\rightarrow -\\infty", "formula_004210": "f_{\\VEC{X}}", "formula_004211": "q = \\sigma_x - \\sigma_z", "formula_004206": "\\begin{aligned}\\sigma_m &=\\cfrac{\\kappa_i}{1+2\\kappa_i\\beta_m\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}} \\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon} -\\VEC{p}) \\\\&-\\cfrac{\\kappa_i^2\\beta_m}{(1+2\\kappa_i\\beta_m\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon} )^2} [\\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon} -\\VEC{p})]^2 \\\\\\end{aligned}", "formula_004209": "\\sigma_m = \\displaystyle\\frac{1}{3} (\\sigma_z - 2 p_0),\\quad    \\lVert\\VEC{\\sigma}^D\\rVert = \\sqrt{\\frac{2}{3}}\\, \\lvert q \\rvert,", "formula_004212": "z", "formula_004215": "\\tau_{ij}\\eta_{kl}", "formula_004216": "q \\coloneqq p_0 + \\sigma_z", "formula_004217": "\\frac{1}{\\sqrt{6}}\\left\\lVert\\VEC{\\sigma}^D\\right\\rVert = \\frac{-(a-2\\beta_mb) + a\\sqrt{1-4\\sigma_m\\beta_m}}{2\\beta_m}.", "formula_004218": "45\\degree", "formula_004219": "m_i", "formula_004221": "p_0", "formula_004222": "E/3000", "formula_004224": "\\beta_m \\geq 0", "formula_004225": "a-2\\beta_mb\\geq 0", "formula_004228": "u(t)", "formula_004229": "\\kappa(\\VEC{\\varepsilon})", "formula_004226": "\\frac{3}{2C_0} \\left\\lVert\\VEC{\\sigma}^D\\right\\rVert^2 + \\frac{\\sqrt{3}m_i}{2\\sqrt{2}} \\left\\lVert\\VEC{\\sigma}^D\\right\\rVert + m_i\\sigma_m - C_0 \\leq 0.", "formula_004230": "\\text{Tr}\\hspace{0.5mm} \\VEC{\\varepsilon}", "formula_004233": "\\cal D", "formula_004234": "t_n", "formula_004235": "\\text{Tr}\\hspace{0.5mm}", "formula_004236": "120/E", "formula_004238": "n=0,1,2,3", "formula_004239": "\\VEC{X} = \\cfrac{\\kappa_i}{1+2\\kappa_i\\beta_m\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}} \\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon} -\\VEC{p})\\mathbf{I}+2\\mu_i(\\VEC{\\varepsilon}^D-\\VEC{p}^D)", "formula_004240": "\\MAT{K}:=\\MAT{I} - \\MAT{J}", "formula_004242": "\\VEC{\\tau}^D", "formula_004243": "\\displaystyle\\frac{1}{\\mu(\\varepsilon)^2}\\displaystyle\\frac{\\partial\\mu(\\VEC{\\varepsilon})}{\\partial \\VEC{\\varepsilon}} = -\\frac{\\partial\\mu^{-1}(\\VEC{\\varepsilon})}{\\partial \\VEC{\\varepsilon}}", "formula_004245": "b\\geq 0", "formula_004246": "\\Delta \\lambda", "formula_004247": "\\mu", "formula_004248": "\\kappa_i/\\mu_i", "formula_004249": "t_{n}", "formula_004251": "F", "formula_004252": "\\VEC{p}_n", "formula_004253": "\\VEC{\\tau} : \\VEC{\\eta}\\coloneqq  \\text{Tr}\\hspace{0.5mm} (\\VEC{\\tau} \\cdot \\VEC{\\eta}) = \\tau_{ij}\\eta_{ji}", "formula_004254": "a", "formula_004255": "\\VEC{\\tau}", "formula_004257": "\\VEC{\\tau} = \\tau_m\\, \\mathbf{I} + \\VEC{\\tau}^D, \\quad \\text{with} \\ \\tau_m = \\frac{\\text{Tr}\\hspace{0.5mm}\\VEC{\\tau}}{3},", "formula_004258": "\\| \\VEC{\\varepsilon}^D \\|^2", "formula_004259": "\\mu_i = 5E/13", "formula_004260": "\\VEC{\\sigma} = \\cfrac{\\kappa_i}{1+2\\kappa_i\\beta_m\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}} \\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon} -\\VEC{p})\\left(1-\\cfrac{\\kappa_i\\beta_m}{1+2\\kappa_i\\beta_m\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon} }\\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon} -\\VEC{p})\\right)\\mathbf{I}+2\\mu_i(\\VEC{\\varepsilon}^D-\\VEC{p}^D)", "formula_004262": "f_{\\VEC{X}}(\\VEC{X}^{el})>0", "formula_004263": "\\varepsilon_0 = -\\frac{1}{2\\kappa_i\\beta_m}.", "formula_004265": "10\\degree", "formula_004266": "f(\\VEC{\\varepsilon})", "formula_004267": "\\bar{\\sigma} > 0", "formula_004268": "\\VEC{\\sigma}", "formula_004269": "\\beta_m", "formula_004270": "(\\VEC{e}_x,\\VEC{e}_y,\\VEC{e}_z)", "formula_004272": "b=0", "formula_004274": "\\psi(\\VEC{\\varepsilon},\\VEC{p})", "formula_004276": "\\mu(\\VEC{\\varepsilon})", "formula_004277": "\\beta^D>0", "formula_004278": "\\VEC{\\sigma} = \\VEC{X} + \\frac{1}{2\\kappa(\\VEC{\\varepsilon})^2}\\frac{\\partial\\kappa(\\VEC{\\varepsilon})}{\\partial \\VEC{\\varepsilon}}X_m^2 + \\frac{1}{4\\mu(\\VEC{\\varepsilon})^2}\\frac{\\partial\\mu(\\VEC{\\varepsilon})}{\\partial \\VEC{\\varepsilon}} \\VEC{X}^D :\\VEC{X}^D.", "formula_004279": "a=1", "formula_004280": "{\\cal D} =\\VEC{\\sigma} : \\dot{\\VEC{\\varepsilon}} - \\dot\\psi (\\VEC{\\varepsilon},\\VEC{p})\\ge 0,", "formula_004281": "\\displaystyle\\frac{1}{\\kappa(\\VEC{\\varepsilon})^2}\\frac{\\partial\\kappa(\\VEC{\\varepsilon})}{\\partial \\VEC{\\varepsilon}} = -\\frac{\\partial\\kappa^{-1}(\\VEC{\\varepsilon})}{\\partial \\VEC{\\varepsilon}}", "formula_004282": "f_{\\VEC{X}}(\\VEC{X}^{el})\\leq 0", "formula_004283": "\\begin{align}\\begin{split}\\VEC{\\sigma} &= \\frac{\\partial\\psi (\\VEC{\\varepsilon},\\VEC{p})}{\\partial \\VEC{\\varepsilon}} \\\\&= \\MAT{E}(\\VEC{\\varepsilon}):(\\VEC{\\varepsilon} - \\VEC{p}) + \\frac{1}{2} \\frac{\\partial\\kappa(\\VEC{\\varepsilon})}{\\partial \\VEC{\\varepsilon}}[\\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon}-\\VEC{p})]^2 + \\frac{\\partial\\mu(\\VEC{\\varepsilon})}{\\partial \\VEC{\\varepsilon}}(\\VEC{\\varepsilon}^D-\\VEC{p}^D) : (\\VEC{\\varepsilon}^D-\\VEC{p}^D),\\end{split}\\end{align}", "formula_004284": "1\\%", "formula_004285": "\\VEC{\\sigma} = \\VEC{X} - \\beta_mX_m^2\\mathbf{I}.", "formula_004286": "\\MAT{A}", "formula_004287": "x", "formula_004289": "(X_m,\\VEC{X}^D)=(b/a, \\VEC{0})", "formula_004290": "\\bar\\sigma", "formula_004291": "\\Delta\\lambda < \\sqrt{6}\\|\\VEC{X}^{D,el}\\|/(2\\mu)", "formula_004292": "\\MAT{E}", "formula_004293": "\\VEC{\\eta}", "formula_004295": "0.3", "formula_004297": "C_1, C_2", "formula_004298": "b", "formula_004299": "\\coloneqq", "formula_004300": "C_0", "formula_004301": "\\varepsilon_0", "formula_004302": "\\VEC{\\sigma} = \\begin{pmatrix}    -p_0 & 0 & 0 \\\\     0 & -p_0 & 0 \\\\    0 & 0 & \\sigma_z    \\end{pmatrix},\\quad    \\VEC{\\varepsilon} = \\begin{pmatrix}    \\varepsilon_x & 0 & 0 \\\\    0 & \\varepsilon_x & 0 \\\\    0 & 0 & \\varepsilon_z    \\end{pmatrix}.", "formula_004303": "\\kappa(\\VEC{\\varepsilon}) = \\frac{\\kappa_i}{1+2\\kappa_i \\beta_m\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}},\\quad \\mu(\\VEC{\\varepsilon}) = \\frac{\\mu_i}{1+4\\mu_i\\beta^D\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}},", "formula_004305": "(\\sigma_1 - \\sigma_3)^2 \\sim \\sigma_1 + \\sigma_3,", "formula_004304": "\\varepsilon_z", "formula_004306": "\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}", "formula_004307": "\\MAT{I}", "formula_004308": "\\sigma_1", "formula_004309": "h_c/h_0=2^3", "formula_004311": "{\\cal D} = -\\frac{\\partial \\psi}{\\partial \\VEC{p}} :\\dot{\\VEC{p}} = \\VEC{X} : \\dot{\\VEC{p}}\\ge 0.", "formula_004312": "\\mathbf{I}", "formula_004313": "y", "formula_004314": "-p_0", "formula_004315": "(\\MAT{A}:\\VEC{\\tau})_{ij}\\coloneqq A_{ijkl}\\tau_{kl}", "formula_004316": "q", "formula_004317": "\\frac{1}{\\sqrt{6}}\\left\\lVert\\VEC{\\sigma}^D\\right\\rVert = b-a\\sigma_m.", "formula_004318": "\\VEC{X} =-\\frac{\\partial\\psi (\\VEC{\\varepsilon},\\VEC{p})}{\\partial \\VEC{p}}= \\MAT{E}(\\VEC{\\varepsilon}):(\\VEC{\\varepsilon} - \\VEC{p}) = \\kappa(\\VEC{\\varepsilon})\\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon}-\\VEC{p})\\mathbf{I} + 2\\mu(\\VEC{\\varepsilon})\\, (\\VEC{\\varepsilon}^D-\\VEC{p}^D).", "formula_004319": "f_{\\VEC{X}}(\\VEC{X}) = 0", "formula_004320": "\\VEC{\\sigma}  = \\partial\\psi/\\partial\\VEC{\\varepsilon}", "formula_004321": "\\VEC{\\varepsilon}", "formula_004322": "t_{n+1}", "formula_004323": "\\sigma_2", "formula_004324": "h_c/h_0=2^0", "formula_004326": "\\VEC{X} = X_m\\mathbf{I} + \\VEC{X}^D,\\quad\\text{with}\\quad \\begin{cases}X_m =\\cfrac{\\kappa_i}{1+2\\kappa_i \\beta_m \\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}}\\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon} -\\VEC{p})  \\vspace{4mm} \\\\\\VEC{X}^D = 2\\mu_i (\\VEC{\\varepsilon}^D-\\VEC{p}^D).\\end{cases}", "formula_004328": "\\VEC{\\varepsilon}^D = \\VEC{0}", "formula_004329": "\\MAT{J}:=\\mathbf{I}\\otimes\\mathbf{I}/3", "formula_004330": "\\beta_m=120/E", "formula_004331": "\\VEC{u}", "formula_004332": "{\\cal D}\\geq 0", "formula_004333": "\\dot{\\lambda} \\geq 0,\\quad f_{\\VEC{X}}(\\VEC{X}) \\leq 0, \\quad \\dot{\\lambda}f_{\\VEC{X}}(\\VEC{X})=0.", "formula_004334": "\\VEC{X}", "formula_004335": "\\mu_i", "formula_004336": "\\bar{\\sigma} = \\frac{1}{4 \\beta_m} \\left(1 - \\frac{1}{(1+2\\kappa_i\\beta_m \\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon})^2} \\right),", "formula_004337": "\\VEC{X} = \\VEC{X}^{el} := \\kappa(\\VEC{\\varepsilon})\\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon}-\\VEC{p}_n)\\mathbf{I} + 2\\mu(\\VEC{\\varepsilon}^D-\\VEC{p}_n^D).", "formula_004338": "\\VEC{X} \\in \\MAT{R}^{3\\times 3}_{\\text{sym}}", "formula_004340": "\\beta^D=0", "formula_004341": "\\VEC{\\sigma} = \\sigma_m\\mathbf{I} + \\VEC{\\sigma}^D,\\quad\\text{with}\\quad \\begin{cases}\\begin{aligned}\\sigma_m &=\\cfrac{\\kappa_i}{1+2\\kappa_i\\beta_m\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}} \\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon} -\\VEC{p}) \\\\&-\\cfrac{\\kappa_i^2\\beta_m}{(1+2\\kappa_i\\beta_m\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon} )^2} [\\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon} -\\VEC{p})]^2 \\\\\\end{aligned}\\vspace{4mm} \\\\\\VEC{\\sigma}^D = 2\\mu_i(\\VEC{\\varepsilon}^D-\\VEC{p}^D),\\end{cases}", "formula_004344": "\\beta^D> 0", "formula_004343": "f(\\VEC{\\varepsilon}) = C_1 + C_2\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}", "formula_004345": "\\VEC{\\sigma} = \\bar{\\sigma} \\mathbf{I}", "formula_004347": "\\beta_m=0", "formula_004349": "n+1", "formula_004350": "\\left\\lVert \\VEC{\\tau}\\right\\rVert \\coloneqq \\sqrt{\\VEC{\\tau} : \\VEC{\\tau}}", "formula_004351": "\\VEC{p}", "formula_004352": "\\nu", "formula_004353": "\\VEC{\\tau},\\VEC{\\eta}", "formula_004354": "f(\\VEC{\\varepsilon})\\in\\mathbb{R}", "formula_004356": "h_c = h_0/2^n", "formula_004357": "\\varepsilon_{eq}=\\sqrt{2/3}\\|\\VEC{\\varepsilon}^D\\|", "formula_004359": "\\VEC{\\sigma} = \\frac{\\partial \\psi}{\\partial\\VEC{\\varepsilon}}= \\MAT{E}:(\\VEC{\\varepsilon} - \\VEC{p}).", "formula_004360": "\\tau_m", "formula_004364": "\\VEC{n}^D := \\VEC{X}^D/(\\sqrt{6}\\|\\VEC{X}^D\\|) = \\VEC{X}^{D,el}/(\\sqrt{6}\\|\\VEC{X}^{D,el}\\|)", "formula_004365": "\\bar{\\sigma} < 0", "formula_004366": "\\|\\VEC{X}^{D}\\|>0", "formula_004367": "\\beta_m > 0", "formula_004368": "\\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}=0", "formula_004369": "53\\degree-56\\degree", "formula_004371": "100", "formula_004372": "\\psi(\\VEC{\\varepsilon},\\VEC{p}) := \\cfrac{1}{2}\\cfrac{\\kappa_i}{1+2\\kappa_i \\beta_m \\text{Tr}\\hspace{0.5mm}\\VEC{\\varepsilon}}[\\text{Tr}\\hspace{0.5mm}(\\VEC{\\varepsilon} -\\VEC{p})]^2+\\mu_i(\\VEC{\\varepsilon}^D-\\VEC{p}^D):(\\VEC{\\varepsilon}^D-\\VEC{p}^D)", "formula_004373": "\\VEC{p} = \\VEC{0}", "formula_004374": "(\\sigma_m,\\VEC{\\sigma}^D)=(b(a-\\beta_m b)/a^2, \\VEC{0})", "formula_004376": "\\tau_{rise}", "formula_004378": "A_{light}", "formula_004377": "^{232}", "formula_004379": "^{-4}", "formula_004380": "45\\times45\\times45", "formula_004381": "E", "formula_004382": "G_{CROSS}", "formula_004384": "2 \\nu 2 \\beta", "formula_004383": "A_{signal}", "formula_004385": "^{82}", "formula_004387": "_2$$^{100\\mathrm{depl}}", "formula_004386": "\\oslash", "formula_004388": "\\beta", "formula_004389": "_{2615}", "formula_004390": "BI = P \\cdot (1 - r) \\cdot \\frac{A^2}{m} \\cdot Time \\cdot \\Delta t_{r = 100\\%},", "formula_004391": "2\\nu\\beta\\beta", "formula_004392": "V_{NTL}", "formula_004393": "BI \\sim 0.5 \\times 10^{-4}", "formula_004394": "N_{rej}", "formula_004395": "^\\circ", "formula_004396": "r", "formula_004398": ">", "formula_004397": "\\Delta", "formula_004399": "O", "formula_004400": "0\\nu \\beta \\beta", "formula_004401": "LY_{far}", "formula_004403": "Q", "formula_004402": "\\sim", "formula_004404": "10^{-5}", "formula_004405": "_{r = 100\\%}", "formula_004408": "E_0", "formula_004406": "^{10}", "formula_004407": "E_{NTL} = E_0 \\cdot (1 + e \\cdot V_{NTL} \\cdot \\eta / \\epsilon ) = E_0 \\cdot G_{NTL},", "formula_004409": "f_{scale} = \\frac{G_{NTL}}{G_{CROSS}} = \\frac{1}{\\xi_{CROSS}} - \\frac{1 - \\xi_{CROSS}}{\\xi_{CROSS} \\cdot G_{CROSS}},", "formula_004410": "^{116}", "formula_004411": "2\\beta", "formula_004412": "0.5\\times10^{-4}", "formula_004413": "^{100}", "formula_004414": "\\approx", "formula_004415": "45\\times45\\times0.3", "formula_004418": "\\mu", "formula_004416": "e \\cdot V_{NTL} \\cdot \\eta / \\epsilon", "formula_004417": "LY_{close}", "formula_004419": "^{130}", "formula_004420": "e \\cdot V_{NTL}", "formula_004421": "LY_{CROSS}", "formula_004422": "LY_{\\gamma(\\beta)}", "formula_004423": "f_{scale}", "formula_004424": "_2", "formula_004425": "\\sigma_{baseline}", "formula_004426": "G_{CROSS} = (1 - \\xi_{CROSS}) + \\xi_{CROSS} \\cdot G_{NTL},", "formula_004427": "<", "formula_004428": "E_{NTL}", "formula_004429": "LY", "formula_004430": "^{208}", "formula_004431": "_{baseline}", "formula_004432": "_{2}", "formula_004434": "\\xi_{CROSS}", "formula_004435": "G_{CUPID}", "formula_004433": "\\eta", "formula_004436": "Time", "formula_004438": "\\alpha", "formula_004437": "0 \\nu 2 \\beta", "formula_004439": "\\Omega", "formula_004440": "_4", "formula_004441": "A", "formula_004442": "\\epsilon", "formula_004443": "^{210}", "formula_004444": "R_{NTD}", "formula_004445": "^3", "formula_004446": "\\pm", "formula_004447": "45 \\times 45 \\times 0.3", "formula_004448": "^{-3}", "formula_004449": "\\xi_{CUPID}", "formula_004451": "I_{NTD}", "formula_004450": "N_{inj}", "formula_004452": "\\tau_{decay}", "formula_004453": "G_{NTL}^{eff}", "formula_004454": "^{100}\\mathrm{Mo}", "formula_004455": "P", "formula_004458": "BI", "formula_004456": "f_{scale} = \\left(\\frac{1 - \\xi_{CUPID}}{G_{CROSS}} + \\xi_{CUPID} \\cdot \\left(\\frac{1}{\\xi_{CROSS}} - \\frac{1 - \\xi_{CROSS}}{\\xi_{CROSS} \\cdot G_{CROSS}}\\right)\\right) \\cdot \\frac{LY_{CUPID}}{LY_{CROSS}}.", "formula_004457": "\\times", "formula_004459": "\\nu", "formula_004460": "G_{NTL}", "formula_004462": "E_0 / \\epsilon", "formula_004461": "f_{scale} = \\frac{G_{CUPID}}{G_{CROSS}} \\cdot \\frac{LY_{CUPID}}{LY_{CROSS}} ,", "formula_004463": "_{r = 100\\%} \\approx \\tau_{rise}", "formula_004464": "r = \\frac{N_{rej}}{N_{inj}}.", "formula_004465": "Q_{\\beta\\beta}", "formula_004466": "^{-1}", "formula_004467": "T_{plate}", "formula_004468": "_2$$^{100}", "formula_004469": "\\cdot", "formula_004470": "P_{\\Delta t = 0}", "formula_004471": "0\\nu\\beta\\beta", "formula_004472": "G_{CUPID} = (1 - \\xi_{CUPID}) + \\xi_{CUPID} \\cdot G_{NTL},", "formula_004473": "LY_{CUPID}", "formula_004474": "\\gamma", "formula_004475": "0\\nu2\\beta", "formula_004476": "V = SL", "formula_004483": "E", "formula_004480": "L =(R_0+r_0) {\\sqrt{1+a^2}}\\,\\theta,", "formula_004482": "R", "formula_004477": "L= 2\\,k\\pi \\, R_1\\,\\sqrt{1+a^2}", "formula_004481": "O\\boldsymbol{\\overrightarrow k}", "formula_004478": "{\\overrightarrow {dOM}}=\\left(R_1 \\overrightarrow{\\boldsymbol v}+ a\\,R_1 \\,\\overrightarrow{\\boldsymbol{k}}\\right) d\\theta = {\\overrightarrow{\\boldsymbol{t}}}\\,ds", "formula_004479": "\\frac{4\\,\t\\mathcal W}{SL} \\approx \\lambda\\left[1+\\frac{3\\,a^2}{1+a^2}+\\frac{2\\,a}{\\displaystyle \\sqrt{1+a^2}}\\right]\t\t+ \\displaystyle   2\\mu\\left[2+\\frac{3\\,a^2}{1+a^2}+\\frac{3}{2\\displaystyle ({1+a^2})}-\\frac{a}{\\displaystyle \\sqrt{1+a^2}}\\right]", "formula_004484": "O\\boldsymbol {\\overrightarrow k}", "formula_004485": "s", "formula_004486": "\\displaystyle \\frac{\\partial \\boldsymbol x}{\\partial \\boldsymbol X_0}", "formula_004487": "\\displaystyle O\\, \\boldsymbol {\\overrightarrow{t}, \\overrightarrow{n}, \\overrightarrow{b}}", "formula_004488": "M_0\\, \\boldsymbol {\\overrightarrow{i}, \\overrightarrow{j}, \\overrightarrow{k}}", "formula_004489": "1", "formula_004490": "\\boldsymbol{\\overrightarrow u}=\\cos\\,\\theta\\,\\boldsymbol{\\overrightarrow i}+ \\sin\\, \\theta\\, \\boldsymbol{\\overrightarrow j}\\quad{\\rm and}\\quad\t\\boldsymbol{\\overrightarrow v}=-\\sin\\,\\theta\\,\\boldsymbol{\\overrightarrow i}+ \\cos\\, \\theta\\, \\boldsymbol{\\overrightarrow j}.", "formula_004491": "\\lambda", "formula_004492": "g", "formula_004493": "2\\,P_{\\tau}", "formula_004494": "\\mathcal W_{\\mathcal D}", "formula_004495": "\\left\\{ \t\\begin{array}{l}\t\tx= \\left(R_1-x_0\\right)\\,\\cos\\theta- \\left(\\displaystyle\\frac{z_0-a\\,y_0}{\\sqrt{1+a^2}}-R_1\\,\\theta\\right)\\sin\\theta\\\\ \\\\\t\ty = \\left(R_1-x_0\\right)\\,\\sin\\theta+  \\left(\\displaystyle\\frac{z_0-a\\,y_0}{\\sqrt{1+a^2}}-R_1\\,\\theta\\right)\\cos\\theta\\\\ \\\\\t\tz=\\displaystyle\\frac{y_0+a\\,z_0}{\\sqrt{1+a^2}}\\\\ \\\\\t\\end{array}\t\\right.", "formula_004496": "\\nu=0.25", "formula_004497": "M= \\sigma_0\\,S\\,L", "formula_004498": "{\\rm Tr} \\,D= \\frac{\\displaystyle a}{\\displaystyle \\sqrt{1+a^2}}-\\cos\\theta\\left(1+\\frac{a}{\\displaystyle \\sqrt{1+a^2}}\\right),", "formula_004499": "\\begin{array}{l}\\displaystyle\t4\\,\\mathcal W_{\\mathcal D}=   {S\\,L} \\left\\{\\lambda\\left[1+\\frac{3\\,a^2}{1+a^2}+\\frac{2\\,a}{\\displaystyle \\sqrt{1+a^2}}\\right]\\right.\\\\ \\displaystyle +\\left. 2\\,\\mu\\left[2+\\frac{3\\,a^2}{1+a^2}+\\frac{3}{2\\displaystyle ({1+a^2})}-\\frac{a}{\\displaystyle \\sqrt{1+a^2}}\\right]\\right\\}\\end{array}", "formula_004500": "d\\boldsymbol {\\overrightarrow M} = \\boldsymbol{\\overrightarrow t}\\, ds", "formula_004501": "(\\it\\Gamma)", "formula_004504": "({\\rm Tr} \\,D), \\, {\\rm Tr} \\,(D^2)", "formula_004502": "0.05", "formula_004505": "\\nu=0.05", "formula_004506": "z", "formula_004503": "M\\, \\boldsymbol {\\overrightarrow{t}, \\overrightarrow{n}, \\overrightarrow{b}}", "formula_004507": "\\overrightarrow{\\boldsymbol k}", "formula_004508": "d\\mathcal T_{_{\\mathcal T}}= P_{_{\\mathcal T}}\\, S\\,\\overrightarrow{\\boldsymbol{k}}.\\overrightarrow{\\boldsymbol{t}}\\, ds= P_{_{\\mathcal T}}\\,S  \\,\\frac{a}{\\displaystyle \\sqrt{1+a^2}}\\, ds,", "formula_004509": "[0, 0.25]", "formula_004510": "\\nu=0.15", "formula_004511": "\\mathcal W", "formula_004512": "L=\\frac{V}{\\pi \\,r_0^2}\\quad {\\rm and}\\quad  z =\\frac{V}{\\pi \\,r_0^2}\\, \\frac{a}{\\sqrt{1+a^2}}.", "formula_004514": "\\nu=0.2", "formula_004513": "ds =\\sqrt{1+a^2}\\, R_1\\, d\\theta\\quad {\\rm where}\\quad \\theta \\geq 0 ,\\,\\ {\\rm and }\\,\\ \\theta =0 \\,\\ {\\rm corresponds}\\,\\ to\\,\\ s=0.", "formula_004515": "z =\\frac{L\\,a}{\\sqrt{1+a^2}}.", "formula_004516": "\\mathcal D_0", "formula_004517": "S=\\pi \\, r_0^2", "formula_004518": "2\\,\\mathcal W_{\\mathcal D}=\\iiint_{\\mathcal D}\\left[\\lambda\\,\t({\\rm Tr} \\,D)^2+2\\,\\mu \\, {\\rm Tr} \\,(D^2) \\right]\\, dv, \\quad{\\rm where}\\quad  dv = R_1\\,\\sqrt{1+a^2}\\, dS\\, d\\theta.", "formula_004520": "M", "formula_004519": "D =\\left[ \\begin{matrix}\t\t-\\cos\\theta&\\frac{\\displaystyle\\sin\\theta}{\\displaystyle 2}\\left(\\displaystyle\\frac{a}{\\displaystyle\\sqrt{1+a^2}}-1\\right)& -\\frac{\\displaystyle\\sin \\theta}{\\displaystyle 2\\,\\sqrt{1+a^2}}\\\\ \\\\\t\t\\frac{\\displaystyle\\sin\\theta}{\\displaystyle 2}\\left(\\displaystyle\\frac{a}{\\displaystyle\\sqrt{1+a^2}}-1\\right) &  -\t\\frac{\\displaystyle a\\,\\cos\\theta}{\\displaystyle \\sqrt{1+a^2}} & \\frac{\\displaystyle 1}{\\displaystyle2\\,\\sqrt{1+a^2}}(1+\\cos\\theta)\\\\ \\\\\t\t-\\frac{\\displaystyle\\sin \\theta}{\\displaystyle 2\\,\\sqrt{1+a^2}} & \\frac{\\displaystyle 1}{\\displaystyle2\\,\\sqrt{1+a^2}}(1+\\cos\\theta) & \\frac{\\displaystyle a}{\\displaystyle \\sqrt{1+a^2}}\\\\\t\\end{matrix}\t\\right] .", "formula_004521": "\\frac{\\partial \\boldsymbol x}{\\partial \\boldsymbol X_0} =\\left[ \\begin{matrix}\t\\frac{\\displaystyle\\partial x}{\\displaystyle\\partial x_0} & \\frac{\\displaystyle\\partial x}{\\displaystyle\\partial y_0} & \\frac{\\displaystyle\\partial x}{\\displaystyle\\partial z_0}\\\\ \\\\\t\\frac{\\displaystyle\\partial y}{\\displaystyle\\partial x_0} & \\frac{\\displaystyle\\partial y}{\\displaystyle\\partial y_0} & \\frac{\\displaystyle\\partial y}{\\displaystyle\\partial z_0}\\\\ \\\\\t\\frac{\\displaystyle\\partial z}{\\displaystyle\\partial x_0} & \\frac{\\displaystyle\\partial z}{\\displaystyle\\partial y_0} & \\frac{\\displaystyle\\partial z}{\\displaystyle\\partial z_0}\\\\\\end{matrix}\\right],\\quad{\\rm where}\\quad    \\boldsymbol x =\\left[\\ \\begin{matrix}\tx & \\\\ \t\ty &\\\\  \t\tz \t\\end{matrix}\\right].", "formula_004522": "R_0", "formula_004523": "\\overrightarrow{0P_0}= x_o\\boldsymbol {\\overrightarrow i}+y_o\\boldsymbol {\\overrightarrow j}+z_o\\boldsymbol {\\overrightarrow k}\\qquad{\\rm and }\\qquad  \\boldsymbol X_0 =\\left[\\ \\begin{matrix} x_0 & \\\\  y_0 &\\\\   z_0 \\end{matrix}\\right],", "formula_004524": "D", "formula_004525": "\\begin{array} {ccc}\t\t2\\,\\mathcal W = \t \\displaystyle \\sigma_0\\, g\\, S  \\frac{a\\,}{\\displaystyle\\sqrt{1+a^2}}\\, L^2   - 2\\, P_{_{\\mathcal T}}\\,S  \\,\\frac{\\displaystyle a}{\\displaystyle \\sqrt{1+a^2}}\\, L \\\\ \\\\\t \\displaystyle +\\frac{S\\,L}{2}\\left\\{\\lambda\\left[1+\\frac{3\\,a^2}{1+a^2}+\\frac{2\\,a}{\\displaystyle \\sqrt{1+a^2}}\\right]  + \\mu\\left[2+\\frac{3\\,a^2}{1+a^2}+\\frac{3}{2\\displaystyle ({1+a^2})}-\\frac{a}{\\displaystyle \\sqrt{1+a^2}}\\right]\\right\\} \t\\end{array}", "formula_004529": "k\\in {\\it\\mathbb{N}}^\\star", "formula_004526": "\\mathcal W_{_{\\mathcal T}}= - P_{_{\\mathcal T}}\\,S  \\,\\frac{\\displaystyle a}{\\displaystyle \\sqrt{1+a^2}}\\, L", "formula_004528": "2\\,\\mathcal{E_D}=\\lambda\\,\t({\\rm Tr} \\,D)^2+2\\,\\mu \\, {\\rm Tr} \\,(D^2),", "formula_004527": "d\\boldsymbol {\\overrightarrow M} \\equiv d{\\overrightarrow {OM}}", "formula_004530": "\\mu", "formula_004532": "\\tau", "formula_004531": "{\\overrightarrow OP} = x\\,\\boldsymbol{\\overrightarrow i}+y\\,\\boldsymbol{\\overrightarrow j}+z\\,\\boldsymbol{\\overrightarrow k}", "formula_004533": "\\mathcal W_{\\mathcal P}= \\sigma_0\\, g\\, S\\,R_1^2\\, a\\,\\sqrt{1+a^2}\\ \\frac{\\theta^2}{2}, \\quad{\\rm with}\\quad \\frac{\\theta^2}{2}= \\frac{L^2}{\\displaystyle 2\\, R_1^2(1+a^2)}", "formula_004534": "\\mathcal D", "formula_004535": "a", "formula_004537": "\\nu=0.22", "formula_004536": "\\overrightarrow{MP} = x_0\\,\\boldsymbol{\\overrightarrow n}+y_0\\,\\boldsymbol{\\overrightarrow b}+\\left(z_0- s\\right)\\,\\boldsymbol{\\overrightarrow t}\\equiv\\left(z_0- R_1\\sqrt{1+a^2}\\,\\theta\\right)\\,\\boldsymbol{\\overrightarrow t}-x_0\\,\\boldsymbol{\\overrightarrow u}+y_0\\,\\boldsymbol{\\overrightarrow b},", "formula_004538": "\\left\\{\\begin{array}{l} d\\boldsymbol {\\overrightarrow t} = \\displaystyle\\frac{\\boldsymbol{\\overrightarrow n}}{R} \\,ds\\equiv\\rho\\,\\boldsymbol{\\overrightarrow n}\\,ds\\\\ \\\\\t d\\boldsymbol {\\overrightarrow n} = \\displaystyle\\left(-\\frac{\\boldsymbol{\\overrightarrow t}}{R}+\\frac{\\boldsymbol{\\overrightarrow b}}{\\tau}  \\right)\\,ds\\equiv \\left(-\\rho\\,\\boldsymbol {\\overrightarrow t}+ \\gamma\\,\\boldsymbol {\\overrightarrow b}\\right)\\,ds\\\\ \\\\\t d\\boldsymbol {\\overrightarrow b}=-\\displaystyle\\frac{\\boldsymbol{\\overrightarrow n}}{\\tau}\\,ds \\equiv-\\gamma\\,\\boldsymbol{\\overrightarrow n}\\,ds\\end{array}\\right.", "formula_004539": "r_0", "formula_004540": "3", "formula_004541": "\\theta =0", "formula_004542": "\\theta", "formula_004544": "R_1", "formula_004543": "L", "formula_004545": "\\iiint_{\\mathcal D}{\\rm Tr} \\,(D^2)\\, dv = \\frac{S\\,L}{2}\\left[2+\\frac{3\\,a^2}{1+a^2}+\\frac{3}{2\\displaystyle ({1+a^2})}-\\frac{a}{\\displaystyle \\sqrt{1+a^2}}\\right].", "formula_004547": "L= 2\\,k\\pi \\, R_1\\,\\sqrt{1+a^2}.", "formula_004546": "E = \\frac{\\mu(3\\lambda+2\\mu)}{\\lambda+\\mu}, \\qquad\\,\\ \\nu =\\frac{\\lambda}{2(\\lambda+\\mu)},", "formula_004548": "P_{_{\\mathcal T}}\\,\\overrightarrow{\\boldsymbol{k}}", "formula_004549": "\\iiint_{\\mathcal D}( {\\rm Tr} \\, D )^2\\, dv = \\frac{S\\,L}{2}\\left[1+\\frac{3\\,a^2}{1+a^2}+\\frac{2\\,a}{\\displaystyle \\sqrt{1+a^2}}\\right]", "formula_004553": "{\\rm Tr}", "formula_004552": "(\\it \\Gamma)", "formula_004551": "(\\it\\Gamma_0)", "formula_004550": "\\overrightarrow{OP} = \\left(R_1-x_0\\right)\\,\\boldsymbol{\\overrightarrow u}+\\left(\\frac{z_0-a\\,y_0}{\\sqrt{1+a^2}}-R_1\\,\\theta\\right)\\boldsymbol {\\overrightarrow v}+\\left(\\frac{y_0+a\\,z_0}{\\sqrt{1+a^2}}\\right)\\boldsymbol {\\overrightarrow k}.", "formula_004554": "P_{_{\\mathcal T}}", "formula_004556": "0.3", "formula_004555": "\\begin{eqnarray*}& &{\\rm Tr} \\,(D^2) =   \\cos^2\\theta \\left(1+\\frac{\\displaystyle a^2}{\\displaystyle {1+a^2}}+\\frac{\\displaystyle 1}{\\displaystyle {2(1+a^2)}}\\right)+\\\\ & &      \\displaystyle \\frac{\\sin^2\\theta}{2}\\left(1+\\frac{\\displaystyle a^2}{\\displaystyle {1+a^2}}-\\frac{2\\,a}{\\displaystyle \\sqrt{1+a^2}}+\\frac{\\displaystyle 1}{\\displaystyle {(1+a^2)}}\\right) +\\frac{\\cos\\theta}{1+a^2}+\\frac{\\displaystyle 1}{\\displaystyle {2(1+a^2)}} +\\frac{\\displaystyle a^2}{\\displaystyle {1+a^2}}.         \\end{eqnarray*}", "formula_004557": "\\rho", "formula_004558": "R_1=R_0+r_0", "formula_004559": "\\rho=1/R", "formula_004560": "({\\rm Tr} \\,D)^2 =\\frac{a^2}{1+a^2}   +\\cos^2\\theta \\left(1+\\frac{\\displaystyle a}{\\displaystyle \\sqrt{1+a^2}}\\right)^2-\\frac{2\\,a}{\\displaystyle \\sqrt{1+a^2}} \\left(1+\\frac{a}{\\displaystyle \\sqrt{1+a^2}}\\right)\\, \\cos\\theta ,", "formula_004562": "4\\,\t\\mathcal W_{\\mathcal D}=\\iint_{\\it \\Sigma} \\left[\\int_0^{2k\\pi}\t\\left(\\lambda\\,({\\rm Tr} \\,D)^2+2\\,\\mu \\, {\\rm Tr} \\,(D^2)\\right)\\,ds \\right]\\, dS,", "formula_004561": "\\frac{d\\overrightarrow{OM}}{d\\theta}=R_1\\boldsymbol {\\overrightarrow v}+ a\\,R_1 \\,\\boldsymbol{\\overrightarrow k}.", "formula_004563": "10^4", "formula_004564": "\\sigma_0\\,g \\,L", "formula_004565": "\\gamma=1/\\tau", "formula_004566": "\\displaystyle\\frac{ 4 W}{SL}", "formula_004568": "\\sigma_0", "formula_004567": "\\nu \\in [0, 0.25]", "formula_004569": "\\rho={\\frac{1}{R_1\\,\\left(1+a^2\\right)}},\\quad{\\rm and}\\quad \\gamma={\\frac{a}{R_1\\,\\left(1+a^2\\right)}}.", "formula_004571": "d\\boldsymbol {\\overrightarrow t}, d\\boldsymbol {\\overrightarrow n}, d\\boldsymbol {\\overrightarrow b}", "formula_004570": "\\int_0^{2k \\pi}\\cos^2\\theta \\, d\\theta = \\int_0^{2k \\pi}\\sin^2\\theta \\, d\\theta=k\\pi\\quad{\\rm and}\\quad \\int_0^{2k \\pi}\\cos \\theta \\, d\\theta=0", "formula_004572": "d\\mathcal W_{\\mathcal P}=g\\,z\\,dm,", "formula_004573": "P", "formula_004574": "10^9", "formula_004576": "10^6", "formula_004575": "S=\\pi r_0^2", "formula_004578": "\\nu", "formula_004577": "^1", "formula_004580": "a \\in [0.05, 0.22]", "formula_004579": "\\begin{array} {ccc}\\displaystyle\t\\frac{2\\,\t\\mathcal W}{SL} =  \t\\left(\\displaystyle \\sigma_0\\, g\\,L- 2\\,P_{_{\\mathcal T}}\\right)\\displaystyle \\frac{a}{ \\sqrt{1+a^2}}\\\\ \\\\\t\\displaystyle +\t\\frac{1}{2} \\left\\{\\lambda\\left[1+\\frac{3\\,a^2}{1+a^2}+\\frac{2\\,a}{\\sqrt{1+a^2}}\\right] \t\t+     2\\,\\mu\\left[2+\\frac{3\\,a^2}{1+a^2}+\\frac{3}{2\\displaystyle ({1+a^2})}-\\frac{a}{\\displaystyle \\sqrt{1+a^2}}\\right]\\right\\} \t\\end{array}", "formula_004581": "\\nu\\succeq 0.25", "formula_004582": "\\boldsymbol{\\overrightarrow t}= \\frac{\\boldsymbol{\\overrightarrow v}+a\\,\\boldsymbol{\\overrightarrow k}}{\\sqrt{1+a^2}},\\quad \\boldsymbol {\\overrightarrow n} = -\\boldsymbol{\\overrightarrow u} ,\\quad \\boldsymbol{\\overrightarrow b}=\\frac{\\boldsymbol{\\overrightarrow k}-a\\,\\boldsymbol{\\overrightarrow v}}{\\sqrt{1+a^2}}", "formula_004583": "\\left\\{\t\\begin{array}{l}\t\td\\boldsymbol {\\overrightarrow t} = d\\boldsymbol{\\overrightarrow\\omega}\\times \\boldsymbol {\\overrightarrow t}\\\\ \\\\\t\td\\boldsymbol {\\overrightarrow n} = d\\boldsymbol{\\overrightarrow \\omega}\\times \\boldsymbol {\\overrightarrow n}\\\\ \\\\\t\td\\boldsymbol {\\overrightarrow b}=d\\boldsymbol{\\overrightarrow \\omega}\\times \\boldsymbol {\\overrightarrow b}\\\\ \\\\\t\\end{array}\t\\right. \\qquad{\\rm with}\\quad d\\boldsymbol{\\overrightarrow \\omega}= \\boldsymbol{\\overrightarrow \\Omega}\\,ds\\quad{\\rm where}\\quad \\boldsymbol{\\overrightarrow \\Omega}=\\gamma\\,\\boldsymbol{\\overrightarrow t}+\\rho\\,\\boldsymbol{\\overrightarrow n},", "formula_004584": "dv =dS\\times ds", "formula_004585": "\\mathcal W_{\\mathcal P}= \\sigma_0\\, g\\, S  \\frac{a\\,}{2\\displaystyle\\sqrt{1+a^2}}\\, L^2.", "formula_004586": "{\\overrightarrow {OM}} = R_1 \\left(\\boldsymbol {\\overrightarrow u}+ a \\,   \\theta\\,\\boldsymbol{\\overrightarrow k}\\right)", "formula_004587": "(\\it \\Sigma)", "formula_004588": "M_0", "formula_004590": "\\mathcal{E_D}", "formula_004589": "P_0", "formula_004592": "\\nu=0.1", "formula_004591": "S", "formula_004593": "\\gamma"}