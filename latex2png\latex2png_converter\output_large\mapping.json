{"formula_000001": "x_1^2 + y_1^2 = z_1^2", "formula_000002": "x_2^2 + y_2^2 = z_2^2", "formula_000003": "x_3^2 + y_3^2 = z_3^2", "formula_000004": "x_4^2 + y_4^2 = z_4^2", "formula_000005": "x_5^2 + y_5^2 = z_5^2", "formula_000006": "\\int_0^1 x^1 dx", "formula_000007": "\\int_0^2 x^2 dx", "formula_000008": "\\int_0^3 x^3 dx", "formula_000009": "\\int_0^4 x^4 dx", "formula_000010": "\\int_0^5 x^5 dx", "formula_000011": "\\sum_{i=1}^{10} i^1", "formula_000012": "\\sum_{i=1}^{10} i^2", "formula_000013": "\\sum_{i=1}^{10} i^3", "formula_000014": "\\sum_{i=1}^{10} i^4", "formula_000015": "\\sum_{i=1}^{10} i^5", "formula_000016": "\\frac{a_1}{b_1} = c_1", "formula_000017": "\\frac{a_2}{b_2} = c_2", "formula_000018": "\\frac{a_3}{b_3} = c_3", "formula_000019": "\\frac{a_4}{b_4} = c_4", "formula_000020": "\\frac{a_5}{b_5} = c_5", "formula_000021": "\\sqrt{1^2 + 1^2}", "formula_000022": "\\sqrt{2^2 + 2^2}", "formula_000023": "\\sqrt{3^2 + 3^2}", "formula_000024": "\\sqrt{4^2 + 4^2}", "formula_000025": "\\sqrt{5^2 + 5^2}", "formula_000026": "\\lim_{x \\to 1} f_1(x)", "formula_000027": "\\lim_{x \\to 2} f_2(x)", "formula_000028": "\\lim_{x \\to 3} f_3(x)", "formula_000029": "\\lim_{x \\to 4} f_4(x)", "formula_000030": "\\lim_{x \\to 5} f_5(x)", "formula_000031": "\\sin(1x) + \\cos(1x)", "formula_000032": "\\sin(2x) + \\cos(2x)", "formula_000033": "\\sin(3x) + \\cos(3x)", "formula_000034": "\\sin(4x) + \\cos(4x)", "formula_000035": "\\sin(5x) + \\cos(5x)", "formula_000036": "e^{1x} + \\ln(1x)", "formula_000037": "e^{2x} + \\ln(2x)", "formula_000038": "e^{3x} + \\ln(3x)", "formula_000039": "e^{4x} + \\ln(4x)", "formula_000040": "e^{5x} + \\ln(5x)", "formula_000041": "\\alpha_1 + \\beta_1 = \\gamma_1", "formula_000042": "\\alpha_2 + \\beta_2 = \\gamma_2", "formula_000043": "\\alpha_3 + \\beta_3 = \\gamma_3", "formula_000044": "\\alpha_4 + \\beta_4 = \\gamma_4", "formula_000045": "\\alpha_5 + \\beta_5 = \\gamma_5", "formula_000046": "\\nabla f_1 = \\frac{\\partial f_1}{\\partial x}", "formula_000047": "\\nabla f_2 = \\frac{\\partial f_2}{\\partial x}", "formula_000048": "\\nabla f_3 = \\frac{\\partial f_3}{\\partial x}", "formula_000049": "\\nabla f_4 = \\frac{\\partial f_4}{\\partial x}", "formula_000050": "\\nabla f_5 = \\frac{\\partial f_5}{\\partial x}"}