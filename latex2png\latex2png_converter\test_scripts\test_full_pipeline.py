#!/usr/bin/env python3
"""
完整流水线测试
模拟完整的LaTeX2PNG转换流程
"""

import sys
import os
import time
import tempfile
import json
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def setup_texlive_environment():
    """设置TeX Live环境"""
    texlive_path = r"D:\Program Files\texlive\2025\bin\windows"
    
    if Path(texlive_path).exists():
        # 添加到PATH
        current_path = os.environ.get('PATH', '')
        if texlive_path not in current_path:
            os.environ['PATH'] = f"{texlive_path};{current_path}"
        
        # 移除MiKTeX路径
        path_parts = os.environ['PATH'].split(';')
        clean_parts = [p for p in path_parts if 'miktex' not in p.lower()]
        os.environ['PATH'] = ';'.join(clean_parts)
        
        print(f"✓ TeX Live环境已配置: {texlive_path}")
        return True
    else:
        print(f"⚠ TeX Live路径不存在: {texlive_path}")
        return False

def test_latex_commands():
    """测试LaTeX命令可用性"""
    print("\n1. 测试LaTeX命令:")
    
    import subprocess
    commands = ['latex', 'pdflatex']
    
    for cmd in commands:
        try:
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.split('\n')[0]
                print(f"   ✓ {cmd}: {version}")
            else:
                print(f"   ✗ {cmd}: 命令失败")
                return False
        except Exception as e:
            print(f"   ✗ {cmd}: {e}")
            return False
    
    return True

def test_matplotlib_integration():
    """测试matplotlib集成"""
    print("\n2. 测试matplotlib + TeX Live集成:")
    
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        
        # 配置matplotlib使用TeX Live
        matplotlib.rcParams['text.usetex'] = True
        matplotlib.rcParams['text.latex.preamble'] = r'''
        \usepackage{amsmath}
        \usepackage{amsfonts}
        \usepackage{amssymb}
        '''
        matplotlib.rcParams['font.family'] = 'serif'
        
        print("   ✓ matplotlib配置完成")
        
        # 测试渲染
        fig, ax = plt.subplots(figsize=(4, 1))
        ax.text(0.5, 0.5, r'$\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}$',
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes,
               fontsize=14)
        ax.axis('off')
        
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            temp_file = f.name
        
        plt.savefig(temp_file, dpi=300, bbox_inches='tight', transparent=True)
        plt.close(fig)
        
        if Path(temp_file).exists() and Path(temp_file).stat().st_size > 0:
            print(f"   ✓ 集成测试成功: {Path(temp_file).stat().st_size} bytes")
            os.unlink(temp_file)
            return True
        else:
            print("   ✗ 集成测试失败")
            return False
            
    except Exception as e:
        print(f"   ✗ 集成测试失败: {e}")
        plt.close('all')
        return False

def simulate_batch_processing():
    """模拟批量处理流程"""
    print("\n3. 模拟批量处理流程:")
    
    # 测试数据
    test_formulas = [
        r"x^2 + y^2 = z^2",
        r"\frac{-b \pm \sqrt{b^2 - 4ac}}{2a}",
        r"\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}",
        r"\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}",
        r"\begin{bmatrix} a & b \\ c & d \end{bmatrix}",
        r"\oint_C \vec{F} \cdot d\vec{r} = \iint_S (\nabla \times \vec{F}) \cdot d\vec{S}",
        r"\mathcal{L}\{f(t)\} = \int_0^{\infty} f(t) e^{-st} dt",
        r"E = mc^2",
        r"\lim_{x \to 0} \frac{\sin x}{x} = 1",
        r"\nabla \cdot \vec{E} = \frac{\rho}{\epsilon_0}"
    ]
    
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        
        # 配置matplotlib
        matplotlib.rcParams['text.usetex'] = True
        matplotlib.rcParams['text.latex.preamble'] = r'''
        \usepackage{amsmath}
        \usepackage{amsfonts}
        \usepackage{amssymb}
        \usepackage{mathtools}
        '''
        
        # 创建输出目录
        output_dir = Path("./test_output")
        output_dir.mkdir(exist_ok=True)
        images_dir = output_dir / "images"
        images_dir.mkdir(exist_ok=True)
        
        print(f"   输出目录: {output_dir.absolute()}")
        
        # 处理统计
        results = {
            'total': len(test_formulas),
            'success': 0,
            'failed': 0,
            'processing_times': [],
            'file_sizes': [],
            'mapping': {}
        }
        
        start_time = time.time()
        
        for i, formula in enumerate(test_formulas):
            item_start = time.time()
            
            try:
                # 创建图形
                fig, ax = plt.subplots(figsize=(6, 1.5))
                ax.text(0.5, 0.5, f'${formula}$',
                       horizontalalignment='center',
                       verticalalignment='center',
                       transform=ax.transAxes,
                       fontsize=12)
                ax.axis('off')
                
                # 生成文件名
                filename = f"formula_{i+1:04d}.png"
                filepath = images_dir / filename
                
                # 保存图像
                plt.savefig(filepath, dpi=300, bbox_inches='tight', 
                           transparent=True, facecolor='none')
                plt.close(fig)
                
                item_time = time.time() - item_start
                
                # 验证输出
                if filepath.exists() and filepath.stat().st_size > 0:
                    file_size = filepath.stat().st_size
                    results['success'] += 1
                    results['processing_times'].append(item_time)
                    results['file_sizes'].append(file_size)
                    results['mapping'][f"formula_{i+1:04d}"] = formula
                    
                    print(f"   ✓ 公式 {i+1:2d}: {item_time:.3f}s, {file_size:,} bytes")
                else:
                    results['failed'] += 1
                    print(f"   ✗ 公式 {i+1:2d}: 渲染失败")
                    
            except Exception as e:
                results['failed'] += 1
                item_time = time.time() - item_start
                print(f"   ✗ 公式 {i+1:2d}: 异常 ({item_time:.3f}s) - {e}")
                plt.close('all')
        
        total_time = time.time() - start_time
        
        # 生成映射文件
        mapping_file = output_dir / "mapping.json"
        with open(mapping_file, 'w', encoding='utf-8') as f:
            json.dump(results['mapping'], f, ensure_ascii=False, indent=2)
        
        # 生成统计报告
        if results['success'] > 0:
            avg_time = sum(results['processing_times']) / len(results['processing_times'])
            avg_size = sum(results['file_sizes']) / len(results['file_sizes'])
            rate = results['success'] / total_time
            
            report = f"""
批量处理测试报告
===============

处理统计:
- 总数: {results['total']}
- 成功: {results['success']}
- 失败: {results['failed']}
- 成功率: {results['success']/results['total']*100:.1f}%

性能指标:
- 总耗时: {total_time:.2f}秒
- 平均时间: {avg_time:.3f}秒/公式
- 处理速率: {rate:.1f}公式/秒
- 预估速率: {rate*60:.0f}公式/分钟

质量指标:
- 平均文件大小: {avg_size:,.0f} bytes
- 文件大小范围: {min(results['file_sizes']):,} - {max(results['file_sizes']):,} bytes

输出文件:
- 图像目录: {images_dir}
- 映射文件: {mapping_file}
- 成功生成: {results['success']} 个PNG文件
            """
            
            report_file = output_dir / "processing_report.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(report)
            
            # 与目标性能比较
            target_rate = 2000 / 60  # 33.3公式/秒
            achievement = rate / target_rate * 100
            print(f"   目标达成率: {achievement:.1f}% (目标: {target_rate:.1f}公式/秒)")
            
            return results['success'] == results['total']
        else:
            print("   ✗ 所有公式都失败")
            return False
            
    except Exception as e:
        print(f"   ✗ 批量处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("LaTeX2PNG 完整流水线测试")
    print("=" * 50)
    
    # 设置环境
    texlive_ok = setup_texlive_environment()
    
    tests = [
        ("LaTeX命令", test_latex_commands),
        ("matplotlib集成", test_matplotlib_integration),
        ("批量处理流程", simulate_batch_processing),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"完整流水线测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 LaTeX2PNG系统完全可用！")
        print("\n系统特性:")
        print("- ✅ TeX Live 2025集成")
        print("- ✅ 高质量LaTeX渲染")
        print("- ✅ 复杂数学公式支持")
        print("- ✅ 批量处理能力")
        print("- ✅ 完整输出管理")
        
        print("\n下一步:")
        print("1. 处理您的实际数据文件")
        print("2. 根据需要调整并发参数")
        print("3. 设置永久PATH环境变量")
        
        return 0
    else:
        print("❌ 系统存在问题，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
