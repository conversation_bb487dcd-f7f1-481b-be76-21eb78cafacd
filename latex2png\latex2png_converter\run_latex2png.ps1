# LaTeX2PNG 启动脚本
# 自动配置TeX Live环境并运行转换器

param(
    [Parameter(Mandatory=$true)]
    [string]$InputFile,
    
    [string]$OutputDir = "./output",
    [string]$TexLivePath = "D:\Program Files\texlive\2025\bin\windows",
    [string]$PythonPath = "D:\miniforge3\envs\formula\python.exe",
    [int]$MaxWorkers = 16,
    [int]$BatchSize = 2000,
    [int]$DPI = 300,
    [switch]$Verbose,
    [switch]$TestEngines
)

Write-Host "LaTeX2PNG 转换器启动脚本" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# 1. 检查输入文件
if (-not (Test-Path $InputFile)) {
    Write-Host "错误: 输入文件不存在: $InputFile" -ForegroundColor Red
    exit 1
}

Write-Host "输入文件: $InputFile" -ForegroundColor Cyan
Write-Host "输出目录: $OutputDir" -ForegroundColor Cyan

# 2. 配置TeX Live环境
Write-Host "`n配置TeX Live环境..." -ForegroundColor Yellow

if (Test-Path $TexLivePath) {
    # 临时添加TeX Live到PATH
    $env:PATH = "$TexLivePath;$env:PATH"
    Write-Host "✓ TeX Live路径已添加: $TexLivePath" -ForegroundColor Green
    
    # 验证LaTeX命令
    try {
        $latexVersion = & latex --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            $versionLine = ($latexVersion | Select-Object -First 1)
            Write-Host "✓ LaTeX可用: $versionLine" -ForegroundColor Green
        } else {
            Write-Host "⚠ LaTeX命令验证失败" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠ LaTeX命令测试失败" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ TeX Live路径不存在: $TexLivePath" -ForegroundColor Yellow
    Write-Host "  将使用matplotlib内置mathtext" -ForegroundColor Yellow
}

# 3. 清理可能的MiKTeX路径冲突
$env:PATH = ($env:PATH -split ';' | Where-Object { $_ -notlike '*miktex*' }) -join ';'

# 4. 检查Python环境
Write-Host "`n检查Python环境..." -ForegroundColor Yellow

if (Test-Path $PythonPath) {
    try {
        $pythonVersion = & $PythonPath --version 2>$null
        Write-Host "✓ Python可用: $pythonVersion" -ForegroundColor Green
    } catch {
        Write-Host "✗ Python测试失败" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✗ Python路径不存在: $PythonPath" -ForegroundColor Red
    exit 1
}

# 5. 构建命令行参数
$args = @()
if (-not $TestEngines) {
    $args += $InputFile
}
$args += "--output-dir", $OutputDir
$args += "--max-workers", $MaxWorkers
$args += "--batch-size", $BatchSize
$args += "--dpi", $DPI

if ($Verbose) {
    $args += "--verbose"
}

if ($TestEngines) {
    $args += "--test-engines"
}

# 6. 显示运行信息
Write-Host "`n运行配置:" -ForegroundColor Yellow
Write-Host "- Python: $PythonPath" -ForegroundColor White
Write-Host "- TeX Live: $TexLivePath" -ForegroundColor White
if (-not $TestEngines) {
    Write-Host "- 输入文件: $InputFile" -ForegroundColor White
}
Write-Host "- 输出目录: $OutputDir" -ForegroundColor White
Write-Host "- 最大并发: $MaxWorkers" -ForegroundColor White
Write-Host "- 批次大小: $BatchSize" -ForegroundColor White
Write-Host "- DPI: $DPI" -ForegroundColor White

# 7. 运行转换器
Write-Host "`n启动LaTeX2PNG转换器..." -ForegroundColor Green
Write-Host "命令: $PythonPath main.py $($args -join ' ')" -ForegroundColor Gray

try {
    $startTime = Get-Date
    & $PythonPath main.py @args
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✓ 转换完成!" -ForegroundColor Green
        Write-Host "总耗时: $($duration.TotalSeconds.ToString('F2')) 秒" -ForegroundColor Cyan
        
        if (-not $TestEngines -and (Test-Path $OutputDir)) {
            Write-Host "`n输出文件:" -ForegroundColor Yellow
            Get-ChildItem $OutputDir -Recurse | ForEach-Object {
                Write-Host "  $($_.FullName)" -ForegroundColor White
            }
        }
    } else {
        Write-Host "`n✗ 转换失败 (退出码: $LASTEXITCODE)" -ForegroundColor Red
        exit $LASTEXITCODE
    }
    
} catch {
    Write-Host "`n✗ 运行异常: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n脚本执行完成!" -ForegroundColor Green
