#!/usr/bin/env python3
"""
调试配置文件解析
验证路径是否正确解析
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config_loader import load_user_config

def debug_config():
    """调试配置文件"""
    print("配置文件调试")
    print("=" * 40)
    
    # 加载配置
    config = load_user_config("config/user_config.yaml")
    
    if not config:
        print("配置加载失败")
        return
    
    # 检查IO配置
    io_config = config.get('io', {})
    
    print("IO配置:")
    for key, value in io_config.items():
        print(f"  {key}: {repr(value)}")
    
    # 特别检查输入文件路径
    input_file = io_config.get('default_input_file')
    print(f"\n输入文件路径分析:")
    print(f"  原始值: {repr(input_file)}")
    print(f"  类型: {type(input_file)}")
    print(f"  长度: {len(input_file) if input_file else 0}")
    
    if input_file:
        # 检查路径
        path = Path(input_file)
        print(f"  Path对象: {path}")
        print(f"  绝对路径: {path.resolve()}")
        print(f"  是否存在: {path.exists()}")
        
        if not path.exists():
            print(f"  父目录: {path.parent}")
            print(f"  父目录存在: {path.parent.exists()}")
            print(f"  文件名: {path.name}")
            
            # 列出父目录内容
            if path.parent.exists():
                print(f"  父目录内容:")
                try:
                    for item in path.parent.iterdir():
                        print(f"    - {item.name}")
                except Exception as e:
                    print(f"    错误: {e}")

def test_yaml_parsing():
    """测试YAML解析"""
    print("\nYAML解析测试")
    print("=" * 40)
    
    import yaml
    
    # 测试不同的路径写法
    test_cases = [
        'path1: r"D:\\test\\file.txt"',
        'path2: "D:\\\\test\\\\file.txt"',
        'path3: "D:/test/file.txt"',
        'path4: D:\\test\\file.txt',
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case}")
        try:
            result = yaml.safe_load(test_case)
            for key, value in result.items():
                print(f"  解析结果: {key} = {repr(value)}")
        except Exception as e:
            print(f"  解析错误: {e}")

def main():
    """主函数"""
    debug_config()
    test_yaml_parsing()

if __name__ == "__main__":
    main()
