#!/usr/bin/env python3
"""
测试前100个公式的修复效果
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config_loader import load_user_config, setup_environment_from_config

def create_sample_file():
    """创建前100个公式的样本文件"""
    config = load_user_config("config/user_config.yaml")
    input_file = config['io']['default_input_file']
    
    # 读取前100行
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    sample_lines = lines[:100]
    
    # 保存样本文件
    sample_file = "sample_100.txt"
    with open(sample_file, 'w', encoding='utf-8') as f:
        f.writelines(sample_lines)
    
    print(f"✓ 创建样本文件: {sample_file}")
    print(f"  包含 {len(sample_lines)} 个公式")
    
    return sample_file

def main():
    """主函数"""
    print("创建测试样本")
    print("=" * 50)
    
    sample_file = create_sample_file()
    
    print(f"\n现在可以运行:")
    print(f"python run_with_config.py {sample_file} --verbose")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
