# 测试脚本目录

这个目录包含了所有的测试和调试脚本，已从主目录移动到这里以保持项目结构清洁。

## 📁 脚本分类

### 🔧 环境配置脚本
- `setup_texlive_env.ps1` - 设置TeX Live环境
- `find_texlive.ps1` - 查找TeX Live安装
- `find_texlive_simple.ps1` - 简化版TeX Live查找
- `install_texlive_portable.ps1` - 安装便携版TeX Live

### 🧹 清理脚本
- `cleanup_miktex_path.ps1` - 清理MiKTeX路径冲突
- `check_cleanup.ps1` - 检查清理结果

### 🧪 功能测试脚本
- `test_environment.py` - 环境配置测试
- `test_full_pipeline.py` - 完整流水线测试（推荐）
- `test_texlive_direct.py` - TeX Live直接功能测试
- `test_texlive_integration.py` - TeX Live集成测试
- `verify_texlive.py` - TeX Live安装验证

### 🎯 演示脚本
- `quick_demo.py` - 快速演示脚本
- `quick_test.py` - 快速功能测试

### 🐛 调试脚本
- `debug_config.py` - 配置文件调试

## 🚀 推荐使用顺序

### 1. 首次安装后
```bash
# 验证TeX Live安装
python test_scripts/verify_texlive.py

# 测试完整流水线
python test_scripts/test_full_pipeline.py
```

### 2. 问题排查
```bash
# 调试配置文件
python test_scripts/debug_config.py

# 测试环境配置
python test_scripts/test_environment.py
```

### 3. 快速演示
```bash
# 运行演示
python test_scripts/quick_demo.py
```

## 📝 注意事项

- 所有脚本都应该从主目录运行
- 某些PowerShell脚本可能需要管理员权限
- 测试脚本会创建临时输出文件，可以安全删除

## 🔄 如何使用

从主目录运行测试脚本：
```bash
# 从主目录运行
cd d:\formula_ge\latex2png\latex2png_converter
python test_scripts/test_full_pipeline.py
```

或者使用相对路径：
```bash
python test_scripts/脚本名称.py
```
