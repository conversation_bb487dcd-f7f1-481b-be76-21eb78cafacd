#!/usr/bin/env python3
"""
直接测试TeX Live功能（不依赖相对导入）
"""

import matplotlib
import matplotlib.pyplot as plt
import subprocess
import tempfile
import time
from pathlib import Path

def test_external_latex():
    """测试外部LaTeX命令"""
    print("1. 测试外部LaTeX命令:")
    
    try:
        # 检查latex命令
        result = subprocess.run(['latex', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"   ✓ LaTeX: {version_line}")
            
            # 检查是否是TeX Live
            if 'tex live' in result.stdout.lower():
                print("   ✓ 确认为TeX Live")
                return True
            else:
                print("   ⚠ 不是TeX Live")
                return False
        else:
            print("   ✗ LaTeX命令执行失败")
            return False
            
    except Exception as e:
        print(f"   ✗ LaTeX命令测试失败: {e}")
        return False

def test_matplotlib_with_texlive():
    """测试matplotlib与TeX Live集成"""
    print("\n2. 测试matplotlib + TeX Live:")
    
    try:
        # 配置matplotlib使用外部LaTeX
        matplotlib.rcParams['text.usetex'] = True
        matplotlib.rcParams['text.latex.preamble'] = r'''
        \usepackage{amsmath}
        \usepackage{amsfonts}
        \usepackage{amssymb}
        \usepackage{mathtools}
        '''
        matplotlib.rcParams['font.family'] = 'serif'
        matplotlib.rcParams['font.serif'] = ['Computer Modern Roman']
        
        print("   ✓ matplotlib配置完成")
        
        # 测试简单公式
        fig, ax = plt.subplots(figsize=(4, 1))
        ax.text(0.5, 0.5, r'$x^2 + y^2 = z^2$',
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes,
               fontsize=16)
        ax.axis('off')
        
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            temp_file = f.name
        
        plt.savefig(temp_file, dpi=300, bbox_inches='tight', transparent=True)
        plt.close(fig)
        
        if Path(temp_file).exists() and Path(temp_file).stat().st_size > 0:
            print(f"   ✓ 简单公式渲染成功: {Path(temp_file).stat().st_size} bytes")
            Path(temp_file).unlink()  # 清理
            return True
        else:
            print("   ✗ 简单公式渲染失败")
            return False
            
    except Exception as e:
        print(f"   ✗ matplotlib + TeX Live测试失败: {e}")
        plt.close('all')
        return False

def test_complex_formulas():
    """测试复杂公式"""
    print("\n3. 测试复杂LaTeX公式:")
    
    complex_formulas = [
        (r'$\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}$', "积分公式"),
        (r'$\begin{bmatrix} a & b \\ c & d \end{bmatrix}$', "矩阵"),
        (r'$\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}$', "无穷级数"),
        (r'$\oint_C \vec{F} \cdot d\vec{r} = \iint_S (\nabla \times \vec{F}) \cdot d\vec{S}$', "向量积分"),
    ]
    
    success_count = 0
    
    try:
        # 确保matplotlib配置正确
        matplotlib.rcParams['text.usetex'] = True
        
        for i, (formula, description) in enumerate(complex_formulas):
            try:
                fig, ax = plt.subplots(figsize=(6, 1.5))
                ax.text(0.5, 0.5, formula,
                       horizontalalignment='center',
                       verticalalignment='center',
                       transform=ax.transAxes,
                       fontsize=12)
                ax.axis('off')
                
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
                    temp_file = f.name
                
                plt.savefig(temp_file, dpi=300, bbox_inches='tight', transparent=True)
                plt.close(fig)
                
                if Path(temp_file).exists() and Path(temp_file).stat().st_size > 0:
                    print(f"   ✓ {description}: 成功 ({Path(temp_file).stat().st_size} bytes)")
                    success_count += 1
                    Path(temp_file).unlink()  # 清理
                else:
                    print(f"   ✗ {description}: 失败")
                    
            except Exception as e:
                print(f"   ✗ {description}: 异常 - {e}")
                plt.close('all')
        
        print(f"   复杂公式成功率: {success_count}/{len(complex_formulas)} ({success_count/len(complex_formulas)*100:.1f}%)")
        return success_count == len(complex_formulas)
        
    except Exception as e:
        print(f"   ✗ 复杂公式测试失败: {e}")
        return False

def test_performance_benchmark():
    """性能基准测试"""
    print("\n4. 性能基准测试:")
    
    test_formulas = [
        r'$E = mc^2$',
        r'$\frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$',
        r'$\int_0^1 x^2 dx = \frac{1}{3}$',
        r'$\sum_{k=0}^{n} \binom{n}{k} = 2^n$',
        r'$\lim_{x \to 0} \frac{\sin x}{x} = 1$',
        r'$\nabla \cdot \vec{E} = \frac{\rho}{\epsilon_0}$',
        r'$\mathbb{E}[X] = \int_{-\infty}^{\infty} x f(x) dx$',
        r'$\det(A) = \sum_{\sigma \in S_n} \text{sgn}(\sigma) \prod_{i=1}^n a_{i,\sigma(i)}$',
        r'$\zeta(s) = \sum_{n=1}^{\infty} \frac{1}{n^s}$',
        r'$\Gamma(n) = \int_0^{\infty} t^{n-1} e^{-t} dt$'
    ]
    
    try:
        matplotlib.rcParams['text.usetex'] = True
        
        start_time = time.time()
        success_count = 0
        processing_times = []
        
        for i, formula in enumerate(test_formulas):
            item_start = time.time()
            
            try:
                fig, ax = plt.subplots(figsize=(5, 1))
                ax.text(0.5, 0.5, formula,
                       horizontalalignment='center',
                       verticalalignment='center',
                       transform=ax.transAxes,
                       fontsize=12)
                ax.axis('off')
                
                with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
                    temp_file = f.name
                
                plt.savefig(temp_file, dpi=300, bbox_inches='tight', transparent=True)
                plt.close(fig)
                
                item_time = time.time() - item_start
                processing_times.append(item_time)
                
                if Path(temp_file).exists() and Path(temp_file).stat().st_size > 0:
                    print(f"   ✓ 公式 {i+1:2d}: {item_time:.3f}秒")
                    success_count += 1
                    Path(temp_file).unlink()
                else:
                    print(f"   ✗ 公式 {i+1:2d}: 渲染失败")
                    
            except Exception as e:
                item_time = time.time() - item_start
                print(f"   ✗ 公式 {i+1:2d}: 异常 ({item_time:.3f}秒) - {e}")
                plt.close('all')
        
        total_time = time.time() - start_time
        
        if success_count > 0:
            avg_time = sum(processing_times) / len(processing_times)
            rate = success_count / total_time
            
            print(f"\n   性能统计:")
            print(f"   - 成功数: {success_count}/{len(test_formulas)}")
            print(f"   - 总耗时: {total_time:.2f}秒")
            print(f"   - 平均时间: {avg_time:.3f}秒/公式")
            print(f"   - 处理速率: {rate:.1f}公式/秒")
            print(f"   - 预估速率: {rate*60:.0f}公式/分钟")
            
            # 与目标比较
            target_rate = 2000 / 60  # 2000张/分钟 = 33.3张/秒
            print(f"   - 目标速率: {target_rate:.1f}公式/秒")
            print(f"   - 达成率: {rate/target_rate*100:.1f}%")
            
            return success_count >= len(test_formulas) * 0.8  # 80%成功率
        else:
            print("   ✗ 所有公式都失败")
            return False
        
    except Exception as e:
        print(f"   ✗ 性能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("TeX Live直接功能测试")
    print("=" * 50)
    
    tests = [
        ("外部LaTeX命令", test_external_latex),
        ("matplotlib集成", test_matplotlib_with_texlive),
        ("复杂公式渲染", test_complex_formulas),
        ("性能基准", test_performance_benchmark),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"TeX Live功能测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 TeX Live完全可用！您现在拥有完整的LaTeX渲染功能")
        print("\n功能特性:")
        print("- ✅ 高质量LaTeX渲染")
        print("- ✅ 复杂数学公式支持")
        print("- ✅ 矩阵、积分、求和等高级功能")
        print("- ✅ 符合性能要求")
        
        print("\n下一步:")
        print("1. 设置永久PATH环境变量（避免每次手动设置）")
        print("2. 运行完整的批量处理测试")
        print("3. 处理您的实际数据")
        
        return 0
    elif passed >= 2:
        print("⚠ TeX Live基本可用，但存在一些问题")
        return 1
    else:
        print("❌ TeX Live存在严重问题")
        return 2

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
