#!/usr/bin/env python3
"""
基础功能测试
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.render_config import AppConfig, RenderConfig, ProcessConfig, OutputConfig, PreprocessingConfig
from preprocessing.normalizers.level1_basic import Level1BasicNormalizer
from preprocessing.validator import LaTeXValidator
from core.latex_processor import LaTeXProcessor
from core import LaTeXItem, ProcessStatus, ErrorType
from utils.file_utils import read_latex_file, create_output_dirs
from utils.string_utils import clean_latex_code, normalize_whitespace

class TestBasicFunctionality(unittest.TestCase):
    """基础功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_latex_codes = [
            r"x^2 + y^2 = z^2",
            r"\frac{1}{2}\pi r^2",
            r"\int_0^\infty e^{-x} dx",
            r"\sum_{i=1}^n i = \frac{n(n+1)}{2}",
            r"\begin{bmatrix} a & b \\ c & d \end{bmatrix}"
        ]
        
        # 创建测试配置
        self.config = AppConfig(
            render=RenderConfig(),
            process=ProcessConfig(),
            output=OutputConfig(),
            preprocessing=PreprocessingConfig()
        )
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_creation(self):
        """测试配置创建"""
        self.assertIsInstance(self.config, AppConfig)
        self.assertEqual(self.config.render.dpi, 300)
        self.assertEqual(self.config.process.max_workers, 16)
        self.assertEqual(self.config.output.images_subdir, "images")
        self.assertTrue(self.config.preprocessing.level1_enabled)
    
    def test_latex_item_creation(self):
        """测试LaTeX项目创建"""
        item = LaTeXItem(
            index=1,
            original_latex=r"x^2 + y^2 = z^2",
            category="math"
        )
        
        self.assertEqual(item.index, 1)
        self.assertEqual(item.original_latex, r"x^2 + y^2 = z^2")
        self.assertEqual(item.category, "math")
        self.assertEqual(item.normalized_latex, "")
    
    def test_level1_normalizer(self):
        """测试Level1规范化器"""
        normalizer = Level1BasicNormalizer()
        
        # 测试基本规范化
        test_cases = [
            (r"  x^2 + y^2 = z^2  ", r"x^2 + y^2 = z^2"),
            (r"x ^ 2", r"x^2"),
            (r"x _ i", r"x_i"),
            (r"$$x^2$$", r"x^2"),
            (r"\rm ABC", r"\mathrm{ABC}"),
        ]
        
        for input_latex, expected in test_cases:
            with self.subTest(input_latex=input_latex):
                result = normalizer.normalize(input_latex)
                self.assertIsInstance(result, str)
                # 基本检查：结果不应该为空
                self.assertTrue(len(result) > 0)
    
    def test_latex_validator(self):
        """测试LaTeX验证器"""
        validator = LaTeXValidator()
        
        # 测试有效的LaTeX代码
        valid_codes = [
            r"x^2 + y^2 = z^2",
            r"\frac{1}{2}",
            r"\int_0^1 x dx"
        ]
        
        for code in valid_codes:
            with self.subTest(code=code):
                is_valid, errors = validator.validate(code)
                # 基本验证应该通过或至少不崩溃
                self.assertIsInstance(is_valid, bool)
                self.assertIsInstance(errors, list)
        
        # 测试无效的LaTeX代码
        invalid_codes = [
            r"x^2 + y^2 = z^2}",  # 多余的右括号
            r"{x^2 + y^2 = z^2",  # 缺少右括号
        ]
        
        for code in invalid_codes:
            with self.subTest(code=code):
                is_valid, errors = validator.validate(code)
                self.assertIsInstance(is_valid, bool)
                self.assertIsInstance(errors, list)
    
    def test_latex_processor(self):
        """测试LaTeX处理器"""
        processor = LaTeXProcessor(self.config.preprocessing)
        
        for latex_code in self.test_latex_codes:
            with self.subTest(latex_code=latex_code):
                result = processor.normalize(latex_code)
                self.assertIsInstance(result, str)
                self.assertTrue(len(result) > 0)
    
    def test_file_utils(self):
        """测试文件工具函数"""
        # 创建测试文件
        test_file = Path(self.temp_dir) / "test_input.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            for code in self.test_latex_codes:
                f.write(code + '\n')
        
        # 测试读取文件
        read_codes = read_latex_file(str(test_file))
        self.assertEqual(len(read_codes), len(self.test_latex_codes))
        self.assertEqual(read_codes, self.test_latex_codes)
        
        # 测试创建输出目录
        output_dirs = create_output_dirs(self.temp_dir, self.config.output)
        self.assertIn('base', output_dirs)
        self.assertIn('images', output_dirs)
        self.assertIn('error_logs', output_dirs)
        
        # 验证目录确实被创建
        for dir_path in output_dirs.values():
            self.assertTrue(dir_path.exists())
            self.assertTrue(dir_path.is_dir())
    
    def test_string_utils(self):
        """测试字符串工具函数"""
        # 测试清理LaTeX代码
        test_cases = [
            ("  x^2 + y^2  ", "x^2 + y^2"),
            ("x^2\n\n\ny^2", "x^2\ny^2"),
            ("x^2   +   y^2", "x^2 + y^2"),
        ]
        
        for input_str, expected in test_cases:
            with self.subTest(input_str=input_str):
                result = clean_latex_code(input_str)
                self.assertEqual(result, expected)
        
        # 测试空白字符规范化
        test_cases = [
            ("  hello   world  ", "hello world"),
            ("hello\t\tworld", "hello world"),
            ("hello\n\nworld", "hello world"),
        ]
        
        for input_str, expected in test_cases:
            with self.subTest(input_str=input_str):
                result = normalize_whitespace(input_str)
                self.assertEqual(result, expected)
    
    def test_error_types(self):
        """测试错误类型枚举"""
        self.assertEqual(ProcessStatus.SUCCESS.value, "success")
        self.assertEqual(ProcessStatus.FAILED.value, "failed")
        self.assertEqual(ProcessStatus.SKIPPED.value, "skipped")
        
        self.assertEqual(ErrorType.ACCEPTABLE.value, "acceptable")
        self.assertEqual(ErrorType.UNACCEPTABLE.value, "unacceptable")
        self.assertEqual(ErrorType.UNKNOWN.value, "unknown")
    
    def test_empty_input_handling(self):
        """测试空输入处理"""
        processor = LaTeXProcessor(self.config.preprocessing)
        
        # 测试空字符串
        result = processor.normalize("")
        self.assertEqual(result, "")
        
        # 测试只有空白的字符串
        result = processor.normalize("   ")
        self.assertEqual(result, "")
        
        # 测试None输入（应该抛出异常或返回空字符串）
        try:
            result = processor.normalize(None)
            self.assertEqual(result, "")
        except (ValueError, TypeError):
            pass  # 这也是可接受的行为
    
    def test_batch_processing_data_structures(self):
        """测试批量处理数据结构"""
        from core import RenderResult, BatchResult
        
        # 创建测试项目
        item = LaTeXItem(
            index=1,
            original_latex=r"x^2",
            normalized_latex=r"x^2",
            category="math"
        )
        
        # 创建渲染结果
        result = RenderResult(
            item=item,
            status=ProcessStatus.SUCCESS,
            image_path="/path/to/image.png",
            processing_time=0.5
        )
        
        self.assertEqual(result.item.index, 1)
        self.assertEqual(result.status, ProcessStatus.SUCCESS)
        self.assertEqual(result.processing_time, 0.5)
        
        # 创建批次结果
        batch_result = BatchResult(
            batch_id=1,
            total_count=1,
            success_count=1,
            failed_count=0,
            results=[result],
            processing_time=1.0
        )
        
        self.assertEqual(batch_result.batch_id, 1)
        self.assertEqual(batch_result.total_count, 1)
        self.assertEqual(batch_result.success_count, 1)
        self.assertEqual(len(batch_result.results), 1)

class TestIntegration(unittest.TestCase):
    """集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = AppConfig(
            render=RenderConfig(),
            process=ProcessConfig(max_workers=2, batch_size=10),  # 小批次用于测试
            output=OutputConfig(),
            preprocessing=PreprocessingConfig()
        )
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_end_to_end_processing_simulation(self):
        """端到端处理模拟测试"""
        # 创建测试输入文件
        test_file = Path(self.temp_dir) / "test_input.txt"
        test_codes = [
            r"x^2 + y^2 = z^2",
            r"\frac{1}{2}",
            r"E = mc^2"
        ]
        
        with open(test_file, 'w', encoding='utf-8') as f:
            for code in test_codes:
                f.write(code + '\n')
        
        # 测试文件读取
        read_codes = read_latex_file(str(test_file))
        self.assertEqual(len(read_codes), 3)
        
        # 测试预处理
        processor = LaTeXProcessor(self.config.preprocessing)
        normalized_codes = []
        for code in read_codes:
            normalized = processor.normalize(code)
            normalized_codes.append(normalized)
            self.assertIsInstance(normalized, str)
        
        # 测试输出目录创建
        output_dirs = create_output_dirs(self.temp_dir, self.config.output)
        self.assertTrue(output_dirs['images'].exists())
        self.assertTrue(output_dirs['error_logs'].exists())
        
        # 验证处理统计
        stats = processor.get_processing_stats(read_codes, normalized_codes)
        self.assertIn('total_processed', stats)
        self.assertEqual(stats['total_processed'], 3)

def run_tests():
    """运行所有测试"""
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestBasicFunctionality))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 返回测试结果
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
