# TeX Live环境设置脚本（临时设置，不需要重启）

param(
    [string]$TexLivePath = "D:\texlive_portable\texlive"
)

Write-Host "TeX Live环境设置" -ForegroundColor Green
Write-Host "================" -ForegroundColor Green

# 检查TeX Live安装
Write-Host "`n1. 检查TeX Live安装..." -ForegroundColor Yellow
if (!(Test-Path $TexLivePath)) {
    Write-Host "   ✗ TeX Live路径不存在: $TexLivePath" -ForegroundColor Red
    Write-Host "   请先安装TeX Live或指定正确路径" -ForegroundColor Yellow
    exit 1
}

# 查找bin目录
$binDirs = @(
    Join-Path $TexLivePath "bin\windows",
    Join-Path $TexLivePath "bin\win32",
    Join-Path $TexLivePath "bin\x86_64-w64-mingw32"
)

$texBinPath = $null
foreach ($binDir in $binDirs) {
    if (Test-Path $binDir) {
        $texBinPath = $binDir
        break
    }
}

if (!$texBinPath) {
    Write-Host "   ✗ 未找到TeX Live bin目录" -ForegroundColor Red
    exit 1
}

Write-Host "   ✓ 找到TeX Live bin目录: $texBinPath" -ForegroundColor Green

# 临时设置PATH（仅对当前会话有效）
Write-Host "`n2. 设置临时PATH..." -ForegroundColor Yellow
$env:PATH = "$texBinPath;$env:PATH"
Write-Host "   ✓ 已将TeX Live添加到当前会话PATH" -ForegroundColor Green

# 验证安装
Write-Host "`n3. 验证TeX Live安装..." -ForegroundColor Yellow
$commands = @("latex", "pdflatex", "tlmgr")

foreach ($cmd in $commands) {
    try {
        $result = & $cmd --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            $version = ($result | Select-Object -First 1)
            Write-Host "   ✓ $cmd`: $version" -ForegroundColor Green
        } else {
            Write-Host "   ✗ $cmd`: 命令失败" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ✗ $cmd`: 未找到命令" -ForegroundColor Red
    }
}

# 安装必需的包
Write-Host "`n4. 安装必需的LaTeX包..." -ForegroundColor Yellow
$packages = @(
    "amsmath", "amsfonts", "amssymb", "mathtools", 
    "xcolor", "bm", "type1cm", "inputenc", "fontenc"
)

foreach ($package in $packages) {
    try {
        Write-Host "   正在安装 $package..." -ForegroundColor Cyan
        & tlmgr install $package 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "   ✓ $package 安装成功" -ForegroundColor Green
        } else {
            Write-Host "   ⚠ $package 可能已安装或安装失败" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   ✗ $package 安装失败" -ForegroundColor Red
    }
}

# 创建环境设置脚本
Write-Host "`n5. 创建环境设置脚本..." -ForegroundColor Yellow
$envScript = @"
# TeX Live环境设置（每次使用前运行）
`$env:PATH = "$texBinPath;`$env:PATH"
Write-Host "TeX Live环境已设置" -ForegroundColor Green
"@

$envScriptPath = Join-Path (Get-Location) "set_texlive_env.ps1"
$envScript | Out-File -FilePath $envScriptPath -Encoding UTF8
Write-Host "   ✓ 环境脚本已创建: $envScriptPath" -ForegroundColor Green

Write-Host "`n设置完成!" -ForegroundColor Green
Write-Host "使用说明:" -ForegroundColor Yellow
Write-Host "1. 每次使用前运行: .\set_texlive_env.ps1" -ForegroundColor White
Write-Host "2. 或者直接在当前会话中使用（PATH已设置）" -ForegroundColor White
Write-Host "3. 运行验证脚本: python verify_texlive.py" -ForegroundColor White
