#!/usr/bin/env python3
"""
环境测试脚本
验证所有依赖和基本功能
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print("1. Python版本检查:")
    print(f"   Python版本: {sys.version}")
    if sys.version_info >= (3, 8):
        print("   ✓ Python版本符合要求 (>=3.8)")
        return True
    else:
        print("   ✗ Python版本过低，需要3.8+")
        return False

def test_required_packages():
    """测试必需的包"""
    print("\n2. 必需包检查:")
    
    packages = [
        ('matplotlib', 'matplotlib'),
        ('numpy', 'numpy'), 
        ('PIL', 'Pillow'),
        ('yaml', 'PyYAML'),
        ('psutil', 'psutil'),
        ('sympy', 'sympy')
    ]
    
    all_ok = True
    for import_name, package_name in packages:
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"   ✓ {package_name}: {version}")
        except ImportError as e:
            print(f"   ✗ {package_name}: 未安装 ({e})")
            all_ok = False
    
    return all_ok

def test_latex_installation():
    """测试LaTeX安装"""
    print("\n3. LaTeX安装检查:")
    
    import subprocess
    
    commands = [
        ('latex', 'LaTeX'),
        ('pdflatex', 'pdfLaTeX')
    ]
    
    all_ok = True
    for cmd, name in commands:
        try:
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # 提取版本信息的第一行
                version_line = result.stdout.split('\n')[0]
                print(f"   ✓ {name}: {version_line}")
            else:
                print(f"   ✗ {name}: 命令执行失败")
                all_ok = False
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            print(f"   ✗ {name}: 未找到或超时 ({e})")
            all_ok = False
    
    return all_ok

def test_matplotlib_latex():
    """测试matplotlib的LaTeX支持"""
    print("\n4. matplotlib LaTeX支持检查:")

    try:
        import matplotlib
        import matplotlib.pyplot as plt

        # 检查是否有外部LaTeX
        has_external_latex = False
        try:
            import subprocess
            result = subprocess.run(['latex', '--version'],
                                  capture_output=True, text=True, timeout=5)
            has_external_latex = (result.returncode == 0)
        except:
            pass

        if has_external_latex:
            print("   ✓ 检测到外部LaTeX，使用usetex=True")
            matplotlib.rcParams['text.usetex'] = True
        else:
            print("   ⚠ 未检测到外部LaTeX，使用内置mathtext")
            matplotlib.rcParams['text.usetex'] = False
            matplotlib.rcParams['mathtext.fontset'] = 'cm'

        print("   ✓ matplotlib LaTeX配置成功")

        # 创建简单测试图形
        fig, ax = plt.subplots(figsize=(2, 1))
        ax.text(0.5, 0.5, r'$x^2 + y^2 = z^2$',
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes)
        ax.axis('off')

        # 保存到临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            temp_file = f.name

        plt.savefig(temp_file, dpi=150, bbox_inches='tight')
        plt.close(fig)

        # 检查文件是否创建成功
        if Path(temp_file).exists() and Path(temp_file).stat().st_size > 0:
            print("   ✓ LaTeX渲染测试成功")
            os.unlink(temp_file)  # 清理临时文件
            return True
        else:
            print("   ✗ LaTeX渲染测试失败：文件未生成")
            return False

    except Exception as e:
        print(f"   ✗ LaTeX渲染测试失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n5. 基本功能测试:")
    
    try:
        # 测试配置系统
        sys.path.insert(0, str(Path(__file__).parent))
        from config.render_config import AppConfig, load_config
        
        config = load_config()
        print(f"   ✓ 配置系统: DPI={config.render.dpi}, Workers={config.process.max_workers}")
        
        # 测试LaTeX处理（简化版）
        test_latex = r"x^2 + y^2 = z^2"
        
        # 基本字符串处理
        cleaned = test_latex.strip()
        if cleaned == test_latex:
            print("   ✓ 基本LaTeX处理")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 基本功能测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n6. 文件操作测试:")
    
    try:
        import tempfile
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # 测试目录创建
            test_dirs = ['images', 'error_logs', 'temp']
            for dir_name in test_dirs:
                (temp_path / dir_name).mkdir(exist_ok=True)
            
            print("   ✓ 目录创建")
            
            # 测试文件写入
            test_file = temp_path / 'test.txt'
            test_content = "x^2 + y^2 = z^2\n\\frac{1}{2}\\pi r^2"
            
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(test_content)
            
            # 测试文件读取
            with open(test_file, 'r', encoding='utf-8') as f:
                read_content = f.read()
            
            if read_content == test_content:
                print("   ✓ 文件读写")
            else:
                print("   ✗ 文件读写失败")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ✗ 文件操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("LaTeX2PNG 环境配置检查")
    print("=" * 50)
    
    tests = [
        ("Python版本", test_python_version),
        ("必需包", test_required_packages),
        ("LaTeX安装", test_latex_installation),
        ("matplotlib LaTeX支持", test_matplotlib_latex),
        ("基本功能", test_basic_functionality),
        ("文件操作", test_file_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ✗ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"环境检查结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 环境配置完全正确！可以开始使用LaTeX2PNG转换器")
        
        print("\n建议的下一步:")
        print("1. 运行渲染引擎测试:")
        print('   python main.py --test-engines')
        print("2. 处理示例文件:")
        print('   python main.py examples/sample_input.txt --output-dir ./test_output')
        
        return 0
    else:
        print("❌ 环境配置存在问题，请根据上述检查结果进行修复")
        
        print("\n常见问题解决方案:")
        if passed < 3:  # 基础环境问题
            print("- 确保Python 3.8+已安装")
            print("- 安装缺失的包: pip install matplotlib numpy pillow pyyaml psutil sympy")
            print("- 确保LaTeX发行版已正确安装并在PATH中")
        
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
