# 检查MiKTeX清理结果
Write-Host "检查MiKTeX清理结果" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

# 检查PATH
Write-Host "`n1. 检查PATH环境变量:" -ForegroundColor Yellow
$pathEntries = $env:PATH -split ';'
$miktexInPath = $pathEntries | Where-Object { $_ -like '*miktex*' }

if ($miktexInPath.Count -eq 0) {
    Write-Host "  ✓ PATH中已无MiKTeX条目" -ForegroundColor Green
} else {
    Write-Host "  ✗ PATH中仍有MiKTeX条目:" -ForegroundColor Red
    foreach ($entry in $miktexInPath) {
        Write-Host "    - $entry" -ForegroundColor Red
    }
}

# 检查latex命令
Write-Host "`n2. 检查latex命令:" -ForegroundColor Yellow
try {
    $latexCmd = Get-Command latex -ErrorAction SilentlyContinue
    if ($latexCmd) {
        Write-Host "  ✗ 仍能找到latex命令: $($latexCmd.Source)" -ForegroundColor Red
        if ($latexCmd.Source -like '*miktex*') {
            Write-Host "    这是MiKTeX的latex命令，需要进一步清理" -ForegroundColor Red
        }
    } else {
        Write-Host "  ✓ latex命令已不可用（正常，等待安装TeX Live）" -ForegroundColor Green
    }
} catch {
    Write-Host "  ✓ latex命令已不可用（正常，等待安装TeX Live）" -ForegroundColor Green
}

# 检查MiKTeX安装目录
Write-Host "`n3. 检查MiKTeX安装目录:" -ForegroundColor Yellow
$miktexDirs = @(
    "D:\Program Files\MiKTeX",
    "C:\Program Files\MiKTeX",
    "$env:LOCALAPPDATA\Programs\MiKTeX"
)

$foundDirs = @()
foreach ($dir in $miktexDirs) {
    if (Test-Path $dir) {
        $foundDirs += $dir
        Write-Host "  ⚠ 发现MiKTeX目录: $dir" -ForegroundColor Yellow
    }
}

if ($foundDirs.Count -eq 0) {
    Write-Host "  ✓ 未发现MiKTeX安装目录" -ForegroundColor Green
} else {
    Write-Host "  建议手动删除这些目录（如果卸载程序未完全清理）" -ForegroundColor Yellow
}

Write-Host "`n清理检查完成!" -ForegroundColor Green
Write-Host "如果所有检查都通过，可以开始安装TeX Live" -ForegroundColor Green
