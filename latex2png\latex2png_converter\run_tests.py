#!/usr/bin/env python3
"""
测试运行脚本
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def run_basic_tests():
    """运行基础测试"""
    print("运行基础功能测试...")
    try:
        from tests.test_basic import run_tests
        success = run_tests()
        if success:
            print("✓ 基础测试通过")
            return True
        else:
            print("✗ 基础测试失败")
            return False
    except Exception as e:
        print(f"✗ 基础测试异常: {e}")
        return False

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    modules_to_test = [
        'config.render_config',
        'core.latex_processor',
        'core.render_engine',
        'core.batch_manager',
        'core.output_manager',
        'core.error_handler',
        'preprocessing.normalizers.level1_basic',
        'preprocessing.validator',
        'engines.texlive_engine',
        'engines.sympy_engine',
        'utils.file_utils',
        'utils.string_utils',
        'utils.image_utils',
        'utils.performance_utils',
        'monitoring.logger',
        'monitoring.progress_tracker',
        'monitoring.metrics',
        'cli_parser',
        'main'
    ]
    
    failed_imports = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"  ✓ {module_name}")
        except Exception as e:
            print(f"  ✗ {module_name}: {e}")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"导入失败的模块: {failed_imports}")
        return False
    else:
        print("✓ 所有模块导入成功")
        return True

def test_config_creation():
    """测试配置创建"""
    print("测试配置创建...")
    try:
        from config.render_config import AppConfig, load_config, create_default_config_file
        
        # 测试默认配置
        config = load_config()
        assert config.render.dpi == 300
        assert config.process.max_workers == 16
        print("  ✓ 默认配置创建成功")
        
        # 测试配置文件创建
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.yaml', delete=False) as f:
            config_file = f.name
        
        try:
            created_file = create_default_config_file(config_file)
            assert Path(created_file).exists()
            print("  ✓ 配置文件创建成功")
            
            # 测试从文件加载配置
            loaded_config = load_config(created_file)
            assert loaded_config.render.dpi == 300
            print("  ✓ 配置文件加载成功")
            
        finally:
            # 清理临时文件
            try:
                os.unlink(config_file)
            except:
                pass
        
        print("✓ 配置系统测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置系统测试失败: {e}")
        return False

def test_cli_parser():
    """测试命令行解析器"""
    print("测试命令行解析器...")
    try:
        from cli_parser import parse_args, create_config_from_args
        
        # 创建临时输入文件
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("x^2 + y^2 = z^2\n")
            temp_file = f.name
        
        try:
            # 测试基本参数解析
            args = parse_args([temp_file])
            assert args.input_file == temp_file
            print("  ✓ 基本参数解析成功")
            
            # 测试配置创建
            config = create_config_from_args(args)
            assert config.render.dpi == 300
            print("  ✓ 从参数创建配置成功")
            
            # 测试特殊命令
            args = parse_args(['--create-config'])
            print("  ✓ 特殊命令解析成功")
            
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file)
            except:
                pass
        
        print("✓ 命令行解析器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 命令行解析器测试失败: {e}")
        return False

def test_latex_processing():
    """测试LaTeX处理功能"""
    print("测试LaTeX处理功能...")
    try:
        from config.render_config import PreprocessingConfig
        from core.latex_processor import LaTeXProcessor
        from preprocessing.normalizers.level1_basic import Level1BasicNormalizer
        from preprocessing.validator import LaTeXValidator
        
        # 测试规范化器
        normalizer = Level1BasicNormalizer()
        result = normalizer.normalize(r"  x^2 + y^2 = z^2  ")
        assert isinstance(result, str)
        assert len(result) > 0
        print("  ✓ Level1规范化器工作正常")
        
        # 测试验证器
        validator = LaTeXValidator()
        is_valid, errors = validator.validate(r"x^2 + y^2 = z^2")
        assert isinstance(is_valid, bool)
        assert isinstance(errors, list)
        print("  ✓ LaTeX验证器工作正常")
        
        # 测试处理器
        config = PreprocessingConfig()
        processor = LaTeXProcessor(config)
        result = processor.normalize(r"x^2 + y^2 = z^2")
        assert isinstance(result, str)
        print("  ✓ LaTeX处理器工作正常")
        
        print("✓ LaTeX处理功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ LaTeX处理功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("LaTeX2PNG 转换器测试套件")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置系统", test_config_creation),
        ("命令行解析", test_cli_parser),
        ("LaTeX处理", test_latex_processing),
        ("基础功能", run_basic_tests),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
