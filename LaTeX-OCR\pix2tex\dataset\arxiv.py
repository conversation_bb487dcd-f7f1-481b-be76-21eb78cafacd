# 修改自 https://github.com/soskek/arxiv_leaks
# 用于从arXiv网站批量下载论文并提取其中的LaTeX数学公式

# ==================== 配置区域 ====================
# 在这里修改所有参数，无需使用命令行
#
# 🚀 快速开始 - 获取10000篇physics论文:
# 1. 确保 CONFIG['mode'] = 'bulk'
# 2. 确保 BULK_CONFIG['target_paper_count'] = 10000
# 3. 确保 BULK_CONFIG['primary_category'] = 'physics'
# 4. 运行: python arxiv.py
#
# ⚠️  注意事项:
# - 下载10000篇论文大约需要8-12小时 (受arXiv API速率限制)
# - 程序支持断点续传，可以随时中断后重新运行
# - 建议在网络稳定的环境下运行
# - 每3.5秒下载一篇论文，遵守arXiv API限制

# 基础配置
CONFIG = {
    # 工作模式选择 (必须选择其中一种):
    # 'top'  - 自动获取arXiv最新发布的论文，按时间排序获取最新的N篇
    # 'bulk' - 大批量下载模式，支持跨分类、跨时间段获取大量论文 (新增)
    # 'ids'  - 手动指定要下载的论文ID列表，精确控制要处理哪些论文
    # 'dirs' - 处理本地已有的论文文件夹，适合处理之前下载的.tar.gz文件
    'mode': 'bulk',
    
    # 输出目录 - 提取的数学公式和处理记录将保存到此目录
    # 会生成文件: visited_arxiv.txt(已处理论文列表), math_arxiv.txt(提取的公式), progress.json(进度信息)
    'output_dir': r'C:\dataset\arxiv_physics',
    
    # 论文文件保存设置:
    # None        - 临时保存，处理完后自动删除，节省磁盘空间
    # './papers'  - 永久保存到指定目录，方便后续重复使用
    # 建议: 如果只是提取公式用None，如果要建立论文库用路径
    # 'save_papers': r"D:\formula_ge_dataset\arxiv_50\papers",
    'save_papers': None,
    # demacro处理 (高级选项，一般保持False):
    # True  - 使用de-macro工具处理LaTeX宏定义，可能提高公式提取质量但很慢
    # False - 不使用，推荐设置，因为该功能已基本弃用
    # 注意: 设为True需要额外安装de-macro工具
    'use_demacro': False,
    
    # 详细日志输出:
    # True  - 显示下载进度、错误信息等详细信息，便于调试
    # False - 只显示关键信息，适合批量处理时减少输出
    'verbose': True,
}

# TOP模式专用配置 - 当CONFIG['mode'] = 'top'时生效
TOP_CONFIG = {
    # 要获取的论文数量
    # 注意: arXiv只接受以下数值: 25, 50, 100, 250, 500, 1000, 2000
    # 不能使用其他数值(如10)，否则会返回"Invalid show value"错误
    # 如果你想处理少于25篇论文，可以设置25然后在后续处理中限制数量
    'paper_count': 1000,

    # arXiv论文分类 (选择你感兴趣的领域):
    # 'physics'     - 物理学 (包含大量数学公式)
    # 'math'        - 数学 (纯数学论文，公式密度最高)
    # 'cs'          - 计算机科学 (算法、机器学习等)
    # 'stat'        - 统计学 (统计方法、概率论等)
    # 'q-bio'       - 量化生物学 (生物数学模型等)
    # 'q-fin'       - 量化金融 (金融数学模型等)
    # 'econ'        - 经济学 (经济数学模型等)
    'category': 'physics',
}

# BULK模式专用配置 - 新增大批量下载模式
BULK_CONFIG = {
    # 目标论文总数
    'target_paper_count': 10000,

    # 主要搜索的分类 (专注单个领域)
    'primary_category': 'physics',  # 可选: 'math', 'physics', 'cs', 'stat', 'q-bio', 'q-fin', 'econ'

    # 每批次最大论文数 (受arXiv API限制)
    'batch_size': 2000,

    # 批次间延迟时间(秒) - 遵守API速率限制
    'batch_delay': 10,

    # 单个论文下载间隔(秒) - 遵守3秒/请求限制
    'download_delay': 3.5,

    # 最大重试次数
    'max_retries': 5,

    # 深度时间搜索策略 - 从最新到最旧逐年搜索
    'search_years': [2024, 2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015, 2014, 2013, 2012, 2011, 2010],

    # 每年内的时间分片策略 (用于获取更多论文)
    # arXiv按月份分页，我们可以按月搜索来获取一年内的所有论文
    'monthly_search': True,  # 是否按月搜索

    # 是否启用智能去重 (避免重复下载相同论文)
    'enable_deduplication': True,

    # 是否启用子分类搜索 (physics有很多子分类)
    'enable_subcategories': True,

    # physics的主要子分类 (按数学公式密度排序)
    'physics_subcategories': [
        'math-ph',      # 数学物理 (公式密度最高)
        'hep-th',       # 高能物理理论
        'gr-qc',        # 广义相对论和量子宇宙学
        'quant-ph',     # 量子物理
        'cond-mat',     # 凝聚态物理
        'hep-ph',       # 高能物理现象学
        'nucl-th',      # 核理论
        'astro-ph',     # 天体物理
        'physics',      # 其他物理
    ],

    # math的主要子分类
    'math_subcategories': [
        'math.AP',      # 分析偏微分方程
        'math.DG',      # 微分几何
        'math.AG',      # 代数几何
        'math.NT',      # 数论
        'math.RT',      # 表示论
        'math.QA',      # 量子代数
        'math.MP',      # 数学物理
        'math.PR',      # 概率论
        'math.ST',      # 统计理论
    ],
}

# IDS模式专用配置 - 当CONFIG['mode'] = 'ids'时生效  
IDS_CONFIG = {
    # 指定要处理的arXiv论文ID列表
    # ID格式: YYMM.NNNNN (如: 2301.12345 表示2023年1月的第12345篇论文)
    # 获取方式: 
    # 1. 从arXiv网站论文页面URL中获取 (如: arxiv.org/abs/2301.12345)
    # 2. 从论文引用中获取
    # 3. 从其他研究者分享的论文列表中获取
    'arxiv_ids': [
        '2301.12345',  # 示例ID，请替换为实际需要的论文ID
        '2301.67890',  # 可以添加任意数量的ID
        # '2302.11111',  # 取消注释并添加更多ID
        # '2302.22222',
        # 在这里继续添加更多论文ID...
    ]
}

# DIRS模式专用配置 - 当CONFIG['mode'] = 'dirs'时生效
DIRS_CONFIG = {
    # 包含论文文件的本地目录列表
    # 支持的文件格式: .tar.gz (arXiv标准格式) 和 .tex (纯LaTeX文件)
    # 使用场景:
    # 1. 之前已经下载过的arXiv论文文件
    # 2. 从其他来源获得的LaTeX论文源码
    # 3. 需要重新处理的论文文件
    'directories': [
        './papers',              # 相对路径示例
        './downloaded_papers',   # 另一个目录
        # '/absolute/path/papers', # 绝对路径示例
        # 'D:/research/papers',    # Windows路径示例
        # 在这里添加更多目录路径...
    ]
}

# ==================== 高级配置选项 ====================
# 一般情况下不需要修改以下配置

ADVANCED_CONFIG = {
    # 下载重试次数 - 网络不稳定时增加此值
    'max_retries': 3,
    
    # 下载超时时间(秒) - 网络慢时可以增加
    'download_timeout': 30,
    
    # 并发下载数量 - 可以加快下载速度，但不要设置太高避免被封IP
    'concurrent_downloads': 1,
    
    # 是否跳过已处理的论文 - True可以避免重复处理
    'skip_processed': True,
}

# ==================== 代码实现区域 ====================

import argparse
import subprocess
import os
import glob
import re
import sys
import logging
import tarfile
import tempfile
import requests
import urllib.request
import time
import json
from datetime import datetime, timedelta
from tqdm import tqdm
from urllib.error import HTTPError
from pix2tex.dataset.extract_latex import find_math
from pix2tex.dataset.scraping import recursive_search
from pix2tex.dataset.demacro import *

# 设置日志级别
if CONFIG['verbose']:
    logging.getLogger().setLevel(logging.INFO)

# 正则表达式：匹配arXiv论文ID格式（如：2301.12345）
arxiv_id = re.compile(r'(?<!\d)(\d{4}\.\d{5})(?!\d)')
# arXiv论文下载的基础URL
arxiv_base = 'https://export.arxiv.org/e-print/'


def get_all_arxiv_ids(text):
    '''从给定文本中提取所有arXiv论文ID
    
    Args:
        text (str): 包含arXiv ID的文本内容
        
    Returns:
        list: 去重后的arXiv ID列表
    '''
    ids = []
    # 使用正则表达式查找所有匹配的ID
    for id in arxiv_id.findall(text):
        ids.append(id)
    # 返回去重后的ID列表
    return list(set(ids))


def download(url, dir_path='./', max_retries=3):
    '''从指定URL下载文件到本地目录，支持重试机制

    Args:
        url (str): 下载链接
        dir_path (str): 保存目录，默认为当前目录
        max_retries (int): 最大重试次数

    Returns:
        str or int: 成功返回文件路径，失败返回0
    '''
    # 从URL中提取文件名（论文ID）
    idx = os.path.split(url)[-1]
    file_name = idx + '.tar.gz'
    file_path = os.path.join(dir_path, file_name)

    # 如果文件已存在，直接返回路径
    if os.path.exists(file_path):
        return file_path

    if CONFIG['verbose']:
        print(f'\t正在下载 {url}')

    # 重试机制
    for attempt in range(max_retries):
        try:
            # 使用urllib下载文件
            r = urllib.request.urlretrieve(url, file_path)
            return r[0]
        except HTTPError as e:
            if CONFIG['verbose']:
                print(f'下载失败 (尝试 {attempt + 1}/{max_retries}): HTTP {e.code}')
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
            else:
                if CONFIG['verbose']:
                    print(f'无法下载 {url}')
                return 0
        except Exception as e:
            if CONFIG['verbose']:
                print(f'下载异常 (尝试 {attempt + 1}/{max_retries}): {e}')
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # 指数退避
            else:
                if CONFIG['verbose']:
                    print(f'无法下载 {url}')
                return 0


def read_tex_files(file_path: str, demacro: bool = False) -> str:
    """读取LaTeX源文件中的所有.tex文件内容
    
    如果不是tar.gz文件，则尝试作为普通文本文件读取
    
    Args:
        file_path (str): LaTeX源文件路径
        demacro (bool, optional): 已弃用。是否调用外部de-macro程序。默认False
        
    Returns:
        str: 所有LaTeX文件内容拼接成的字符串
    """    
    tex = ''
    try:
        # 创建临时目录用于解压文件
        with tempfile.TemporaryDirectory() as tempdir:
            try:
                # 尝试打开tar.gz压缩文件
                tf = tarfile.open(file_path, 'r')
                tf.extractall(tempdir)  # 解压到临时目录
                tf.close()
                # 递归查找所有.tex文件
                texfiles = [os.path.abspath(x) for x in glob.glob(os.path.join(tempdir, '**', '*.tex'), recursive=True)]
            except tarfile.ReadError as e:
                # 如果不是压缩文件，直接作为tex文件处理
                texfiles = [file_path]
                
            # 如果启用demacro（已弃用功能）
            if demacro:
                ret = subprocess.run(['de-macro', *texfiles], cwd=tempdir, capture_output=True)
                if ret.returncode == 0:
                    texfiles = glob.glob(os.path.join(tempdir, '**', '*-clean.tex'), recursive=True)
                    
            # 读取所有tex文件内容
            for texfile in texfiles:
                try:
                    ct = open(texfile, 'r', encoding='utf-8').read()
                    tex += ct
                except UnicodeDecodeError as e:
                    logging.debug(e)
                    pass
    except Exception as e:
        logging.debug('无法读取 %s: %s' % (file_path, str(e)))
        raise e
    
    # 使用pydemacro处理LaTeX宏定义
    tex = pydemacro(tex)
    return tex


def download_paper(arxiv_id, dir_path='./', max_retries=None):
    '''根据arXiv ID下载论文，支持重试机制

    Args:
        arxiv_id (str): arXiv论文ID
        dir_path (str): 保存目录
        max_retries (int): 最大重试次数，None则使用配置中的值

    Returns:
        str or int: 下载的文件路径，失败返回0
    '''
    url = arxiv_base + arxiv_id
    if max_retries is None:
        max_retries = BULK_CONFIG.get('max_retries', 3)
    return download(url, dir_path, max_retries)


def read_paper(targz_path, delete=False, demacro=False):
    '''读取已下载的论文文件内容
    
    Args:
        targz_path (str or int): 论文文件路径，0表示下载失败
        delete (bool): 读取后是否删除文件
        demacro (bool): 是否使用demacro处理
        
    Returns:
        str: 论文的LaTeX内容
    '''
    paper = ''
    if targz_path != 0:
        paper = read_tex_files(targz_path, demacro=demacro)
        if delete:
            os.remove(targz_path)
    return paper


def parse_arxiv(id, save=None, demacro=True):
    '''解析单篇arXiv论文，提取其中的数学公式

    Args:
        id (str): arXiv论文ID
        save (str, optional): 保存下载文件的目录，None表示使用临时目录
        demacro (bool): 是否使用demacro处理

    Returns:
        tuple: (数学公式列表, 空列表)
    '''
    if save is None:
        dir = tempfile.gettempdir()  # 使用系统临时目录
    else:
        dir = save

    # 下载论文 -> 读取内容 -> 提取数学公式
    text = read_paper(download_paper(id, dir), delete=save is None, demacro=demacro)

    # 使用find_math函数提取数学公式，wiki=False表示不是维基百科格式
    return find_math(text, wiki=False), []


def get_arxiv_ids_from_url(url, max_retries=3):
    '''从arXiv列表页面获取论文ID列表

    Args:
        url (str): arXiv列表页面URL
        max_retries (int): 最大重试次数

    Returns:
        list: 论文ID列表
    '''
    for attempt in range(max_retries):
        try:
            if CONFIG['verbose']:
                print(f'正在获取论文列表: {url}')
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            ids = get_all_arxiv_ids(response.text)
            if CONFIG['verbose']:
                print(f'从页面获取到 {len(ids)} 篇论文')
            return ids
        except Exception as e:
            if CONFIG['verbose']:
                print(f'获取论文列表失败 (尝试 {attempt + 1}/{max_retries}): {e}')
            if attempt < max_retries - 1:
                time.sleep(5)  # 等待5秒后重试
            else:
                print(f'无法获取论文列表: {url}')
                return []


def build_single_category_urls(category, years, batch_size, enable_subcategories=False, monthly_search=False):
    '''为单个分类构建深度搜索URL列表

    Args:
        category (str): 主分类名称
        years (list): 要搜索的年份列表
        batch_size (int): 每批次大小
        enable_subcategories (bool): 是否启用子分类搜索
        monthly_search (bool): 是否按月搜索

    Returns:
        list: (分类, 时间描述, URL) 元组列表
    '''
    urls = []

    # 获取子分类列表
    subcategories = []
    if enable_subcategories:
        if category == 'physics':
            subcategories = BULK_CONFIG['physics_subcategories']
        elif category == 'math':
            subcategories = BULK_CONFIG['math_subcategories']
        else:
            subcategories = [category]  # 其他分类暂不支持子分类
    else:
        subcategories = [category]

    # 为每个子分类和年份构建URL
    for subcat in subcategories:
        for year in years:
            if monthly_search and year >= 2007:  # arXiv 2007年后支持月份搜索
                # 按月搜索该年份
                for month in range(1, 13):
                    month_str = f"{year:04d}{month:02d}"
                    time_desc = f"{year}年{month}月"

                    # 构建月份搜索URL
                    url = f'https://arxiv.org/list/{subcat}/{month_str}?skip=0&show={batch_size}'
                    urls.append((subcat, time_desc, url))

                    # 如果该月论文很多，可能需要分页
                    # 先添加基础URL，后续可以根据实际情况添加分页

            else:
                # 按年搜索
                time_desc = f"{year}年"
                url = f'https://arxiv.org/list/{subcat}/{year}?skip=0&show={batch_size}'
                urls.append((subcat, time_desc, url))

    return urls


def build_paginated_urls(base_url, estimated_total, batch_size):
    '''为可能有多页的搜索构建分页URL

    Args:
        base_url (str): 基础URL
        estimated_total (int): 估计的总论文数
        batch_size (int): 每页大小

    Returns:
        list: 分页URL列表
    '''
    urls = []

    # 计算需要的页数
    pages_needed = (estimated_total + batch_size - 1) // batch_size

    for page in range(pages_needed):
        skip = page * batch_size
        paginated_url = base_url.replace('skip=0', f'skip={skip}')
        urls.append(paginated_url)

    return urls


def get_category_paper_count(category, year, month=None):
    '''估算某个分类在特定时间的论文数量

    Args:
        category (str): 分类名称
        year (int): 年份
        month (int, optional): 月份

    Returns:
        int: 估算的论文数量
    '''
    # 这是一个简单的估算函数，基于经验数据
    # 实际使用中可以通过先发送小批量请求来获取准确数量

    base_counts = {
        'physics': 3000,    # physics每年约3000篇
        'math': 2000,       # math每年约2000篇
        'cs': 4000,         # cs每年约4000篇
        'stat': 800,        # stat每年约800篇
        'math-ph': 400,     # 数学物理每年约400篇
        'hep-th': 600,      # 高能理论每年约600篇
        'gr-qc': 300,       # 广义相对论每年约300篇
        'quant-ph': 800,    # 量子物理每年约800篇
    }

    yearly_count = base_counts.get(category, 1000)  # 默认1000篇/年

    if month is not None:
        return yearly_count // 12  # 按月平均分配
    else:
        return yearly_count


def save_progress(output_dir, visited_ids, math_formulas, current_batch=0, total_target=0, current_url_index=0, total_urls=0):
    '''保存当前进度到文件，支持断点续传

    Args:
        output_dir (str): 输出目录
        visited_ids (list): 已处理的论文ID列表
        math_formulas (list): 已提取的数学公式列表
        current_batch (int): 当前批次号
        total_target (int): 目标总数
        current_url_index (int): 当前URL索引
        total_urls (int): 总URL数量
    '''
    try:
        # 保存已访问的论文ID
        visited_file = os.path.join(output_dir, 'visited_arxiv.txt')
        with open(visited_file, 'w', encoding='utf-8') as f:
            for vid in visited_ids:
                f.write(str(vid) + '\n')

        # 保存数学公式
        math_file = os.path.join(output_dir, 'math_arxiv.txt')
        with open(math_file, 'w', encoding='utf-8') as f:
            for formula in math_formulas:
                f.write(str(formula) + '\n')

        # 保存详细进度信息
        progress_file = os.path.join(output_dir, 'progress.json')
        progress_data = {
            'current_batch': current_batch,
            'current_url_index': current_url_index,
            'total_urls': total_urls,
            'total_papers_processed': len(visited_ids),
            'total_formulas_extracted': len(math_formulas),
            'target_papers': total_target,
            'completion_rate': len(visited_ids) / total_target if total_target > 0 else 0,
            'url_completion_rate': current_url_index / total_urls if total_urls > 0 else 0,
            'last_update': datetime.now().isoformat(),
            'config_snapshot': {
                'primary_category': BULK_CONFIG['primary_category'],
                'enable_subcategories': BULK_CONFIG['enable_subcategories'],
                'monthly_search': BULK_CONFIG['monthly_search'],
                'search_years': BULK_CONFIG['search_years'][:5]  # 只保存前5年
            }
        }

        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, indent=2, ensure_ascii=False)

        if CONFIG['verbose']:
            print(f"进度已保存: {len(visited_ids)}/{total_target} 论文, {current_url_index}/{total_urls} URL")

    except Exception as e:
        print(f"保存进度时出错: {e}")


def load_progress(output_dir):
    '''从文件加载之前的进度信息

    Args:
        output_dir (str): 输出目录

    Returns:
        dict: 进度信息字典，如果没有找到则返回默认值
    '''
    progress_file = os.path.join(output_dir, 'progress.json')

    if not os.path.exists(progress_file):
        return {
            'current_batch': 0,
            'current_url_index': 0,
            'total_urls': 0,
            'total_papers_processed': 0,
            'total_formulas_extracted': 0,
            'target_papers': 0,
            'completion_rate': 0,
            'url_completion_rate': 0,
            'last_update': None,
            'config_snapshot': {}
        }

    try:
        with open(progress_file, 'r', encoding='utf-8') as f:
            progress_data = json.load(f)

        print(f"发现之前的进度: {progress_data['total_papers_processed']} 篇论文已处理")
        print(f"上次更新时间: {progress_data.get('last_update', '未知')}")

        return progress_data

    except Exception as e:
        print(f"加载进度文件时出错: {e}")
        return {
            'current_batch': 0,
            'current_url_index': 0,
            'total_urls': 0,
            'total_papers_processed': 0,
            'total_formulas_extracted': 0,
            'target_papers': 0,
            'completion_rate': 0,
            'url_completion_rate': 0,
            'last_update': None,
            'config_snapshot': {}
        }


def reset_progress(output_dir):
    '''重置下载进度，删除所有进度文件

    Args:
        output_dir (str): 输出目录
    '''
    files_to_remove = [
        'progress.json',
        'visited_arxiv.txt',
        'math_arxiv.txt'
    ]

    removed_count = 0
    for filename in files_to_remove:
        filepath = os.path.join(output_dir, filename)
        if os.path.exists(filepath):
            try:
                os.remove(filepath)
                removed_count += 1
                print(f"已删除: {filename}")
            except Exception as e:
                print(f"删除 {filename} 时出错: {e}")

    if removed_count > 0:
        print(f"进度重置完成，删除了 {removed_count} 个文件。")
    else:
        print("没有找到需要删除的进度文件。")


def bulk_download_papers():
    '''大批量下载论文的主函数，支持断点续传'''
    print("=== 大批量论文下载模式 ===")
    print(f"目标论文数量: {BULK_CONFIG['target_paper_count']}")
    print(f"主要分类: {BULK_CONFIG['primary_category']}")
    print(f"输出目录: {CONFIG['output_dir']}")

    # 确保输出目录存在
    os.makedirs(CONFIG['output_dir'], exist_ok=True)

    # 加载之前的进度
    progress_info = load_progress(CONFIG['output_dir'])

    # 读取已处理的论文ID (用于去重和断点续传)
    visited_file = os.path.join(CONFIG['output_dir'], 'visited_arxiv.txt')
    if os.path.exists(visited_file):
        with open(visited_file, 'r', encoding='utf-8') as f:
            visited_ids = [line.strip() for line in f if line.strip()]
    else:
        visited_ids = []

    # 读取已提取的数学公式
    math_file = os.path.join(CONFIG['output_dir'], 'math_arxiv.txt')
    if os.path.exists(math_file):
        with open(math_file, 'r', encoding='utf-8') as f:
            math_formulas = [line.strip() for line in f if line.strip()]
    else:
        math_formulas = []

    print(f"已处理论文: {len(visited_ids)} 篇")
    print(f"已提取公式: {len(math_formulas)} 个")

    if len(visited_ids) >= BULK_CONFIG['target_paper_count']:
        print("已达到目标论文数量！")
        return

    # 构建单分类深度搜索URL列表
    urls = build_single_category_urls(
        BULK_CONFIG['primary_category'],
        BULK_CONFIG['search_years'],
        BULK_CONFIG['batch_size'],
        BULK_CONFIG['enable_subcategories'],
        BULK_CONFIG['monthly_search']
    )

    print(f"计划搜索 {len(urls)} 个URL")
    print(f"搜索策略: {'子分类+' if BULK_CONFIG['enable_subcategories'] else ''}{'按月' if BULK_CONFIG['monthly_search'] else '按年'}搜索")

    # 断点续传：从上次中断的地方继续
    start_url_index = progress_info.get('current_url_index', 0)
    if start_url_index > 0 and start_url_index < len(urls):
        print(f"检测到断点续传，从第 {start_url_index + 1} 个URL继续...")
        urls = urls[start_url_index:]
    elif start_url_index >= len(urls):
        print(f"所有URL已处理完毕，但未达到目标论文数量。")
        print(f"建议：增加搜索年份或调整搜索策略。")

        # 重置搜索，扩展年份范围
        print("正在扩展搜索年份范围...")
        extended_years = list(range(2009, 1999, -1))  # 2009-2000年
        BULK_CONFIG['search_years'].extend(extended_years)

        # 重新构建URL列表
        urls = build_single_category_urls(
            BULK_CONFIG['primary_category'],
            extended_years,
            BULK_CONFIG['batch_size'],
            BULK_CONFIG['enable_subcategories'],
            BULK_CONFIG['monthly_search']
        )

        print(f"扩展后新增 {len(urls)} 个URL")
        start_url_index = 0  # 从新的URL开始

    batch_count = progress_info.get('current_batch', 0)
    all_paper_ids = set(visited_ids)  # 用于去重

    total_original_urls = len(urls) + start_url_index  # 原始总URL数量

    try:
        for url_index, (category, time_range, url) in enumerate(urls):
            current_url_index = start_url_index + url_index

            if len(visited_ids) >= BULK_CONFIG['target_paper_count']:
                print(f"已达到目标论文数量 {BULK_CONFIG['target_paper_count']} 篇！")
                break

            print(f"\n--- 批次 {batch_count + 1}: {category} - {time_range} (URL {current_url_index + 1}/{total_original_urls}) ---")

            # 获取论文ID列表，带重试机制
            paper_ids = get_arxiv_ids_from_url(url, BULK_CONFIG['max_retries'])

            if not paper_ids:
                print("未获取到论文ID，跳过此批次")
                # 即使跳过也要保存进度
                save_progress(
                    CONFIG['output_dir'],
                    visited_ids,
                    math_formulas,
                    batch_count,
                    BULK_CONFIG['target_paper_count'],
                    current_url_index + 1,
                    total_original_urls
                )
                continue

            # 去重处理
            if BULK_CONFIG['enable_deduplication']:
                new_paper_ids = [pid for pid in paper_ids if pid not in all_paper_ids]
                print(f"去重后新论文: {len(new_paper_ids)} 篇 (原始: {len(paper_ids)} 篇)")
                paper_ids = new_paper_ids

            if not paper_ids:
                print("去重后无新论文，跳过此批次")
                # 即使跳过也要保存进度
                save_progress(
                    CONFIG['output_dir'],
                    visited_ids,
                    math_formulas,
                    batch_count,
                    BULK_CONFIG['target_paper_count'],
                    current_url_index + 1,
                    total_original_urls
                )
                continue

            # 处理每篇论文
            batch_math = []
            batch_visited = []

            for i, paper_id in enumerate(paper_ids):
                if len(visited_ids) >= BULK_CONFIG['target_paper_count']:
                    break

                try:
                    print(f"处理论文 {i+1}/{len(paper_ids)}: {paper_id}")

                    # 遵守API速率限制
                    time.sleep(BULK_CONFIG['download_delay'])

                    # 解析论文并提取公式，使用改进的下载函数
                    formulas, _ = parse_arxiv(
                        paper_id,
                        save=CONFIG['save_papers'],
                        demacro=CONFIG['use_demacro']
                    )

                    # 收集结果
                    batch_math.extend(formulas)
                    batch_visited.append(paper_id)
                    all_paper_ids.add(paper_id)

                    if CONFIG['verbose']:
                        print(f"  提取到 {len(formulas)} 个公式")

                except Exception as e:
                    if CONFIG['verbose']:
                        print(f"  处理论文 {paper_id} 时出错: {e}")
                    continue

            # 更新总计数器
            visited_ids.extend(batch_visited)
            math_formulas.extend(batch_math)

            print(f"批次完成: 处理 {len(batch_visited)} 篇论文，提取 {len(batch_math)} 个公式")
            print(f"总进度: {len(visited_ids)}/{BULK_CONFIG['target_paper_count']} 篇论文 ({len(visited_ids)/BULK_CONFIG['target_paper_count']*100:.1f}%)")

            # 保存进度 (每个批次都保存)
            save_progress(
                CONFIG['output_dir'],
                visited_ids,
                math_formulas,
                batch_count + 1,
                BULK_CONFIG['target_paper_count'],
                current_url_index + 1,
                total_original_urls
            )

            batch_count += 1

            # 批次间延迟
            if url_index < len(urls) - 1:  # 不是最后一个URL
                print(f"等待 {BULK_CONFIG['batch_delay']} 秒后继续下一批次...")
                time.sleep(BULK_CONFIG['batch_delay'])

    except KeyboardInterrupt:
        print("\n用户中断操作，正在保存当前进度...")
        save_progress(
            CONFIG['output_dir'],
            visited_ids,
            math_formulas,
            batch_count,
            BULK_CONFIG['target_paper_count'],
            current_url_index if 'current_url_index' in locals() else start_url_index,
            total_original_urls
        )
        print("进度已保存，下次运行将从中断处继续。")
    except Exception as e:
        print(f"\n发生异常: {e}")
        print("正在保存当前进度...")
        save_progress(
            CONFIG['output_dir'],
            visited_ids,
            math_formulas,
            batch_count,
            BULK_CONFIG['target_paper_count'],
            current_url_index if 'current_url_index' in locals() else start_url_index,
            total_original_urls
        )
        print("进度已保存。")
        raise

    # 最终保存
    save_progress(
        CONFIG['output_dir'],
        visited_ids,
        math_formulas,
        batch_count,
        BULK_CONFIG['target_paper_count'],
        total_original_urls,  # 所有URL都已处理
        total_original_urls
    )

    print(f"\n=== 批量下载完成 ===")
    print(f"总共处理: {len(visited_ids)} 篇论文")
    print(f"提取公式: {len(math_formulas)} 个")
    print(f"完成率: {len(visited_ids)/BULK_CONFIG['target_paper_count']*100:.1f}%")
    print(f"结果保存在: {CONFIG['output_dir']}")

    if len(visited_ids) >= BULK_CONFIG['target_paper_count']:
        print("🎉 恭喜！已达到目标论文数量！")
    else:
        remaining = BULK_CONFIG['target_paper_count'] - len(visited_ids)
        print(f"还需要 {remaining} 篇论文才能达到目标。")
        print("提示: 可以尝试启用更多搜索年份或子分类来获取更多论文。")


def main_with_config():
    """使用配置区域的参数运行程序"""
    print("=== ArXiv 数学公式提取工具 ===")
    print(f"当前模式: {CONFIG['mode']}")
    print(f"输出目录: {CONFIG['output_dir']}")

    # 确保输出目录存在
    os.makedirs(CONFIG['output_dir'], exist_ok=True)

    # 如果是批量下载模式，使用专门的函数
    if CONFIG['mode'] == 'bulk':
        bulk_download_papers()
        return

    # 设置已访问记录文件路径
    skips = os.path.join(CONFIG['output_dir'], 'visited_arxiv.txt')
    if os.path.exists(skips):
        # 读取已处理过的论文ID，避免重复处理
        skip = open(skips, 'r', encoding='utf-8').read().split('\n')
    else:
        skip = []

    # 如果指定了保存目录，确保目录存在
    if CONFIG['save_papers'] is not None:
        os.makedirs(CONFIG['save_papers'], exist_ok=True)
    
    try:
        if CONFIG['mode'] == 'ids':
            # 模式1: 处理指定的arXiv ID列表
            print(f"正在处理 {len(IDS_CONFIG['arxiv_ids'])} 篇指定论文...")
            visited, math = recursive_search(
                parse_arxiv, IDS_CONFIG['arxiv_ids'], skip=skip, unit='paper', 
                save=CONFIG['save_papers'], demacro=CONFIG['use_demacro']
            )
            
        elif CONFIG['mode'] == 'top':
            # 模式2: 处理最新的N篇论文
            num = TOP_CONFIG['paper_count']
            category = TOP_CONFIG['category']
            # 构建arXiv列表页面URL
            url = f'https://arxiv.org/list/{category}/pastweek?skip=0&show={num}'
            print(f"正在获取最新的 {num} 篇 {category} 论文...")

            # 从页面HTML中提取所有论文ID
            response = requests.get(url)
            ids = get_all_arxiv_ids(response.text)
            print(f"实际获取到 {len(ids)} 篇论文")

            math, visited = [], ids

            # 逐个处理每篇论文
            for id in tqdm(ids, desc="处理论文"):
                try:
                    # 遵守API速率限制
                    time.sleep(3.5)
                    m, _ = parse_arxiv(id, save=CONFIG['save_papers'], demacro=CONFIG['use_demacro'])
                    math.extend(m)  # 收集所有数学公式
                except Exception as e:
                    if CONFIG['verbose']:
                        print(f"处理论文 {id} 时出错: {e}")
                    
        elif CONFIG['mode'] == 'dirs':
            # 模式3: 处理本地文件夹中的.tar.gz文件
            files = []
            # 收集所有指定文件夹中的文件
            for folder in DIRS_CONFIG['directories']:
                if os.path.exists(folder):
                    files.extend([os.path.join(folder, p) for p in os.listdir(folder)])
                else:
                    print(f"警告: 目录 {folder} 不存在")
            
            print(f"正在处理 {len(files)} 个本地文件...")
            math, visited = [], []
            
            # 逐个处理每个文件
            for f in tqdm(files, desc="处理文件"):
                try:
                    text = read_paper(f, delete=False, demacro=CONFIG['use_demacro'])
                    math.extend(find_math(text, wiki=False))
                    visited.append(os.path.basename(f))
                except Exception as e:
                    if CONFIG['verbose']:
                        print(f"处理文件 {f} 时出错: {e}")
        else:
            raise NotImplementedError(f"不支持的模式: {CONFIG['mode']}")
            
    except KeyboardInterrupt:
        print("\n用户中断操作")
    
    print(f'找到 {len(math)} 个数学LaTeX代码实例')
    
    # 保存结果到文件
    for l, name in zip([visited, math], ['visited_arxiv.txt', 'math_arxiv.txt']):
        f = os.path.join(CONFIG['output_dir'], name)
        if not os.path.exists(f):
            open(f, 'w').write('')
        with open(f, 'a', encoding='utf-8') as file:
            for element in l:
                file.write(str(element) + '\n')
    
    print(f"结果已保存到: {CONFIG['output_dir']}")


if __name__ == '__main__':
    # 检查是否有命令行参数，如果没有则使用配置区域
    if len(sys.argv) == 1:
        # 使用配置区域运行
        main_with_config()
    else:
        # 保持原有的命令行参数功能
        parser = argparse.ArgumentParser(description='从arXiv提取数学公式')
        parser.add_argument('-m', '--mode', default='top100', choices=['top', 'ids', 'dirs'],
                            help='提取来源选择')
        parser.add_argument(nargs='*', dest='args', default=[])
        parser.add_argument('-o', '--out', default=os.path.join(os.path.dirname(os.path.realpath(__file__)), 'data'))
        parser.add_argument('-d', '--demacro', dest='demacro', action='store_true')
        parser.add_argument('-s', '--save', default=None, type=str)
        args = parser.parse_args()
        
        # 原有的命令行逻辑...
        print("使用命令行模式，建议直接运行 python arxiv.py 使用配置区域")
