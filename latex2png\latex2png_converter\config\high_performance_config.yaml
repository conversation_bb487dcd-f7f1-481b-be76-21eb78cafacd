# LaTeX2PNG 高性能配置文件
# 专为大批量处理优化，目标：100+张/秒

# ==================== 系统路径配置 ====================
system:
  # TeX Live安装路径（重要！）
  texlive_path: "D:\\Program Files\\texlive\\2025\\bin\\windows"
  
  # Python环境路径
  python_path: "D:\\miniforge3\\envs\\formula\\python.exe"
  
  # 是否自动配置PATH环境变量
  auto_configure_path: true
  
  # 是否清理MiKTeX路径冲突
  clean_miktex_paths: true

# ==================== 输入输出配置 ====================
io:
  # 默认输入文件（可选，命令行会覆盖）
  default_input_file: "D:\\formula_ge_dataset\\arxiv_25\\math_arxiv.txt"
  
  # 默认输出目录
  default_output_dir: "D:\\formula_ge\\latex2png\\latex2png_converter\\output"
  
  # 输入文件编码
  input_encoding: "utf-8"
  
  # 是否备份现有输出
  backup_existing_output: false  # 禁用备份以提升速度

# ==================== 渲染配置 ====================
render:
  # 图像质量设置（优化为高速模式）
  dpi: 150                          # 降低DPI以提升速度
  transparent_background: false      # 背景透明（向后兼容，建议使用background配置）
  font_size: 12                     # LaTeX字体大小
  image_format: "png"               # 输出格式

  # 背景配置（新功能，优先级高于transparent_background）
  background:
    type: "solid_color"             # 使用固体背景（比透明背景更快）
    color: "white"                  # 白色背景
    opacity: 1.0                    # 背景不透明度 (0.0-1.0)
  
  # TeX Live特定配置
  use_external_latex: true          # 优先使用外部TeX Live
  fallback_to_mathtext: true        # 失败时回退到matplotlib mathtext
  
  # matplotlib配置（高性能优化）
  matplotlib_backend: "Agg"         # 非交互式后端
  figure_size: null                 # 图形尺寸 [宽, 高]，null表示自适应内容大小
  auto_size: true                   # 启用自适应尺寸（推荐）
  padding_inches: 0.02              # 减少边距以提升速度

# ==================== 性能配置（高性能优化）====================
performance:
  # 并发处理（优化配置）
  max_workers: 12                   # 减少进程数，避免过度竞争（原24→12）
  batch_size: 1000                  # 增大批次，减少进程池创建开销（原200→1000）
  
  # 超时设置
  timeout_seconds: 15               # 减少单项超时时间
  total_timeout_minutes: 120        # 增加总超时时间
  
  # 重试机制（简化以提升速度）
  retry_attempts: 1                 # 减少重试次数（原2→1）
  retry_delay_seconds: 0.5          # 减少重试间隔
  
  # 内存管理（激进优化）
  memory_limit_gb: 16               # 增加内存限制
  cleanup_interval: 500             # 增加清理间隔（原100→500）

# ==================== 输出管理 ====================
output:
  # 目录结构
  images_subdir: "images"           # 图像子目录名
  error_logs_subdir: "error_logs"   # 错误日志子目录
  reports_subdir: "reports"         # 报告子目录
  
  # 文件命名
  json_filename: "mapping.json"     # 映射文件名
  image_prefix: "arxiv25_"          # 图像文件前缀
  image_digits: 7                   # 文件编号位数（如：000001）
  
  # 报告生成（简化以提升速度）
  generate_summary: true            # 生成处理摘要
  generate_statistics: false        # 禁用统计报告以提升速度
  generate_error_analysis: false    # 禁用错误分析以提升速度
  
  # 文件管理
  compress_output: false            # 禁用压缩以提升速度
  keep_temp_files: false            # 不保留临时文件

# ==================== 预处理配置 ====================
preprocessing:
  # 规范化级别
  level1_enabled: true              # 基础规范化（推荐开启）
  level2_enabled: false             # 结构规范化（禁用以提升速度）
  level3_enabled: false             # 语义规范化（禁用以提升速度）
  
  # 清理选项
  remove_comments: true             # 移除注释
  normalize_whitespace: true        # 标准化空白字符
  fix_common_errors: true           # 修复常见错误
  
  # 验证选项（简化）
  validate_syntax: false            # 禁用语法验证以提升速度
  skip_invalid: false               # 跳过无效项目（false=尝试处理）

# ==================== 错误处理配置 ====================
error_handling:
  # 错误分类（简化）
  classify_errors: false            # 禁用错误分类以提升速度
  log_all_errors: true              # 记录所有错误
  
  # 错误阈值
  max_acceptable_error_rate: 0.2    # 提高可接受错误率（10%→20%）
  stop_on_critical_error: false     # 遇到严重错误时不停止
  
  # 错误报告（简化）
  detailed_error_logs: false        # 禁用详细错误日志以提升速度
  error_screenshots: false          # 错误截图（暂不支持）

# ==================== 日志配置 ====================
logging:
  # 日志级别：DEBUG, INFO, WARNING, ERROR
  level: "WARNING"                  # 提高日志级别，减少输出（原INFO→WARNING）
  
  # 日志输出
  console_output: true              # 控制台输出
  file_output: false                # 禁用文件输出以提升速度
  log_filename: "latex2png.log"     # 日志文件名
  
  # 日志格式（简化）
  include_timestamp: false          # 不包含时间戳
  include_level: true               # 包含日志级别
  include_module: false             # 不包含模块名

# ==================== 高级配置 ====================
advanced:
  # 实验性功能（全部禁用以提升速度）
  enable_caching: false             # 禁用结果缓存
  cache_directory: "./cache"        # 缓存目录
  
  # 性能监控（禁用）
  enable_profiling: false           # 禁用性能分析
  profile_output: "./profile.txt"   # 性能分析输出
  
  # 调试选项（全部禁用）
  debug_mode: false                 # 调试模式
  save_intermediate_files: false    # 不保存中间文件
  verbose_matplotlib: false         # matplotlib详细输出

# ==================== 高性能预设 ====================
# 高速预设（专为大批量处理优化）
speed_preset: "ultra_fast"
# 预期性能：100-200张/秒
# 适用场景：大批量数据处理，对质量要求不高
# 优化重点：速度优先，减少系统卡顿
