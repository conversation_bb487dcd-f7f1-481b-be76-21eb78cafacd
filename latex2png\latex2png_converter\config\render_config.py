"""
渲染配置管理
"""

import yaml
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional

@dataclass
class RenderConfig:
    """渲染配置"""
    dpi: int = 300
    transparent_background: bool = True
    font_family: str = "Computer Modern"
    font_size: int = 12
    image_format: str = "png"
    output_dir: str = "./output"

@dataclass
class ProcessConfig:
    """处理配置"""
    max_workers: int = 16
    batch_size: int = 2000
    timeout_seconds: int = 30
    retry_attempts: int = 2

@dataclass
class OutputConfig:
    """输出配置"""
    output_dir: str = "./output"
    images_subdir: str = "images"
    json_filename: str = "mapping.json"
    error_log_dir: str = "error_logs"

@dataclass
class PreprocessingConfig:
    """预处理配置"""
    enabled_levels: list = None
    level1_enabled: bool = True
    level2_enabled: bool = False  # 迭代1暂不启用
    level3_enabled: bool = False  # 迭代1暂不启用
    
    def __post_init__(self):
        if self.enabled_levels is None:
            self.enabled_levels = []
            if self.level1_enabled:
                self.enabled_levels.append('level1')
            if self.level2_enabled:
                self.enabled_levels.append('level2')
            if self.level3_enabled:
                self.enabled_levels.append('level3')

@dataclass
class AppConfig:
    """应用总配置"""
    render: RenderConfig
    process: ProcessConfig
    output: OutputConfig
    preprocessing: PreprocessingConfig

def load_config(config_file: Optional[str] = None) -> AppConfig:
    """加载配置文件"""
    if config_file and Path(config_file).exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            return AppConfig(
                render=RenderConfig(**config_data.get('render', {})),
                process=ProcessConfig(**config_data.get('process', {})),
                output=OutputConfig(**config_data.get('output', {})),
                preprocessing=PreprocessingConfig(**config_data.get('preprocessing', {}))
            )
        except Exception as e:
            print(f"警告：配置文件加载失败 {config_file}: {e}")
            print("使用默认配置")
    
    # 返回默认配置
    return AppConfig(
        render=RenderConfig(),
        process=ProcessConfig(),
        output=OutputConfig(),
        preprocessing=PreprocessingConfig()
    )

def save_config(config: AppConfig, config_file: str):
    """保存配置到文件"""
    config_data = {
        'render': asdict(config.render),
        'process': asdict(config.process),
        'output': asdict(config.output),
        'preprocessing': asdict(config.preprocessing)
    }

    # 确保目录存在
    config_path = Path(config_file)
    config_path.parent.mkdir(parents=True, exist_ok=True)

    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)

def create_default_config_file(config_file: str = "config/default_config.yaml"):
    """创建默认配置文件"""
    default_config = AppConfig(
        render=RenderConfig(),
        process=ProcessConfig(),
        output=OutputConfig(),
        preprocessing=PreprocessingConfig()
    )
    save_config(default_config, config_file)
    return config_file
