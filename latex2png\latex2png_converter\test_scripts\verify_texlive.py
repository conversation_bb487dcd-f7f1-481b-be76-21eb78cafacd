#!/usr/bin/env python3
"""
TeX Live安装验证脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def check_texlive_commands():
    """检查TeX Live命令是否可用"""
    print("1. 检查TeX Live命令:")
    
    commands = [
        ('latex', 'LaTeX编译器'),
        ('pdflatex', 'pdfLaTeX编译器'),
        ('xelatex', 'XeLaTeX编译器'),
        ('lualatex', 'LuaLaTeX编译器'),
        ('tlmgr', 'TeX Live包管理器')
    ]
    
    all_ok = True
    for cmd, desc in commands:
        try:
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # 检查是否是TeX Live
                output = result.stdout.lower()
                if 'tex live' in output or 'texlive' in output:
                    version_line = result.stdout.split('\n')[0]
                    print(f"   ✓ {desc}: {version_line}")
                elif 'miktex' in output:
                    print(f"   ⚠ {desc}: 检测到MiKTeX，不是TeX Live")
                    all_ok = False
                else:
                    print(f"   ? {desc}: 未知LaTeX发行版")
                    print(f"     版本信息: {result.stdout.split()[0] if result.stdout else 'N/A'}")
            else:
                print(f"   ✗ {desc}: 命令执行失败")
                all_ok = False
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            print(f"   ✗ {desc}: 未找到命令 ({e})")
            all_ok = False
    
    return all_ok

def check_texlive_packages():
    """检查TeX Live包"""
    print("\n2. 检查TeX Live包:")
    
    # 必需的包
    required_packages = [
        'amsmath',
        'amsfonts', 
        'amssymb',
        'mathtools',
        'xcolor',
        'bm',
        'type1cm',
        'inputenc',
        'fontenc'
    ]
    
    try:
        # 使用kpsewhich检查包是否存在
        all_ok = True
        for package in required_packages:
            try:
                result = subprocess.run(['kpsewhich', f'{package}.sty'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0 and result.stdout.strip():
                    print(f"   ✓ {package}: {result.stdout.strip()}")
                else:
                    print(f"   ✗ {package}: 未找到")
                    all_ok = False
            except Exception as e:
                print(f"   ? {package}: 检查失败 ({e})")
        
        return all_ok
        
    except Exception as e:
        print(f"   ✗ 包检查失败: {e}")
        return False

def check_path_configuration():
    """检查PATH配置"""
    print("\n3. 检查PATH配置:")
    
    path_entries = os.environ.get('PATH', '').split(os.pathsep)
    
    texlive_paths = [p for p in path_entries if 'texlive' in p.lower()]
    miktex_paths = [p for p in path_entries if 'miktex' in p.lower()]
    
    if texlive_paths:
        print("   ✓ 找到TeX Live路径:")
        for path in texlive_paths:
            print(f"     - {path}")
    else:
        print("   ✗ 未找到TeX Live路径")
    
    if miktex_paths:
        print("   ⚠ 发现MiKTeX路径（可能导致冲突）:")
        for path in miktex_paths:
            print(f"     - {path}")
        return False
    
    return len(texlive_paths) > 0

def test_latex_compilation():
    """测试LaTeX编译"""
    print("\n4. 测试LaTeX编译:")
    
    # 创建测试LaTeX文件
    test_latex = r"""
\documentclass{article}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\begin{document}
$x^2 + y^2 = z^2$
\end{document}
"""
    
    import tempfile
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            tex_file = Path(temp_dir) / 'test.tex'
            with open(tex_file, 'w', encoding='utf-8') as f:
                f.write(test_latex)
            
            # 编译测试
            result = subprocess.run(['pdflatex', '-interaction=nonstopmode', 
                                   '-output-directory', temp_dir, str(tex_file)],
                                  capture_output=True, text=True, timeout=30)
            
            pdf_file = Path(temp_dir) / 'test.pdf'
            if pdf_file.exists() and pdf_file.stat().st_size > 0:
                print("   ✓ LaTeX编译测试成功")
                return True
            else:
                print("   ✗ LaTeX编译测试失败")
                if result.stderr:
                    print(f"     错误信息: {result.stderr[:200]}...")
                return False
                
    except Exception as e:
        print(f"   ✗ LaTeX编译测试异常: {e}")
        return False

def test_matplotlib_integration():
    """测试matplotlib集成"""
    print("\n5. 测试matplotlib + TeX Live集成:")
    
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        
        # 配置matplotlib使用LaTeX
        matplotlib.rcParams['text.usetex'] = True
        matplotlib.rcParams['text.latex.preamble'] = r'''
        \usepackage{amsmath}
        \usepackage{amsfonts}
        \usepackage{amssymb}
        '''
        
        # 创建测试图形
        fig, ax = plt.subplots(figsize=(2, 1))
        ax.text(0.5, 0.5, r'$\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}$',
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes)
        ax.axis('off')
        
        # 保存测试
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            temp_file = f.name
        
        plt.savefig(temp_file, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        # 检查结果
        if Path(temp_file).exists() and Path(temp_file).stat().st_size > 0:
            print("   ✓ matplotlib + TeX Live集成测试成功")
            os.unlink(temp_file)
            return True
        else:
            print("   ✗ matplotlib + TeX Live集成测试失败")
            return False
            
    except Exception as e:
        print(f"   ✗ matplotlib + TeX Live集成测试失败: {e}")
        return False

def main():
    """主函数"""
    print("TeX Live安装验证")
    print("=" * 50)
    
    tests = [
        ("TeX Live命令", check_texlive_commands),
        ("TeX Live包", check_texlive_packages),
        ("PATH配置", check_path_configuration),
        ("LaTeX编译", test_latex_compilation),
        ("matplotlib集成", test_matplotlib_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ✗ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"TeX Live验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 TeX Live安装完全正确！")
        print("\n可以开始使用LaTeX2PNG转换器:")
        print("python main.py --test-engines")
        return 0
    else:
        print("❌ TeX Live安装存在问题")
        
        if passed < 3:
            print("\n建议操作:")
            print("1. 确保完全卸载MiKTeX")
            print("2. 重新安装TeX Live完整版")
            print("3. 重启系统以更新PATH")
            print("4. 清理matplotlib缓存: rm -rf ~/.matplotlib")
        
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
