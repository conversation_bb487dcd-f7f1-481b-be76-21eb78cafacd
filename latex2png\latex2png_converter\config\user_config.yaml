# LaTeX2PNG 用户配置文件
# 在这里配置所有路径和参数，避免每次手动输入

# ==================== 系统路径配置 ====================
system:
  # TeX Live安装路径（重要！）
  texlive_path: "D:\\Program Files\\texlive\\2025\\bin\\windows"
  
  # Python环境路径
  python_path: "D:\\miniforge3\\envs\\formula\\python.exe"
  
  # 是否自动配置PATH环境变量
  auto_configure_path: true
  
  # 是否清理MiKTeX路径冲突
  clean_miktex_paths: true

# ==================== 输入输出配置 ====================
io:
  # 默认输入文件（可选，命令行会覆盖）
  default_input_file: "D:\\formula_ge_dataset\\arxiv_25\\math_arxiv.txt"
  
  # 默认输出目录
  default_output_dir: "./output"
  
  # 输入文件编码
  input_encoding: "utf-8"
  
  # 是否备份现有输出
  backup_existing_output: true

# ==================== 渲染配置 ====================
render:
  # 图像质量设置
  dpi: 150                          # 标准质量：300，高质量：600，预览：150
  transparent_background: true      # 背景透明
  font_size: 12                     # LaTeX字体大小
  image_format: "png"               # 输出格式
  
  # TeX Live特定配置
  use_external_latex: true          # 优先使用外部TeX Live
  fallback_to_mathtext: true        # 失败时回退到matplotlib mathtext
  
  # matplotlib配置
  matplotlib_backend: "Agg"         # 后端设置
  figure_size: [8, 2]               # 图形尺寸 [宽, 高]
  padding_inches: 0.1               # 边距

# ==================== 性能配置 ====================
performance:
  # 并发处理
  max_workers: 24                   # 最大并发进程数（优化为20核28线程的1.2倍）
  batch_size: 2000                  # 批次大小（内存足够可以增大）
  
  # 超时设置
  timeout_seconds: 30               # 单项处理超时
  total_timeout_minutes: 60         # 总处理超时
  
  # 重试机制
  retry_attempts: 2                 # 失败重试次数
  retry_delay_seconds: 1            # 重试间隔
  
  # 内存管理
  memory_limit_gb: 8                # 内存使用限制
  cleanup_interval: 100             # 清理间隔（处理多少项后清理）

# ==================== 输出管理 ====================
output:
  # 目录结构
  images_subdir: "images"           # 图像子目录名
  error_logs_subdir: "error_logs"   # 错误日志子目录
  reports_subdir: "reports"         # 报告子目录
  
  # 文件命名
  json_filename: "mapping.json"     # 映射文件名
  image_prefix: "formula_"          # 图像文件前缀
  image_digits: 6                   # 文件编号位数（如：000001）
  
  # 报告生成
  generate_summary: true            # 生成处理摘要
  generate_statistics: true         # 生成统计报告
  generate_error_analysis: true     # 生成错误分析
  
  # 文件管理
  compress_output: false            # 是否压缩输出
  keep_temp_files: false            # 是否保留临时文件

# ==================== 预处理配置 ====================
preprocessing:
  # 规范化级别
  level1_enabled: true              # 基础规范化（推荐开启）
  level2_enabled: false             # 结构规范化（实验性）
  level3_enabled: false             # 语义规范化（实验性）
  
  # 清理选项
  remove_comments: true             # 移除注释
  normalize_whitespace: true        # 标准化空白字符
  fix_common_errors: true           # 修复常见错误
  
  # 验证选项
  validate_syntax: true             # 语法验证
  skip_invalid: false               # 跳过无效项目（false=尝试处理）

# ==================== 错误处理配置 ====================
error_handling:
  # 错误分类
  classify_errors: true             # 自动分类错误
  log_all_errors: true              # 记录所有错误
  
  # 错误阈值
  max_acceptable_error_rate: 0.1    # 最大可接受错误率（10%）
  stop_on_critical_error: false     # 遇到严重错误时停止
  
  # 错误报告
  detailed_error_logs: true         # 详细错误日志
  error_screenshots: false          # 错误截图（暂不支持）

# ==================== 日志配置 ====================
logging:
  # 日志级别：DEBUG, INFO, WARNING, ERROR
  level: "INFO"                     # 标准模式：INFO，调试模式：DEBUG
  
  # 日志输出
  console_output: true              # 控制台输出
  file_output: true                 # 文件输出
  log_filename: "latex2png.log"     # 日志文件名
  
  # 日志格式
  include_timestamp: true           # 包含时间戳
  include_level: true               # 包含日志级别
  include_module: false             # 包含模块名（调试用）

# ==================== 高级配置 ====================
advanced:
  # 实验性功能
  enable_caching: false             # 启用结果缓存（实验性）
  cache_directory: "./cache"        # 缓存目录
  
  # 性能监控
  enable_profiling: false           # 启用性能分析
  profile_output: "./profile.txt"   # 性能分析输出
  
  # 调试选项
  debug_mode: false                 # 调试模式
  save_intermediate_files: false    # 保存中间文件
  verbose_matplotlib: false         # matplotlib详细输出

# ==================== 快速预设 ====================
# 取消注释以下预设之一来快速配置

# 高质量预设（慢但质量最好）
# quality_preset: "high"
# render:
#   dpi: 600
#   font_size: 14
# performance:
#   max_workers: 8
#   batch_size: 1000

# 高速预设（快但质量一般）
# speed_preset: "fast"
# render:
#   dpi: 150
#   use_external_latex: false
# performance:
#   max_workers: 32
#   batch_size: 5000

# 平衡预设（推荐）
balance_preset: "recommended"
# 使用上述默认配置
