# Simple TeX Live finder

Write-Host "Searching for TeX Live installation..." -ForegroundColor Green

# Check common paths
$paths = @(
    "C:\texlive",
    "D:\texlive", 
    "C:\texlive\2024",
    "C:\texlive\2023",
    "D:\texlive\2024",
    "D:\texlive\2023",
    "C:\Program Files\texlive",
    "D:\Program Files\texlive"
)

$found = @()

foreach ($path in $paths) {
    if (Test-Path $path) {
        Write-Host "Found: $path" -ForegroundColor Green
        $found += $path
        
        # Check for bin directory
        $binPaths = @(
            "$path\bin\windows",
            "$path\bin\win32",
            "$path\bin\x86_64-w64-mingw32"
        )
        
        foreach ($binPath in $binPaths) {
            if (Test-Path $binPath) {
                Write-Host "  Bin directory: $binPath" -ForegroundColor Cyan
                
                # Check for executables
                $exes = @("latex.exe", "pdflatex.exe", "tlmgr.exe")
                foreach ($exe in $exes) {
                    $exePath = Join-Path $binPath $exe
                    if (Test-Path $exePath) {
                        Write-Host "    Found: $exe" -ForegroundColor Green
                    }
                }
            }
        }
    }
}

if ($found.Count -eq 0) {
    Write-Host "No TeX Live installation found in common locations" -ForegroundColor Red
    Write-Host "Checking if TeX Live is in PATH..." -ForegroundColor Yellow
    
    $latexCmd = Get-Command latex -ErrorAction SilentlyContinue
    if ($latexCmd) {
        Write-Host "Found latex command in PATH: $($latexCmd.Source)" -ForegroundColor Green
    } else {
        Write-Host "No latex command found in PATH" -ForegroundColor Red
    }
} else {
    Write-Host "`nFound $($found.Count) TeX Live installation(s)" -ForegroundColor Green
    Write-Host "To use TeX Live, add the bin directory to PATH or run:" -ForegroundColor Yellow
    Write-Host ".\setup_texlive_env.ps1 -TexLivePath 'path_to_texlive'" -ForegroundColor White
}
