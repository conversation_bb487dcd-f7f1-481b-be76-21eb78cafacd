#!/usr/bin/env python3
"""
快速测试脚本 - 测试核心功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_engine_initialization():
    """测试引擎初始化"""
    print("测试引擎初始化...")
    
    try:
        from config.render_config import RenderConfig
        from engines.texlive_engine import TeXLiveEngine
        
        # 创建配置
        config = RenderConfig()
        print(f"  ✓ 配置创建成功: DPI={config.dpi}")
        
        # 创建引擎
        engine = TeXLiveEngine(config)
        print("  ✓ TeX Live引擎创建成功")
        
        # 检查可用性
        is_available = engine.is_available()
        print(f"  ✓ 引擎可用性: {is_available}")
        
        # 初始化引擎
        if is_available:
            success = engine.initialize()
            print(f"  ✓ 引擎初始化: {success}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ 引擎测试失败: {e}")
        return False

def test_latex_processing():
    """测试LaTeX处理"""
    print("\n测试LaTeX处理...")
    
    try:
        from config.render_config import PreprocessingConfig
        from core.latex_processor import LaTeXProcessor
        
        # 创建处理器
        config = PreprocessingConfig()
        processor = LaTeXProcessor(config)
        print("  ✓ LaTeX处理器创建成功")
        
        # 测试规范化
        test_latex = r"  x^2 + y^2 = z^2  "
        normalized = processor.normalize(test_latex)
        print(f"  ✓ 规范化测试: '{test_latex}' -> '{normalized}'")
        
        return True
        
    except Exception as e:
        print(f"  ✗ LaTeX处理测试失败: {e}")
        return False

def test_simple_rendering():
    """测试简单渲染"""
    print("\n测试简单渲染...")
    
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        import tempfile
        
        # 配置matplotlib使用内置mathtext
        matplotlib.rcParams['text.usetex'] = False
        matplotlib.rcParams['mathtext.fontset'] = 'cm'
        
        # 创建测试图形
        fig, ax = plt.subplots(figsize=(3, 1))
        ax.text(0.5, 0.5, r'$\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}$',
               horizontalalignment='center',
               verticalalignment='center',
               transform=ax.transAxes,
               fontsize=14)
        ax.axis('off')
        
        # 保存测试
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            temp_file = f.name
        
        plt.savefig(temp_file, dpi=300, bbox_inches='tight', 
                   transparent=True, facecolor='none')
        plt.close(fig)
        
        # 检查结果
        if Path(temp_file).exists() and Path(temp_file).stat().st_size > 0:
            print(f"  ✓ 渲染测试成功: {temp_file}")
            print(f"  ✓ 文件大小: {Path(temp_file).stat().st_size} bytes")
            
            # 清理
            os.unlink(temp_file)
            return True
        else:
            print("  ✗ 渲染测试失败：文件未生成")
            return False
            
    except Exception as e:
        print(f"  ✗ 渲染测试失败: {e}")
        return False

def test_batch_data_structures():
    """测试批量处理数据结构"""
    print("\n测试数据结构...")
    
    try:
        from core import LaTeXItem, RenderResult, BatchResult, ProcessStatus
        
        # 创建LaTeX项目
        item = LaTeXItem(
            index=1,
            original_latex=r"x^2 + y^2 = z^2",
            normalized_latex=r"x^2 + y^2 = z^2",
            category="math"
        )
        print("  ✓ LaTeX项目创建成功")
        
        # 创建渲染结果
        result = RenderResult(
            item=item,
            status=ProcessStatus.SUCCESS,
            image_path="test.png",
            processing_time=0.5
        )
        print("  ✓ 渲染结果创建成功")
        
        # 创建批次结果
        batch_result = BatchResult(
            batch_id=1,
            total_count=1,
            success_count=1,
            failed_count=0,
            results=[result],
            processing_time=1.0
        )
        print("  ✓ 批次结果创建成功")
        
        return True
        
    except Exception as e:
        print(f"  ✗ 数据结构测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n测试文件操作...")
    
    try:
        from utils.file_utils import create_output_dirs
        from config.render_config import OutputConfig
        import tempfile
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            config = OutputConfig()
            dirs = create_output_dirs(temp_dir, config)
            
            print(f"  ✓ 输出目录创建成功: {len(dirs)} 个目录")
            for name, path in dirs.items():
                print(f"    - {name}: {path}")
        
        return True
        
    except Exception as e:
        print(f"  ✗ 文件操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("LaTeX2PNG 快速功能测试")
    print("=" * 40)
    
    tests = [
        ("引擎初始化", test_engine_initialization),
        ("LaTeX处理", test_latex_processing),
        ("简单渲染", test_simple_rendering),
        ("数据结构", test_batch_data_structures),
        ("文件操作", test_file_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 20)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心功能正常！")
        print("\n下一步可以尝试:")
        print("1. 处理示例文件:")
        print("   python quick_demo.py")
        return 0
    else:
        print("❌ 部分功能存在问题")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
