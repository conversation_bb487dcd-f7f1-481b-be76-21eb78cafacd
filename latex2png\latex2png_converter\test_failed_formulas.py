#!/usr/bin/env python3
"""
测试失败公式的改善效果
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config_loader import load_user_config, setup_environment_from_config

def test_failed_formulas():
    """测试失败公式文件"""
    print("测试失败公式改善效果")
    print("=" * 50)
    
    # 检查失败公式文件
    failed_file = Path("output/error_logs/failed_formulas.txt")
    if not failed_file.exists():
        print("❌ 失败公式文件不存在")
        print("请先运行主程序生成失败公式文件")
        return 1
    
    # 读取失败公式
    with open(failed_file, 'r', encoding='utf-8') as f:
        failed_formulas = [line.strip() for line in f if line.strip()]
    
    print(f"找到 {len(failed_formulas)} 个失败公式")
    
    if len(failed_formulas) == 0:
        print("🎉 没有失败公式！成功率100%")
        return 0
    
    # 显示失败公式
    print("\n失败公式列表:")
    print("-" * 30)
    for i, formula in enumerate(failed_formulas, 1):
        print(f"{i}. {formula[:80]}...")
    
    # 提供测试选项
    print(f"\n测试选项:")
    print(f"1. 使用失败公式文件作为输入重新测试")
    print(f"2. 修改配置后重新测试")
    print(f"3. 查看详细错误信息")
    
    print(f"\n命令示例:")
    print(f"# 重新测试失败公式")
    print(f'python run_with_config.py --input "{failed_file}"')
    
    print(f"\n# 修改配置文件后重新测试全部")
    print(f"python run_with_config.py --verbose")
    
    return 0

if __name__ == "__main__":
    exit_code = test_failed_formulas()
    sys.exit(exit_code)
