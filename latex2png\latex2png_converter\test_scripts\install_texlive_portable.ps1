# TeX Live便携版安装脚本
# 不需要重启，适合当前情况

param(
    [string]$InstallPath = "D:\texlive_portable"
)

Write-Host "TeX Live便携版安装脚本" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host "安装路径: $InstallPath" -ForegroundColor Yellow

# 检查网络连接
Write-Host "`n1. 检查网络连接..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "https://www.tug.org" -UseBasicParsing -TimeoutSec 10
    Write-Host "   ✓ 网络连接正常" -ForegroundColor Green
} catch {
    Write-Host "   ✗ 网络连接失败，请检查网络" -ForegroundColor Red
    exit 1
}

# 创建安装目录
Write-Host "`n2. 创建安装目录..." -ForegroundColor Yellow
if (!(Test-Path $InstallPath)) {
    New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    Write-Host "   ✓ 创建目录: $InstallPath" -ForegroundColor Green
} else {
    Write-Host "   ✓ 目录已存在: $InstallPath" -ForegroundColor Green
}

# 下载TeX Live安装器
Write-Host "`n3. 下载TeX Live安装器..." -ForegroundColor Yellow
$installerUrl = "https://mirror.ctan.org/systems/texlive/tlnet/install-tl-windows.exe"
$installerPath = Join-Path $InstallPath "install-tl-windows.exe"

try {
    Write-Host "   正在下载安装器..." -ForegroundColor Cyan
    Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath -UseBasicParsing
    Write-Host "   ✓ 安装器下载完成" -ForegroundColor Green
} catch {
    Write-Host "   ✗ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 创建安装配置文件
Write-Host "`n4. 创建安装配置..." -ForegroundColor Yellow
$configContent = @"
# TeX Live安装配置文件
selected_scheme scheme-basic
TEXDIR $InstallPath\texlive
TEXMFLOCAL $InstallPath\texlive\texmf-local
TEXMFSYSCONFIG $InstallPath\texlive\texmf-config
TEXMFSYSVAR $InstallPath\texlive\texmf-var
TEXMFHOME $InstallPath\texlive\texmf-home
option_doc 0
option_src 0
option_adjustpath 0
option_autobackup 0
option_desktop_integration 0
option_file_assocs 0
option_menu_integration 0
instopt_letter 0
tlpdbopt_autobackup 0
tlpdbopt_create_formats 1
tlpdbopt_generate_updmap 1
tlpdbopt_install_docfiles 0
tlpdbopt_install_srcfiles 0
tlpdbopt_post_code 1
"@

$configPath = Join-Path $InstallPath "texlive.profile"
$configContent | Out-File -FilePath $configPath -Encoding UTF8
Write-Host "   ✓ 配置文件创建完成" -ForegroundColor Green

Write-Host "`n5. 安装说明:" -ForegroundColor Yellow
Write-Host "   由于这是便携版安装，需要手动执行以下步骤:" -ForegroundColor Cyan
Write-Host "   1. 运行: $installerPath" -ForegroundColor White
Write-Host "   2. 选择 'Advanced' 模式" -ForegroundColor White
Write-Host "   3. 设置安装路径为: $InstallPath\texlive" -ForegroundColor White
Write-Host "   4. 选择 'Basic scheme' (最小安装)" -ForegroundColor White
Write-Host "   5. 取消勾选 'Adjust PATH'" -ForegroundColor White
Write-Host "   6. 开始安装" -ForegroundColor White

Write-Host "`n6. 或者使用自动安装:" -ForegroundColor Yellow
$autoInstall = Read-Host "   是否要尝试自动安装? (y/N)"

if ($autoInstall -eq 'y' -or $autoInstall -eq 'Y') {
    Write-Host "   正在启动自动安装..." -ForegroundColor Cyan
    try {
        Start-Process -FilePath $installerPath -ArgumentList "-profile", $configPath -Wait -NoNewWindow
        Write-Host "   ✓ 安装完成" -ForegroundColor Green
    } catch {
        Write-Host "   ✗ 自动安装失败，请手动安装" -ForegroundColor Red
        Write-Host "   手动运行: $installerPath" -ForegroundColor Yellow
    }
}

Write-Host "`n安装完成后，请运行以下命令设置环境:" -ForegroundColor Green
Write-Host "   .\setup_texlive_env.ps1 -TexLivePath '$InstallPath\texlive'" -ForegroundColor White
