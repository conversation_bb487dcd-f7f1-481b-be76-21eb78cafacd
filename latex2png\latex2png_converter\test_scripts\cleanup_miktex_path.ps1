# MiKTeX PATH清理脚本
# 需要以管理员身份运行

Write-Host "MiKTeX PATH清理脚本" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green

# 检查是否以管理员身份运行
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误: 此脚本需要以管理员身份运行" -ForegroundColor Red
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    pause
    exit 1
}

# 获取当前系统PATH
$currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
$pathEntries = $currentPath -split ';'

Write-Host "`n当前PATH中的MiKTeX条目:" -ForegroundColor Yellow
$miktexEntries = $pathEntries | Where-Object { $_ -like '*miktex*' }

if ($miktexEntries.Count -eq 0) {
    Write-Host "  未找到MiKTeX相关的PATH条目" -ForegroundColor Green
} else {
    foreach ($entry in $miktexEntries) {
        Write-Host "  - $entry" -ForegroundColor Red
    }
    
    # 询问是否删除
    $response = Read-Host "`n是否要从系统PATH中删除这些MiKTeX条目? (y/N)"
    
    if ($response -eq 'y' -or $response -eq 'Y') {
        # 过滤掉MiKTeX条目
        $cleanedEntries = $pathEntries | Where-Object { $_ -notlike '*miktex*' -and $_ -ne '' }
        $newPath = $cleanedEntries -join ';'
        
        try {
            # 更新系统PATH
            [Environment]::SetEnvironmentVariable("Path", $newPath, "Machine")
            Write-Host "`n✓ 成功从系统PATH中删除MiKTeX条目" -ForegroundColor Green
            
            # 检查用户PATH
            $userPath = [Environment]::GetEnvironmentVariable("Path", "User")
            if ($userPath -like '*miktex*') {
                $userEntries = $userPath -split ';'
                $cleanedUserEntries = $userEntries | Where-Object { $_ -notlike '*miktex*' -and $_ -ne '' }
                $newUserPath = $cleanedUserEntries -join ';'
                [Environment]::SetEnvironmentVariable("Path", $newUserPath, "User")
                Write-Host "✓ 同时清理了用户PATH中的MiKTeX条目" -ForegroundColor Green
            }
            
            Write-Host "`n重要提示:" -ForegroundColor Yellow
            Write-Host "1. 请重启计算机以使PATH更改生效" -ForegroundColor Yellow
            Write-Host "2. 重启后安装TeX Live" -ForegroundColor Yellow
            Write-Host "3. 确保TeX Live安装时选择'添加到PATH'" -ForegroundColor Yellow
            
        } catch {
            Write-Host "`n✗ 更新PATH失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "操作已取消" -ForegroundColor Yellow
    }
}

# 检查MiKTeX进程
Write-Host "`n检查正在运行的MiKTeX进程:" -ForegroundColor Yellow
$miktexProcesses = Get-Process | Where-Object { $_.ProcessName -like '*miktex*' -or $_.ProcessName -like '*tex*' }

if ($miktexProcesses.Count -eq 0) {
    Write-Host "  未找到正在运行的MiKTeX进程" -ForegroundColor Green
} else {
    Write-Host "  发现以下相关进程:" -ForegroundColor Red
    foreach ($proc in $miktexProcesses) {
        Write-Host "    - $($proc.ProcessName) (PID: $($proc.Id))" -ForegroundColor Red
    }
    
    $killResponse = Read-Host "`n是否要终止这些进程? (y/N)"
    if ($killResponse -eq 'y' -or $killResponse -eq 'Y') {
        foreach ($proc in $miktexProcesses) {
            try {
                Stop-Process -Id $proc.Id -Force
                Write-Host "  ✓ 已终止进程: $($proc.ProcessName)" -ForegroundColor Green
            } catch {
                Write-Host "  ✗ 无法终止进程: $($proc.ProcessName)" -ForegroundColor Red
            }
        }
    }
}

Write-Host "`n脚本执行完成!" -ForegroundColor Green
pause
